{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pandas import DataFrame, read_excel\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.metrics import confusion_matrix\n", "#IMPORT MODELS\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.ensemble import AdaBoostClassifier\n", "from sklearn import svm\n", "from sklearn.metrics import roc_auc_score\n", "from sklearn.metrics import accuracy_score\n", "from sklearn.metrics import fbeta_score\n", "from sklearn.metrics import recall_score\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.decomposition import PCA\n", "import seaborn as sns\n", "from sklearn.metrics import recall_score\n", "from sklearn.metrics import f1_score\n", "from sklearn.metrics import precision_score\n", "from sklearn.metrics import accuracy_score\n", "\n", "import matplotlib.pyplot as plt\n", "import itertools\n", "def plot_confusion_matrix(cm, classes,\n", "                          title='Confusion matrix',\n", "                          cmap=plt.cm.binary):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    \"\"\"\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "    plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=0)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        plt.text(j, i, cm[i, j],\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "\n", "    plt.tight_layout()\n", "    plt.ylabel('True label')\n", "    plt.xlabel('Predicted label')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loading data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["#read training and test data\n", "p4jtraining = pd.DataFrame()\n", "addtraining = pd.DataFrame()\n", "s4rtraining = pd.DataFrame()\n", "p4jtest = pd.DataFrame()\n", "addtest = pd.DataFrame()\n", "s4rtest = pd.DataFrame()\n", "\n", "P4J_training_list= [\"./data/CodeTrain10302.csv\"]\n", "ADD_training_list= [\"./data/PatternsTrain10302.csv\"]\n", "S4R_training_list= [\"./data/ContextTrain10302.csv\"]\n", "\n", "P4J_testing_list= [\"./data/CodePatchSim.csv\"]\n", "ADD_testing_list= [\"./data/PatternsPatchSim.csv\"]\n", "S4R_testing_list= [\"./data/ContextPatchSim.csv\"]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(10302, 204)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["for f in P4J_training_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    p4jtraining = p4jtraining.append(data, ignore_index=True)\n", "    \n", "for f in ADD_training_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    addtraining = addtraining.append(data, ignore_index=True)\n", "    \n", "    \n", "for f in S4R_training_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    s4rtraining = s4rtraining.append(data, ignore_index=True)    \n", "    \n", "    \n", "p4jtraining=p4jtraining.iloc[:,:152]\n", "addtraining=addtraining.iloc[:,:28]\n", "s4rtest=s4rtest.iloc[:,:28]\n", "p4jtraining=p4jtraining.drop(columns='label')\n", "s4rtraining=s4rtraining.drop(columns='label')\n", "totaltraining = addtraining.merge(p4jtraining, on=\"id\").merge(s4rtraining, on=\"id\")  \n", "totaltraining.shape"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(139, 157)\n", "(139, 29)\n", "(139, 28)\n", "p4jtest:  (139, 151)\n", "addtest:  (139, 28)\n", "s4rtest:  (139, 27)\n"]}, {"data": {"text/plain": ["(139, 204)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["for f in P4J_testing_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    p4jtest = p4jtest.append(data, ignore_index=True)\n", "    \n", "for f in ADD_testing_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    addtest = addtest.append(data, ignore_index=True)\n", "    \n", "for f in S4R_testing_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    s4rtest = s4rtest.append(data, ignore_index=True)    \n", "    \n", "    \n", "print(p4jtest.shape)\n", "print(addtest.shape)\n", "print(s4rtest.shape)\n", "\n", "\n", "p4jtest=p4jtest.iloc[:,:152]\n", "addtest=addtest.iloc[:,:28]\n", "s4rtest=s4rtest.iloc[:,:28]\n", "p4jtest=p4jtest.drop(columns='label')\n", "s4rtest=s4rtest.drop(columns='label')\n", "print('p4jtest: ',p4jtest.shape)\n", "print('addtest: ',addtest.shape)\n", "print('s4rtest: ',s4rtest.shape)\n", "totaltest = addtest.merge(p4jtest, on=\"id\").merge(s4rtest, on=\"id\")  \n", "totaltest.shape"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(26, 204)\n", "(8955, 204)\n"]}], "source": ["test =  totaltest[ totaltest['id'].str.contains(r'Chart')  ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Chart') ]\n", "print(test.shape)\n", "print(training.shape)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (8955, 204)\n", "overfitting in train (6977, 204)\n", "correct in train (1978, 204)\n", "====================\n", "====================\n", "Total test:  (26, 204)\n", "overfitting in test (23, 204)\n", "correct in test (3, 204)\n"]}], "source": ["#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about validation samples\n", "# overfittinginValidation = validation[validation['label']==1]\n", "# correctinValidation=validation[validation['label']==0]\n", "# print(\"Total validation: \", validation.shape)\n", "# print(\"overfitting in train\",overfittinginValidation.shape)\n", "# print(\"correct in train\",correctinValidation.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>label</th>\n", "      <th>wrapsLoop</th>\n", "      <th>wrapsTryCatch</th>\n", "      <th>wrapsIfElse</th>\n", "      <th>wrongMethodRef</th>\n", "      <th>constChange</th>\n", "      <th>unwrapIfElse</th>\n", "      <th>unwrapTryCatch</th>\n", "      <th>expArithMod</th>\n", "      <th>...</th>\n", "      <th>similarObjectTypeWithNullGuard</th>\n", "      <th>typeOfFaultyStmt</th>\n", "      <th>fieldNotAssigned</th>\n", "      <th>fieldNotUsed</th>\n", "      <th>similarPrimitiveTypeWithNormalGuard</th>\n", "      <th>similarPrimitiveTypeWithNullGuard</th>\n", "      <th>methodThrowsException</th>\n", "      <th>objectUsedInAssignment</th>\n", "      <th>primitiveUsedInAssignment</th>\n", "      <th>methodCallWithNormalGuard</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5498</th>\n", "      <td>Closure_163</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>While</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506</th>\n", "      <td>Closure_103</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>Switch</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5519</th>\n", "      <td>Jsou_52</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>If</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5556</th>\n", "      <td>Compress_29</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>Field</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5562</th>\n", "      <td>Math_78</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>If</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2168</th>\n", "      <td>GenProg_patch_Defects4J_Math_8_0_240</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>LocalVariable</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2620</th>\n", "      <td>GenProg_patch_Defects4J_Math_50_0_83</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>While</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2944</th>\n", "      <td><PERSON><PERSON><PERSON>_patch_Defects4J_Math_81_0_321</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>Assignment</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3708</th>\n", "      <td><PERSON><PERSON><PERSON>_patch_Defects4J_Math_81_0_136</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>If</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3901</th>\n", "      <td><PERSON><PERSON><PERSON>_patch_Defects4J_Math_81_0_444</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>If</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>847 rows × 204 columns</p>\n", "</div>"], "text/plain": ["                                        id  label  wrapsLoop  wrapsTryCatch  \\\n", "5498                           Closure_163      0        0.0            0.0   \n", "5506                           Closure_103      0        0.0            0.0   \n", "5519                               Jsou_52      0        0.0            0.0   \n", "5556                           Compress_29      0        0.0            0.0   \n", "5562                               Math_78      0        0.0            0.0   \n", "...                                    ...    ...        ...            ...   \n", "2168  GenProg_patch_Defects4J_Math_8_0_240      1        0.0            0.0   \n", "2620  GenProg_patch_Defects4J_Math_50_0_83      1        0.0            0.0   \n", "2944    <PERSON><PERSON><PERSON>_patch_Defects4J_Math_81_0_321      1        0.0            0.0   \n", "3708    <PERSON><PERSON><PERSON>_patch_Defects4J_Math_81_0_136      1        0.0            0.0   \n", "3901    A<PERSON><PERSON>_patch_Defects4J_Math_81_0_444      1        0.0            0.0   \n", "\n", "      wrapsIfElse  wrongMethodRef  constChange  unwrapIfElse  unwrapTryCatch  \\\n", "5498          0.0            11.0          1.0           0.0             0.0   \n", "5506          0.0             0.0          0.0           0.0             0.0   \n", "5519          0.0             2.0          0.0           0.0             0.0   \n", "5556          0.0             0.0          0.0           0.0             0.0   \n", "5562          0.0             0.0          0.0           0.0             0.0   \n", "...           ...             ...          ...           ...             ...   \n", "2168          0.0             0.0          0.0           0.0             0.0   \n", "2620          0.0             0.0          0.0           0.0             0.0   \n", "2944          0.0             0.0          0.0           0.0             0.0   \n", "3708          0.0             0.0          0.0           0.0             0.0   \n", "3901          0.0             0.0          0.0           0.0             0.0   \n", "\n", "      expArithMod  ...  similarObjectTypeWithNullGuard  typeOfFaultyStmt  \\\n", "5498          0.0  ...                               0             While   \n", "5506          0.0  ...                               0            Switch   \n", "5519          0.0  ...                               0                If   \n", "5556          0.0  ...                               0             Field   \n", "5562          0.0  ...                               0                If   \n", "...           ...  ...                             ...               ...   \n", "2168          0.0  ...                               0     LocalVariable   \n", "2620          0.0  ...                               0             While   \n", "2944          0.0  ...                               0        Assignment   \n", "3708          0.0  ...                               0                If   \n", "3901          0.0  ...                               0                If   \n", "\n", "      fieldNotAssigned  fieldNotUsed  similarPrimitiveTypeWithNormalGuard  \\\n", "5498                 0             0                                    0   \n", "5506                 0             0                                    0   \n", "5519                 0             0                                    0   \n", "5556                 0             0                                    0   \n", "5562                 0             0                                    0   \n", "...                ...           ...                                  ...   \n", "2168                 0             0                                    0   \n", "2620                 0             0                                    0   \n", "2944                 0             0                                    0   \n", "3708                 0             0                                    0   \n", "3901                 0             0                                    0   \n", "\n", "      similarPrimitiveTypeWithNullGuard  methodThrowsException  \\\n", "5498                                  0                      0   \n", "5506                                  0                      0   \n", "5519                                  0                      0   \n", "5556                                  0                      0   \n", "5562                                  0                      0   \n", "...                                 ...                    ...   \n", "2168                                  0                      1   \n", "2620                                  0                      0   \n", "2944                                  0                      0   \n", "3708                                  0                      0   \n", "3901                                  0                      0   \n", "\n", "      objectUsedInAssignment  primitiveUsedInAssignment  \\\n", "5498                       0                        0.0   \n", "5506                       0                        0.0   \n", "5519                       0                        0.0   \n", "5556                       0                        0.0   \n", "5562                       0                        0.0   \n", "...                      ...                        ...   \n", "2168                       0                        0.0   \n", "2620                       0                        0.0   \n", "2944                       0                        0.0   \n", "3708                       0                        0.0   \n", "3901                       0                        0.0   \n", "\n", "      methodCallWithNormalGuard  \n", "5498                        0.0  \n", "5506                        0.0  \n", "5519                        0.0  \n", "5556                        0.0  \n", "5562                        0.0  \n", "...                         ...  \n", "2168                        0.0  \n", "2620                        0.0  \n", "2944                        0.0  \n", "3708                        0.0  \n", "3901                        0.0  \n", "\n", "[847 rows x 204 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Outlier detection \n", "from collections import Counter\n", "\n", "def detect_outliers(df,n,features):\n", "    \"\"\"\n", "    Takes a dataframe df of features and returns a list of the indices\n", "    corresponding to the observations containing more than n outliers according\n", "    to the Tukey method.\n", "    \"\"\"\n", "    outlier_indices = []\n", "    \n", "    # iterate over features(columns)\n", "    for col in features:\n", "        # 1st quartile (25%)\n", "        Q1 = np.percentile(df[col], 25)\n", "        # 3rd quartile (75%)\n", "        Q3 = np.percentile(df[col],75)\n", "        # Interquartile range (IQR)\n", "        IQR = Q3 - Q1\n", "        \n", "        # outlier step\n", "        outlier_step = 1.5 * IQR\n", "        \n", "        # Determine a list of indices of outliers for feature col\n", "        outlier_list_col = df[(df[col] < Q1 - outlier_step) | (df[col] > Q3 + outlier_step )].index\n", "        \n", "        # append the found outlier indices for col to the list of outlier indices \n", "        outlier_indices.extend(outlier_list_col)\n", "        \n", "    # select observations containing more than 2 outliers\n", "    outlier_indices = Counter(outlier_indices)        \n", "    multiple_outliers = list( k for k, v in outlier_indices.items() if v > n )\n", "    \n", "    return multiple_outliers  \n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# process category features"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=449)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>wrapsLoop</th>\n", "      <th>wrapsTryCatch</th>\n", "      <th>wrapsIfElse</th>\n", "      <th>wrongMethodRef</th>\n", "      <th>constChange</th>\n", "      <th>unwrapIfElse</th>\n", "      <th>unwrapTryCatch</th>\n", "      <th>expArithMod</th>\n", "      <th>codeMove</th>\n", "      <th>expLogicExpand</th>\n", "      <th>...</th>\n", "      <th>typeOfFaultyStmt_ NewClass</th>\n", "      <th>typeOfFaultyStmt_ OperatorAssignment</th>\n", "      <th>typeOfFaultyStmt_ Parameter</th>\n", "      <th>typeOfFaultyStmt_ Return</th>\n", "      <th>typeOfFaultyStmt_ Switch</th>\n", "      <th>typeOfFaultyStmt_ Throw</th>\n", "      <th>typeOfFaultyStmt_ Try</th>\n", "      <th>typeOfFaultyStmt_ TypeReference</th>\n", "      <th>typeOfFaultyStmt_ UnaryOperator</th>\n", "      <th>typeOfFaultyStmt_ While</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 449 columns</p>\n", "</div>"], "text/plain": ["   wrapsLoop  wrapsTryCatch  wrapsIfElse  wrongMethodRef  constChange  \\\n", "0        0.0            0.0          0.0             0.0          0.0   \n", "1        0.0            0.0          0.0             0.0          0.0   \n", "\n", "   unwrapIfElse  unwrapTryCatch  expArithMod  codeMove  expLogicExpand  ...  \\\n", "0           0.0             0.0          0.0       0.0             0.0  ...   \n", "1           0.0             0.0          1.0       6.0             0.0  ...   \n", "\n", "   typeOfFaultyStmt_ NewClass  typeOfFaultyStmt_ OperatorAssignment  \\\n", "0                         0.0                                   0.0   \n", "1                         0.0                                   0.0   \n", "\n", "   typeOfFaultyStmt_ Parameter  typeOfFaultyStmt_ Return  \\\n", "0                          0.0                       0.0   \n", "1                          0.0                       0.0   \n", "\n", "   typeOfFaultyStmt_ Switch  typeOfFaultyStmt_ Throw  typeOfFaultyStmt_ Try  \\\n", "0                       0.0                      0.0                    0.0   \n", "1                       0.0                      0.0                    0.0   \n", "\n", "   typeOfFaultyStmt_ TypeReference  typeOfFaultyStmt_ UnaryOperator  \\\n", "0                              0.0                              0.0   \n", "1                              0.0                              0.0   \n", "\n", "   typeOfFaultyStmt_ While  \n", "0                      0.0  \n", "1                      0.0  \n", "\n", "[2 rows x 449 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "X_train.head(2)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"scrolled": true}, "outputs": [], "source": ["from imblearn.over_sampling import SMOTE\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["PCAmodel = PCA(n_components=6, random_state=42)\n", "X_train_PCA = PCAmodel.fit_transform(X_train.values)\n", "X_test_PAC = PCAmodel.fit_transform(X_test.values)\n", "\n", "X_train=X_train_PCA\n", "X_test = X_test_PAC"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Shuffle the training and test data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [], "source": ["from sklearn.utils import shuffle\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0]\tvalidation_0-map:0.85680\n", "Will train until validation_0-map hasn't improved in 10 rounds.\n", "[1]\tvalidation_0-map:0.91708\n", "[2]\tvalidation_0-map:0.92108\n", "[3]\tvalidation_0-map:0.94918\n", "[4]\tvalidation_0-map:0.95313\n", "[5]\tvalidation_0-map:0.96454\n", "[6]\tvalidation_0-map:0.95958\n", "[7]\tvalidation_0-map:0.96776\n", "[8]\tvalidation_0-map:0.96947\n", "[9]\tvalidation_0-map:0.96204\n", "[10]\tvalidation_0-map:0.95914\n", "[11]\tvalidation_0-map:0.95432\n", "[12]\tvalidation_0-map:0.95066\n", "[13]\tvalidation_0-map:0.95066\n", "[14]\tvalidation_0-map:0.95667\n", "[15]\tvalidation_0-map:0.95939\n", "[16]\tvalidation_0-map:0.95939\n", "[17]\tvalidation_0-map:0.95667\n", "[18]\tvalidation_0-map:0.95439\n", "Stopping. Best iteration:\n", "[8]\tvalidation_0-map:0.96947\n", "\n", "f1 score: 0.7567567567567568\n", "acc score: 0.6538461538461539\n", "precision score: 1.0\n", "recall score: 0.6086956521739131\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import xgboost as xgb\n", "from sklearn.metrics import recall_score\n", "from sklearn.metrics import f1_score\n", "from sklearn.metrics import precision_score\n", "from sklearn.metrics import accuracy_score\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6,gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=10, eval_metric=\"map\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "\n", "# Plot non-normalized confusion matrix\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  \n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                patch  groundtruth  label\n", "5       Patch3Chart_3            1      1\n", "8    Patch189Chart_19            0      0\n", "10   Patch188Chart_14            0      0\n", "28      Patch7Chart_5            0      0\n", "32    Patch17Chart_25            1      1\n", "33     Patch9Chart_13            1      0\n", "35    Patch10Chart_13            1      0\n", "42      Patch1Chart_1            1      1\n", "45    Patch16Chart_25            1      0\n", "49    Patch90Chart_17            1      1\n", "50      Patch5Chart_5            1      0\n", "53    Patch11Chart_13            1      1\n", "57    Patch19Chart_26            1      1\n", "59      Patch6Chart_5            1      0\n", "61    Patch92Chart_25            1      1\n", "66      Patch2Chart_1            1      1\n", "68    Patch14Chart_21            1      1\n", "69    Patch18Chart_26            1      1\n", "72      Patch8Chart_7            1      1\n", "90    Patch13Chart_15            1      0\n", "94    Patch93Chart_26            1      1\n", "97    Patch12Chart_15            1      0\n", "122     Patch4Chart_3            1      1\n", "126   Patch89Chart_13            1      0\n", "127   Patch15Chart_25            1      0\n", "133    Patch88Chart_9            1      1\n"]}], "source": ["d={'patch':test_file_name,'groundtruth':Y_test,'label':Y_pred}\n", "fn3 = pd.DataFrame(d)\n", "fn3.to_csv('./data/PatchSimODSChart.csv')\n", "print(fn3.head(30))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# PatchSim Repoducible labels:\n", "\n", "|patchid|project|bug|groundtruth|patchsim-label|\n", "|--|--|--|--|--|\n", "Patch1|Chart|1|Incorrect|Incorrect\n", "Patch2|Chart|1|Incorrect|Incorrect\n", "Patch4|Chart|3|Incorrect|Incorrect\n", "Patch5|Chart|5|Incorrect|Incorrect\n", "Patch6|Chart|5|Incorrect|Incorrect\n", "Patch7|Chart|5|Correct|Correct\n", "Patch8|Chart|7|Incorrect|Incorrect\n", "Patch9|Chart|13|Incorrect|Incorrect\n", "Patch10|Chart|13|Incorrect|Correct\n", "Patch11|Chart|13|Incorrect|Correct\n", "Patch12|Chart|15|Incorrect|Incorrect\n", "Patch13|Chart|15|Incorrect|Incorrect\n", "Patch14|Chart|21|Incorrect|Correct\n", "Patch15|Chart|25|Incorrect|Incorrect\n", "Patch16|Chart|25|Incorrect|Incorrect\n", "Patch17|Chart|25|Incorrect|Incorrect\n", "Patch18|Chart|26|Incorrect|\n", "Patch19|Chart|26|Incorrect|Incorrect\n", "Patch88|Chart|9|Incorrect|Correct\n", "Patch89|Chart|13|Incorrect|Correct\n", "Patch90|Chart|17|Incorrect|Correct\n", "Patch91|Chart|21|Incorrect|Correct\n", "Patch92|Chart|25|Incorrect|Incorrect\n", "Patch93|Chart|26|Incorrect|\n", "Patch188|Chart|14|Correct|Correct\n", "Patch189|Chart|19|Correct|Correct"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Testing on Lang"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(15, 204)\n", "(9207, 204)\n", "Total train:  (9207, 204)\n", "overfitting in train (7267, 204)\n", "correct in train (1940, 204)\n", "====================\n", "Total test:  (15, 204)\n", "overfitting in test (11, 204)\n", "correct in test (4, 204)\n"]}], "source": ["test = totaltest[ totaltest['id'].str.contains(r'Lang') ]\n", "training= totaltraining[~ totaltraining['id'].str.contains(r'Lang')]\n", "print(test.shape)\n", "print(training.shape)\n", "\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewArray', 'typeOfFaultyStmt_ NewClass',\n", "       'typeOfFaultyStmt_ OperatorAssignment', 'typeOfFaultyStmt_ Parameter',\n", "       'typeOfFaultyStmt_ Return', 'typeOfFaultyStmt_ Switch',\n", "       'typeOfFaultyStmt_ Throw', 'typeOfFaultyStmt_ Try',\n", "       'typeOfFaultyStmt_ TypeReference', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=445)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>wrapsLoop</th>\n", "      <th>wrapsTryCatch</th>\n", "      <th>wrapsIfElse</th>\n", "      <th>wrongMethodRef</th>\n", "      <th>constChange</th>\n", "      <th>unwrapIfElse</th>\n", "      <th>unwrapTryCatch</th>\n", "      <th>expArithMod</th>\n", "      <th>codeMove</th>\n", "      <th>expLogicExpand</th>\n", "      <th>...</th>\n", "      <th>typeOfFaultyStmt_ NewArray</th>\n", "      <th>typeOfFaultyStmt_ NewClass</th>\n", "      <th>typeOfFaultyStmt_ OperatorAssignment</th>\n", "      <th>typeOfFaultyStmt_ Parameter</th>\n", "      <th>typeOfFaultyStmt_ Return</th>\n", "      <th>typeOfFaultyStmt_ Switch</th>\n", "      <th>typeOfFaultyStmt_ Throw</th>\n", "      <th>typeOfFaultyStmt_ Try</th>\n", "      <th>typeOfFaultyStmt_ TypeReference</th>\n", "      <th>typeOfFaultyStmt_ While</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 445 columns</p>\n", "</div>"], "text/plain": ["   wrapsLoop  wrapsTryCatch  wrapsIfElse  wrongMethodRef  constChange  \\\n", "0        0.0            0.0          0.0             0.0          0.0   \n", "1        0.0            0.0          0.0             0.0          0.0   \n", "\n", "   unwrapIfElse  unwrapTryCatch  expArithMod  codeMove  expLogicExpand  ...  \\\n", "0           0.0             0.0          0.0       0.0             0.0  ...   \n", "1           0.0             0.0          0.0       0.0             0.0  ...   \n", "\n", "   typeOfFaultyStmt_ NewArray  typeOfFaultyStmt_ NewClass  \\\n", "0                         0.0                         0.0   \n", "1                         0.0                         0.0   \n", "\n", "   typeOfFaultyStmt_ OperatorAssignment  typeOfFaultyStmt_ Parameter  \\\n", "0                                   0.0                          0.0   \n", "1                                   0.0                          0.0   \n", "\n", "   typeOfFaultyStmt_ Return  typeOfFaultyStmt_ Switch  \\\n", "0                       0.0                       0.0   \n", "1                       0.0                       0.0   \n", "\n", "   typeOfFaultyStmt_ Throw  typeOfFaultyStmt_ Try  \\\n", "0                      0.0                    0.0   \n", "1                      0.0                    0.0   \n", "\n", "   typeOfFaultyStmt_ TypeReference  typeOfFaultyStmt_ While  \n", "0                              0.0                      0.0  \n", "1                              0.0                      0.0  \n", "\n", "[2 rows x 445 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "X_train.head(2)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0]\tvalidation_0-map:0.88014\n", "Will train until validation_0-map hasn't improved in 10 rounds.\n", "[1]\tvalidation_0-map:0.92737\n", "[2]\tvalidation_0-map:0.95360\n", "[3]\tvalidation_0-map:0.99242\n", "[4]\tvalidation_0-map:0.99242\n", "[5]\tvalidation_0-map:0.99242\n", "[6]\tvalidation_0-map:0.99242\n", "[7]\tvalidation_0-map:0.99242\n", "[8]\tvalidation_0-map:0.99242\n", "[9]\tvalidation_0-map:0.99242\n", "[10]\tvalidation_0-map:0.99242\n", "[11]\tvalidation_0-map:1.00000\n", "[12]\tvalidation_0-map:1.00000\n", "[13]\tvalidation_0-map:1.00000\n", "[14]\tvalidation_0-map:1.00000\n", "[15]\tvalidation_0-map:0.99242\n", "[16]\tvalidation_0-map:0.99242\n", "[17]\tvalidation_0-map:0.99242\n", "[18]\tvalidation_0-map:0.98052\n", "[19]\tvalidation_0-map:0.98601\n", "[20]\tvalidation_0-map:0.99242\n", "[21]\tvalidation_0-map:0.99242\n", "Stopping. Best iteration:\n", "[11]\tvalidation_0-map:1.00000\n", "\n", "f1 score: 0.9523809523809523\n", "acc score: 0.9333333333333333\n", "precision score: 1.0\n", "recall score: 0.9090909090909091\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from imblearn.over_sampling import SMOTE\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "PCAmodel = PCA(n_components=6, random_state=42)\n", "X_train_PCA = PCAmodel.fit_transform(X_train.values)\n", "X_test_PAC = PCAmodel.fit_transform(X_test.values)\n", "\n", "X_train=X_train_PCA\n", "X_test = X_test_PAC\n", "\n", "\n", "from sklearn.utils import shuffle\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "import xgboost as xgb\n", "from sklearn.metrics import recall_score\n", "from sklearn.metrics import f1_score\n", "from sklearn.metrics import precision_score\n", "from sklearn.metrics import accuracy_score\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6,gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=10, eval_metric=\"map\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "\n", "# Plot non-normalized confusion matrix\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  "]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                    patch  groundtruth  label\n", "7          Patch190Lang_7            0      0\n", "9         Patch191Lang_24            0      0\n", "13        Patch193Lang_39            1      1\n", "18         Patch21Lang_44            0      0\n", "52         Patch22Lang_46            1      1\n", "54   PatchHDRepair2Lang_6            1      1\n", "56        Patch192Lang_35            1      0\n", "70        Patch152Lang_53            1      1\n", "77         Patch20Lang_39            1      1\n", "86         Patch24Lang_53            1      1\n", "95        Patch150Lang_44            1      1\n", "100       Patch153Lang_58            1      1\n", "107        Patch23Lang_51            1      1\n", "117       Patch151Lang_51            1      1\n", "128        Patch25Lang_55            0      0\n"]}], "source": ["d={'patch':test_file_name,'groundtruth':Y_test,'label':Y_pred}\n", "fn3 = pd.DataFrame(d)\n", "fn3.to_csv('./data/PatchSimODSLang.csv')\n", "print(fn3.head(30))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# patchsim labels\n", "\n", "|patchid|project|bug|groundtruth|patchsim-label|\n", "|--|--|--|--|--|\n", "Patch20|Lang|39|Incorrect|\n", "Patch21|Lang|44|Correct|Correct\n", "Patch22|Lang|46|Incorrect|Incorrect\n", "Patch23|Lang|51|Incorrect|Incorrect\n", "Patch24|Lang|53|Incorrect|Correct\n", "Patch25|Lang|55|Correct|Correct\n", "Patch26|Lang|58|Correct|Correct\n", "Patch150|Lang|44|Incorrect|Incorrect\n", "Patch151|Lang|51|Incorrect|Incorrect\n", "Patch152|Lang|53|Incorrect|Correct\n", "Patch153|Lang|58|Incorrect|Incorrect\n", "Patch191|<PERSON>|24|Correct|Correct\n", "Patch192|Lang|35|Correct|\n", "Patch193|Lang|39|Incorrect|Incorrect\n", "PatchHDRepair1|Lang|57|Incorrect|Correct"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Testing on Math"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(83, 204)\n", "(5522, 204)\n", "Total train:  (5522, 204)\n", "overfitting in train (3625, 204)\n", "correct in train (1897, 204)\n", "====================\n", "Total test:  (83, 204)\n", "overfitting in test (63, 204)\n", "correct in test (20, 204)\n"]}], "source": ["test = totaltest[ totaltest['id'].str.contains(r'Math') ]\n", "training= totaltraining[~ totaltraining['id'].str.contains(r'Math') ]\n", "print(test.shape)\n", "print(training.shape)\n", "\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=443)\n", "[0]\tvalidation_0-map:0.83650\n", "Will train until validation_0-map hasn't improved in 10 rounds.\n", "[1]\tvalidation_0-map:0.86318\n", "[2]\tvalidation_0-map:0.86520\n", "[3]\tvalidation_0-map:0.87533\n", "[4]\tvalidation_0-map:0.86413\n", "[5]\tvalidation_0-map:0.86838\n", "[6]\tvalidation_0-map:0.85694\n", "[7]\tvalidation_0-map:0.84965\n", "[8]\tvalidation_0-map:0.86124\n", "[9]\tvalidation_0-map:0.85978\n", "[10]\tvalidation_0-map:0.84591\n", "[11]\tvalidation_0-map:0.84650\n", "[12]\tvalidation_0-map:0.84872\n", "[13]\tvalidation_0-map:0.85103\n", "Stopping. Best iteration:\n", "[3]\tvalidation_0-map:0.87533\n", "\n", "f1 score: 0.6862745098039216\n", "acc score: 0.6144578313253012\n", "precision score: 0.8974358974358975\n", "recall score: 0.5555555555555556\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "X_train.head(2)\n", "\n", "from imblearn.over_sampling import SMOTE\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "PCAmodel = PCA(n_components=6, random_state=42)\n", "X_train_PCA = PCAmodel.fit_transform(X_train.values)\n", "X_test_PAC = PCAmodel.fit_transform(X_test.values)\n", "\n", "X_train=X_train_PCA\n", "X_test = X_test_PAC\n", "\n", "\n", "from sklearn.utils import shuffle\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "import xgboost as xgb\n", "from sklearn.metrics import recall_score\n", "from sklearn.metrics import f1_score\n", "from sklearn.metrics import precision_score\n", "from sklearn.metrics import accuracy_score\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6,gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=10, eval_metric=\"map\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "\n", "# Plot non-normalized confusion matrix\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  \n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                    patch  groundtruth  label\n", "0          Patch196Math_5            0      0\n", "1          Patch45Math_50            0      0\n", "2          Patch194Math_3            0      0\n", "3          Patch47Math_53            0      0\n", "4         Patch209Math_99            0      0\n", "6   PatchHDRepair7Math_70            0      1\n", "11        Patch205Math_89            0      0\n", "12        Patch199Math_35            0      0\n", "14         Patch46Math_50            0      0\n", "15         Patch54Math_73            0      1\n", "16        Patch203Math_82            0      0\n", "17          Patch29Math_5            0      0\n", "19        Patch207Math_93            0      0\n", "20  PatchHDRepair4Math_34            0      0\n", "21         Patch51Math_70            0      1\n", "22         Patch44Math_50            0      0\n", "23        Patch206Math_90            0      0\n", "25        Patch197Math_25            0      0\n", "26         Patch195Math_4            0      0\n", "27        Patch204Math_85            0      1\n", "30         Patch42Math_49            1      0\n", "31         Patch154Math_2            1      0\n", "37         Patch32Math_28            1      0\n", "38  PatchHDRepair6Math_53            1      1\n", "39        Patch201Math_73            1      1\n", "41        Patch202Math_81            1      0\n", "44        Patch166Math_50            1      0\n", "46         Patch72Math_85            1      1\n", "47         Patch63Math_81            1      0\n", "48        Patch176Math_88            1      1\n", "51         Patch58Math_78            1      0\n", "55         Patch74Math_88            1      1\n", "58        Patch168Math_58            1      0\n", "60        Patch208Math_97            1      1\n", "63         Patch53Math_71            1      1\n", "64         Patch73Math_87            1      0\n", "65         Patch67Math_82            1      1\n", "67         Patch68Math_84            1      1\n", "71        Patch159Math_33            1      1\n", "73  PatchHDRepair3Math_22            1      0\n", "74  PatchHDRepair8Math_82            1      0\n", "75          Patch30Math_8            1      1\n", "76        Patch78Math_104            1      1\n", "78        Patch171Math_78            1      0\n", "79        Patch79Math_105            1      0\n", "80         Patch41Math_49            1      1\n", "81         Patch75Math_95            1      1\n", "82        Patch161Math_40            1      0\n", "83         Patch37Math_40            1      0\n", "85        Patch169Math_69            1      1\n", "                     patch  groundtruth  label\n", "88         Patch163Math_42            1      1\n", "89          Patch77Math_97            1      0\n", "92          Patch65Math_82            1      1\n", "93          Patch62Math_81            1      1\n", "98         Patch198Math_28            1      0\n", "99          Patch33Math_28            1      1\n", "101        Patch170Math_73            1      1\n", "102        Patch173Math_81            1      0\n", "103          Patch28Math_2            1      1\n", "105        Patch175Math_87            1      0\n", "106         Patch59Math_80            1      0\n", "108         Patch36Math_33            1      1\n", "109         Patch49Math_58            1      1\n", "110        Patch165Math_49            1      1\n", "111  PatchHDRepair9Math_85            1      1\n", "112         Patch43Math_49            1      1\n", "113        Patch157Math_24            1      1\n", "114        Patch174Math_85            1      1\n", "115        Patch162Math_41            1      0\n", "116          Patch31Math_8            1      1\n", "118         Patch55Math_73            1      1\n", "119         Patch64Math_81            1      1\n", "120         Patch38Math_40            1      0\n", "123         Patch48Math_57            1      0\n", "124         Patch156Math_7            1      1\n", "125         Patch69Math_84            1      1\n", "129          Patch27Math_2            1      1\n", "130         Patch66Math_82            1      1\n", "131        Patch167Math_57            1      0\n", "134        Patch158Math_28            1      0\n", "135        Patch160Math_39            1      0\n", "136         Patch76Math_95            1      0\n", "137         Patch155Math_4            1      0\n"]}], "source": ["d={'patch':test_file_name,'groundtruth':Y_test,'label':Y_pred}\n", "fn3 = pd.DataFrame(d)\n", "fn3.to_csv('./data/PatchSimODSMath.csv')\n", "print(fn3.head(50))\n", "print(fn3.tail(33))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# PatchSim Repoducible labels:\n", "\n", "|patchid|project|bug|groundtruth|patchsim-label|\n", "|--|--|--|--|--|\n", "Patch27|Math|2|Incorrect|Incorrect\n", "Patch28|Math|2|Incorrect|Incorrect\n", "Patch29|Math|5|Correct|Correct\n", "Patch30|Math|8|Incorrect|Correct\n", "Patch31|Math|8|Incorrect|Correct\n", "Patch32|Math|28|Incorrect|Incorrect\n", "Patch33|Math|28|Incorrect|Incorrect\n", "Patch34|Math|32|Incorrect|Correct\n", "Patch36|Math|33|Incorrect|Correct\n", "Patch37|Math|40|Incorrect|Correct\n", "Patch38|Math|40|Incorrect|Incorrect\n", "Patch44|Math|50|Correct|Correct\n", "Patch45|Math|50|Correct|Correct\n", "Patch46|Math|50|Correct|Correct\n", "Patch47|Math|53|Correct|Correct\n", "Patch48|Math|57|Incorrect|Incorrect\n", "Patch49|Math|58|Incorrect|Incorrect\n", "Patch51|Math|70|Correct|Correct\n", "Patch53|Math|71|Incorrect|Correct\n", "Patch54|Math|73|Correct|Correct\n", "Patch55|Math|73|Incorrect|Correct\n", "Patch58|Math|78|Incorrect|Correct\n", "Patch59|Math|80|Incorrect|Correct\n", "Patch62|Math|81|Incorrect|Incorrect\n", "Patch63|Math|81|Incorrect|Correct\n", "Patch64|Math|81|Incorrect|Correct\n", "Patch65|Math|82|Incorrect|Incorrect\n", "Patch66|Math|82|Incorrect|Incorrect\n", "Patch67|Math|82|Incorrect|Incorrect\n", "Patch68|Math|84|Incorrect|Incorrect\n", "Patch69|Math|84|Incorrect|Incorrect\n", "Patch72|Math|85|Incorrect|Incorrect\n", "Patch73|Math|87|Incorrect|Correct\n", "Patch74|Math|88|Incorrect|Incorrect\n", "Patch75|Math|95|Incorrect|Correct\n", "Patch76|Math|95|Incorrect|Correct\n", "Patch77|Math|97|Incorrect|Correct\n", "Patch78|Math|104|Incorrect|Incorrect\n", "Patch79|Math|105|Incorrect|Correct\n", "Patch154|Math|2|Incorrect|Incorrect\n", "Patch155|Math|4|Incorrect|Correct\n", "Patch157|Math|24|Incorrect|Incorrect\n", "Patch158|Math|28|Incorrect|Incorrect\n", "Patch159|Math|33|Incorrect|Incorrect\n", "Patch160|Math|39|Incorrect|Incorrect\n", "Patch161|Math|40|Incorrect|Correct\n", "Patch162|Math|41|Incorrect|Correct\n", "Patch163|Math|42|Incorrect|Correct\n", "Patch165|Math|49|Incorrect|Correct\n", "Patch166|Math|50|Incorrect|Correct\n", "Patch167|Math|57|Incorrect|Incorrect\n", "Patch168|Math|58|Incorrect|Correct\n", "Patch169|Math|69|Incorrect|Incorrect\n", "Patch170|Math|73|Incorrect|Correct\n", "Patch171|Math|78|Incorrect|Correct\n", "Patch172|Math|80|Incorrect|Correct\n", "Patch173|Math|81|Incorrect|Correct\n", "Patch174|Math|85|Incorrect|Incorrect\n", "Patch175|Math|87|Incorrect|Incorrect\n", "Patch176|Math|88|Incorrect|Incorrect\n", "Patch177|Math|105|Incorrect|Incorrect\n", "Patch194|Math|3|Correct|Correct\n", "Patch195|Math|4|Correct|Correct\n", "Patch196|Math|5|Correct|Correct\n", "Patch197|Math|25|Correct|Correct\n", "Patch198|Math|28|Incorrect|Incorrect\n", "Patch199|Math|35|Correct|Correct\n", "Patch201|Math|73|Incorrect|Incorrect\n", "Patch202|Math|81|Incorrect|Correct\n", "Patch203|Math|82|Correct|Correct\n", "Patch204|Math|85|Correct|Correct\n", "Patch205|Math|89|Correct|Correct\n", "Patch206|Math|90|Correct|Correct\n", "Patch207|Math|93|Correct|Correct\n", "Patch208|Math|97|Incorrect|Incorrect\n", "Patch209|Math|99|Correct|Correct\n", "PatchHDRepair3|Math|22|Incorrect|Correct\n", "PatchHDRepair4|Math|34|Correct|Correct\n", "PatchHDRepair5|Math|50|Incorrect|Correct\n", "PatchHDRepair6|Math|53|Incorrect|Incorrect\n", "PatchHDRepair7|Math|70|Correct|Correct\n", "PatchHDRepair8|Math|82|Incorrect|Incorrect\n", "PatchHDRepair9|Math|85|Incorrect|Incorrect"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Testing on Time"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(15, 204)\n", "(10273, 204)\n", "Total train:  (10273, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1974, 204)\n", "====================\n", "Total test:  (15, 204)\n", "overfitting in test (13, 204)\n", "correct in test (2, 204)\n"]}], "source": ["test = totaltest[ totaltest['id'].str.contains(r'Time') ]\n", "training= totaltraining[~ totaltraining['id'].str.contains(r'Time') ]\n", "print(test.shape)\n", "print(training.shape)\n", "\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewArray', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=448)\n", "[0]\tvalidation_0-map:0.97042\n", "Will train until validation_0-map hasn't improved in 10 rounds.\n", "[1]\tvalidation_0-map:0.97042\n", "[2]\tvalidation_0-map:0.96273\n", "[3]\tvalidation_0-map:0.96273\n", "[4]\tvalidation_0-map:0.96273\n", "[5]\tvalidation_0-map:0.97042\n", "[6]\tvalidation_0-map:0.97518\n", "[7]\tvalidation_0-map:0.96749\n", "[8]\tvalidation_0-map:0.96749\n", "[9]\tvalidation_0-map:0.96273\n", "[10]\tvalidation_0-map:0.95418\n", "[11]\tvalidation_0-map:0.95895\n", "[12]\tvalidation_0-map:0.95418\n", "[13]\tvalidation_0-map:0.95895\n", "[14]\tvalidation_0-map:0.95895\n", "[15]\tvalidation_0-map:0.95895\n", "[16]\tvalidation_0-map:0.95895\n", "Stopping. Best iteration:\n", "[6]\tvalidation_0-map:0.97518\n", "\n", "f1 score: 0.8799999999999999\n", "acc score: 0.8\n", "precision score: 0.9166666666666666\n", "recall score: 0.8461538461538461\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXQAAAEmCAYAAAByJWuvAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjEsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+j8jraAAAcl0lEQVR4nO3de5hU1Z3u8e+LGBXBC4J4i6IGzVEmGGSimIhEGQdNZkQPGJWYKF7jRI23Ockcr5zJmTxxxkQTjUFIMBqNQtRR9KAejIrEGyAqeJ2MFyKogHc0CvibP/ZqLXro7qrqqq5du9/P89RD7Uut/atufWv12nuvUkRgZmbNr0ejCzAzs9pwoJuZFYQD3cysIBzoZmYF4UA3MysIB7qZWUE40C33JG0k6TZJb0ua1ol2xku6q5a1NYqkfSU92+g6LF/k69CtViQdBZwJfB54F1gA/DAiHuhku0cDpwL7RMTqTheac5ICGBQR/9HoWqy5uIduNSHpTOCnwP8FBgDbA1cAh9Sg+R2A57pDmJdDUs9G12A5FRF++NGpB7Ap8B4wrp19NiAL/CXp8VNgg7RtJPBn4CzgdWApcGzadhHwEbAqHeM44ELg2pK2BwIB9EzLxwD/SfZXwgvA+JL1D5S8bh/gUeDt9O8+JdvuBf4PMCe1cxfQr4331lL/P5bUPwY4GHgOeAP4p5L9vwQ8CLyV9v058Jm07f70Xlam9/uNkvb/F/AqcE3LuvSandMxhqblbYBlwMhG/7fhR9c+3EO3WhgObAjc3M4+/xvYG9gDGEIWaueWbN+K7INhW7LQvlzS5hFxAVmv/4aI6B0RU9orRNLGwGXAQRHRhyy0F6xjv77A7WnfLYBLgNslbVGy21HAscCWwGeAs9s59FZkP4NtgfOBq4BvAnsC+wLnSdox7bsGOAPoR/azOwA4BSAiRqR9hqT3e0NJ+33J/lo5sfTAEfEnsrC/VlIv4NfA1RFxbzv1WgE50K0WtgCWR/tDIuOBiRHxekQsI+t5H12yfVXavioi7iDrne5aZT0fA4MlbRQRSyNi0Tr2+RrwfERcExGrI+J64Bng70r2+XVEPBcRHwA3kn0YtWUV2fmCVcDvyML60oh4Nx3/KbIPMiJiXkQ8lI77IvBLYL8y3tMFEfFhqmctEXEV8B/Aw8DWZB+g1s040K0WVgD9Ohjb3QZ4qWT5pbTukzZafSC8D/SutJCIWEk2THEysFTS7ZI+X0Y9LTVtW7L8agX1rIiINel5S+C+VrL9g5bXS9pF0gxJr0p6h+wvkH7ttA2wLCL+0sE+VwGDgZ9FxIcd7GsF5EC3WngQ+JBs3LgtS8iGC1psn9ZVYyXQq2R5q9KNEXFnRPwNWU/1GbKg66ielppeqbKmSvyCrK5BEbEJ8E+AOnhNu5ejSepNdl5iCnBhGlKybsaBbp0WEW+TjRtfLmmMpF6S1pd0kKQfp92uB86V1F9Sv7T/tVUecgEwQtL2kjYFftCyQdIASYeksfQPyYZuPl5HG3cAu0g6SlJPSd8AdgNmVFlTJfoA7wDvpb8evtNq+2vAThW2eSkwNyKOJzs3cGWnq7Sm40C3moiIfyO7Bv1csissFgPfBW5Ju/wzMBd4AngSmJ/WVXOsu4EbUlvzWDuEe6Q6lpBd+bEf/z0wiYgVwNfJrqxZQXaFytcjYnk1NVXobLITru+S/fVwQ6vtFwJXS3pL0uEdNSbpEGA0n77PM4GhksbXrGJrCr6xyMysINxDNzMrCAe6mVlBONDNzArCgW5mVhCe5KeT+vXrFwMHDmx0GWbWjnnz5i2PiP61ai/NiFmuOyNidK2O3R4HeicNHDiQuXPnNroMM2uHpNZ3BXelju4CrhkHuplZFaSObu7NdOWl4Q50M7MqONDNzAqi3EDvSg50M7MKSaJHj/IuElyzZk3HO9WIA93MrAruoZuZFYQD3cysIBzoZmYF4UA3MysASQ50M7OicKCbmRWEA93MrCAc6GZmBeFANzMrgEruFO1KDnQzsyq4h25mVhAOdDOzgnCgm5kVgG8sMjMrkDwGev5O05qZNYGWXnpHjzLa+ZWk1yUtLFnXV9Ldkp5P/25eTk0OdDOzKtQq0IGpwOhW674PzIqIQcCstNwhB7qZWRVqFegRcT/wRqvVhwBXp+dXA2PKqclj6GZmFarwpGg/SXNLlidFxKQOXjMgIpam568CA8o5kAPdzKwKFQT68ogYVu1xIiIkRTn7OtDNzKpQ51v/X5O0dUQslbQ18HpZNdWzIjOzoqrhSdF1uRX4dnr+beDfy3mRe+hmZhWq5Y1Fkq4HRpKNtf8ZuAD4EXCjpOOAl4DDy2nLgW5mVoVaBXpEHNnGpgMqbcuBbmZWhTzeKepANzOrggPdzKwgHOhmZgXg2RbNzArEgW5mVhAOdDOzgvCXRJuZFYDH0M3MCsSBbmZWEA50M7OCcKCbmRWEA93MrAB8UtTMrEAc6GZmBeFANzMrCAe6mVkBSPKdomZmReEeuplZQTjQzcwKwoFuZlYQDnQzswLI641F+TtNa7kyYcIEttxySwYPHtzoUqxG/DutjZZQ7+jRlRzo1q5jjjmGmTNnNroMqyH/TmvDgW5NZ8SIEfTt27fRZVgN+XdaG3kMdI+hm5lVwWPoTULSZpJOaXQdZpZP5fbOPeRSA5J6trdchs0AB7qZtalHjx5lPbpS7odcJH0LOBsI4AngPOBXQD9gGXBsRLwsaSrwF+CLwBxJfVstXw5cDvQH3gdOiIhnJA0ArgR2Sof8DnAasLOkBcDdEXFOl7xZM2saHnKpkKTdgXOB/SNiCHA68DPg6oj4AvBb4LKSl2wH7BMRZ65jeRJwakTsSfYBcUXa5zLgvtT+UGAR8H3gTxGxx7rCXNKJkuZKmrts2bIav+t8OfLIIxk+fDjPPvss2223HVOmTGl0SdZJ/p3WRh6HXPLeQ98fmBYRywEi4g1Jw4HD0vZrgB+X7D8tIta0XpbUG9gHmFbyA96g5BjfSu2vAd6WtHl7RUXEJLIPCIYNGxbVvrlmcP311ze6BKsx/047L683FuU90Cu1so3lHsBbEbFHF9djZgWVx0DP9ZALcA8wTtIWAGlc/I/AEWn7eGB2R41ExDvAC5LGpXYkaUjaPIts3BxJ60naFHgX6FPLN2JmxZLHIZdcB3pELAJ+CNwn6XHgEuBU4FhJTwBHk42rl2M8cFxqZxFwSFp/OvBVSU8C84DdImIF2YnUhZIurt07MrOiyGOg537IJSKuBq5utXr/dex3TAfLLwCj1/G61/g03EvXH1V5tWbWXeRxyCX3gW5mljc+KWpmViB5DPRcj6GbmeVVLe8UlXSGpEXpvN31kjasqqZqXmRm1t3V6qSopG3J7k4fFhGDgfX49Eq+injIxcysQnUYQ+8JbCRpFdALWFJNI+6hm5lVoYIeer+WqULS48TSdiLiFeBfgZeBpcDbEXFXNTW5h25mVoUKeujLI2JYO+1sTnbp9I7AW2RTlHwzIq6ttCb30M3MqlDDG4tGAS9ExLKIWAXcRDb3VMXcQzczq0INx9BfBvaW1Av4ADgAmFtNQw50M7MK1fKkaEQ8LGk6MB9YDTxGms21Ug50M7Mq1PIql4i4ALigs+040M3MqpDHO0Ud6GZmFZLU5d8XWg4HuplZFdxDNzMrCAe6mVlBONDNzArCgW5mVgD+ggszswJxoJuZFYQD3cysIBzoZmYF4UA3MysAnxQ1MysQ3/pvZlYQ7qGbmRWEA93MrAA8hm5mViAOdDOzgnCgm5kVRFMFuqSfAdHW9og4rS4VmZk1gaYKdGBul1VhZtZEmu6kaERcXbosqVdEvF//kszM8i+Pgd7hrU6Shkt6CngmLQ+RdEXdKzMzy7EePXqU9ejSmsrY56fA3wIrACLicWBEPYsyM8u7lmGXjh5dqayrXCJicavC1tSnHDOz/Gu6MfQSiyXtA4Sk9YHTgafrW5aZWb7lMdDLGXI5GfgHYFtgCbBHWjYz67aacsglIpYD47ugFjOzptGUPXRJO0m6TdIySa9L+ndJO3VFcWZmeZXHHno5Qy7XATcCWwPbANOA6+tZlJlZnpUb5nkM9F4RcU1ErE6Pa4EN612YmVme5THQ25vLpW96+v8kfR/4HdncLt8A7uiC2szMciuPY+jtnRSdRxbgLVWfVLItgB/Uqygzs7xrqu8UjYgdu7IQM7Nm0cw3FiFpMLAbJWPnEfGbehVlZpZ3TRnoki4ARpIF+h3AQcADgAPdzLqtPAZ6OYNAY4EDgFcj4lhgCLBpXasyM8u5prrKpcQHEfGxpNWSNgFeBz5b57rMzHKtWXvocyVtBlxFduXLfODBulZlZpZjtb6xSNJmkqZLekbS05KGV1NXOXO5nJKeXilpJrBJRDxRzcHMzIqixj30S4GZETFW0meAXtU00t6NRUPb2xYR86s5oJlZEdQq0CVtSvalQccARMRHwEfVtNVeD/3f2tkWwP7VHLBoVq1axdKlSxtdhtXQNtts0+gSrAlUEOj9JM0tWZ4UEZNKlncElgG/ljSEbGj79IhYWWlN7d1Y9NVKGzMz6y4qCPTlETGsne09gaHAqRHxsKRLge8D51VaU/7uXTUzyzlJtfyS6D8Df46Ih9PydLKAr5gD3cysCrW6yiUiXiX7qs9d06oDgKeqqamsW//NzGxtNb7K5VTgt+kKl/8Ejq2mkXJu/RfZV9DtFBETJW0PbBURj1RzQDOzIqhloEfEAqC9cfaylDPkcgUwHDgyLb8LXN7ZA5uZNau8fmNROUMue0XEUEmPAUTEm+nPAjOzbiuPt/6XE+irJK1Hdu05kvoDH9e1KjOznGvWQL8MuBnYUtIPyWZfPLeuVZmZ5VxTBnpE/FbSPLJLaQSMiYin616ZmVmONWWgp6ta3gduK10XES/XszAzs7xq5q+gu51Pvyx6Q7J5B54Fdq9jXWZmudZUXxLdIiL+qnQ5zcJ4Shu7m5l1C83aQ19LRMyXtFc9ijEzaxZNGeiSzixZ7EE2acySulVkZpZzzTyG3qfk+WqyMfXf16ccM7Pm0HSBnm4o6hMRZ3dRPWZmTaGpAl1Sz4hYLenLXVmQmVkzaKpABx4hGy9fIOlWYBrwyVciRcRNda7NzCy3mi3QW2wIrCD7DtGW69EDcKCbWbfUjCdFt0xXuCzk0yBvEXWtysws55ot0NcDerN2kLdwoJtZt9Zsd4oujYiJXVaJmVkTabYeev6qNTPLgWYcQz+gy6owM2syTRXoEfFGVxZiZtZMmirQzcysbQ50M7OCcKCbmRVAM54UNTOzNjjQzcwKwoFuZlYQDnQzswKQ1HS3/puZWRvcQzczKwgHuplZQTjQzcwKwNehm5kViAPdzKwgHOhmZgXhQDczKwgHuplZAfikqJlZgeTxTtH8VWRm1gRaeukdPSpobz1Jj0maUW1N7qGbmVWhDkMupwNPA5tU24B76GZmFSq3d15u6EvaDvgaMLkzdbmHbmZWhQp66P0kzS1ZnhQRk1rt81PgH4E+nanJgW5mVoUKAn15RAxrp52vA69HxDxJIztTkwPdzKwKNRxD/zLw95IOBjYENpF0bUR8s9KGPIZuZlaFWo2hR8QPImK7iBgIHAHcU02YgwPdOvDKK68wduxY9ttvP0aOHMnkyZ06Z2MNMmXKFF577TWefPLJT9aNHTuWhQsXsmbNGvbcc88GVtd8an1StFYc6Naunj17cv7553PfffcxY8YMpk6dynPPPdfosqxCU6dOZfTo0WutW7hwIYcddhj3339/g6pqbvUI9Ii4NyK+Xm1NHkO3dg0YMIABAwYA0Lt3bz73uc+xdOlSdtlllwZXZpWYPXs2O+yww1rrnnnmmQZVUwx5vFPUgW5lW7x4MQsXLmTo0KGNLsWs4fI4l0v+PmI6IGmcpKcl/UHSMEmXpfUjJe1Tst8YSbuVLE+UNKoRNRfBypUrOf7445k4cSJ9+nTqUlmzppfXMfSm6aEr+8kIOA44ISIeSJtaLtgfCbwH/DEtjwFmAE8BRMT5XVZswaxatYrjjz+eww47jIMPPrjR5ZjlQrfroUs6U9LC9PiepB9J+oeS7RdKOjs9P0fSo5KekHRRWjdQ0rOSfgMsBM4DvgJMkXRx6pXPkDQQOBk4Q9ICSfsBfw9cnJZ3ljRV0tjU7ouSLpI0X9KTkj6f1veXdLekRZImS3pJUr96/ozyLiI466yzGDRoECeddFKjyzHLjTz20OsW6JL2BI4F9gL2Bk4AbgAOL9ntcOAGSQcCg4AvAXsAe0oakfYZBFwREbtHxEVkPfLxEXFOSyMR8SJwJfCTiNgjIu4DbgXOSct/WkeJyyNiKPAL4Oy07gKya0B3B6YD27fx3k6UNFfS3BUrVlT2g2kyjzzyCNOnT2fOnDmMGjWKUaNGMWvWrEaXZRW67rrrePDBB9l1111ZvHgxEyZMYMyYMSxevJjhw4dz++23M3PmzEaX2VTyGOj1HHL5CnBzRKwEkHQTsC+wpaRtgP7AmxGxWNLpwIHAY+m1vcmC/GXgpYh4qA713ZT+nQccVlLzoQARMVPSm+t6YZqHYRLAkCFDog615cZee+3FkiVLGl2GddJRRx21zvW33HJLF1dSHHkccmnEGPo0YCywFVmPHbKx8X+JiF+W7piGUlbWqY4P079raKJzCWbWeI3ofZejnmPos4ExknpJ2pis5zubLMSPIAv1aWnfO4EJknoDSNpW0pYVHu9d1p6prPVyOeaQhoTSMNDmFb7ezLqJPA651C3QI2I+MBV4BHgYmBwRj0XEIrKgfSUilqZ97wKuAx6U9CTZ+HWlYXwbcGg6Cbov8DvgHGXfALJzmW1cBBwoaSEwDniV7IPBzGwteQz0ug41RMQlwCXrWP9X61h3KXDpOpoZ3Gq/kSXP7wXuTc+fA77Q6rW7lTw/puR1A0uezyW75BHgbeBvI2K1pOHAX0fEh5iZtZLHIRePHa9te+BGST2Aj8iuzDEzW4sk3/qfdxHxPPDFRtdhZvnnHrqZWUE40M3MCsKBbmZWEA50M7MCyOuNRQ50M7MqONDNzArCgW5mVhAOdDOzgnCgm5kVgO8UNTMrEPfQzcwKwoFuZlYQDnQzswLwjUVmZgXiQDczKwgHuplZQTjQzcwKwoFuZlYAPilqZlYgvlPUzKwg3EM3MysIB7qZWQF4DN3MrEAc6GZmBZHHQM/faVozsybQMuzS0aOMdj4r6Q+SnpK0SNLp1dbkHrqZWRVq2ENfDZwVEfMl9QHmSbo7Ip6qtCEHuplZhWp5UjQilgJL0/N3JT0NbAs40M3MukIFgd5P0tyS5UkRMamNNgcCXwQerqYmB7qZWRUqCPTlETGsjPZ6A78HvhcR71RTkwPdzKwKtbz1X9L6ZGH+24i4qdp2HOhmZhWq5Ri6soamAE9HxCWdacuXLZqZVaFWly0CXwaOBvaXtCA9Dq6mJvfQzcyqUMOrXB4AatKYA93MrAp5vFPUgW5mVgUHuplZAXi2RTOzAnGgm5kVhAPdzKwgHOhmZgUgyV8SbWZWFO6hm5kVhAPdzKwgHOhmZgXg69DNzArEgW5mVhAOdDOzgnCgm5kVhAPdzKwAfFLUzKxAfKeomVlB5LGHrohodA1NTdIy4KVG19FF+gHLG12E1Ux3+n3uEBH9a9WYpJlkP79yLI+I0bU6dnsc6FY2SXMjYlij67Da8O+zePI3CGRmZlVxoJuZFYQD3SoxqdEFWE3591kwHkM3MysI99DNzArCgW5mVhAOdKsrSZtJOqXRdVhG0jhJT0v6g6Rhki5L60dK2qdkvzGSditZnihpVCNqtvJ5DN3aJalnRKxua7mM1w8EZkTE4DqUZ2VSdlujgDuAf46IB1ptvxB4LyL+NS1PJfu9Te/iUq0THOjdiKRvAWcDATwBnAf8iuyOt2XAsRHxcvqf+S/AF4E5QN9Wy5enR3/gfeCEiHhG0gDgSmCndMjvAKcBhwDPAndHxDn1f6fFIOlMYEJanAxsBSyOiMvT9gtJISzpHOBwYAPg5oi4IH2Y3gk8DOwJ3AicA7wC3ArcTvbfw3eBh4A1ZP8dnA7cDLydHv+T7L+VGRExXdKLwNXA3wHrA+PS778/cB2wDfAg8DfAnhHRXe5GbbyI8KMbPIDdgeeAfmm5L3Ab8O20PAG4JT2fCswA1mtjeRYwKD3fC7gnPb8B+F56vh6wKTAQWNjo999sD7IAfhLYGOgNLCL7QL2vZJ+ngM8CB5JdgiiyYdQZwIj0s/8Y2LvkNfcCw9LzkWQhDXAhcHbJflOBsetaBl4ETk3PTwEmp+c/B36Qno8m6zj0a/TPsjs9PDlX97E/MC1Sbyki3pA0HDgsbb8G+HHJ/tMiYk3rZUm9gX2AaSWTE21QcoxvpfbXAG9L2rwu76b4vkLW014JIOkmYF9gS0nbkP119GZELJZ0OlmoP5Ze2xsYBLwMvBQRD9WhvpvSv/P49L+hrwCHAkTETElv1uG41g4HurVlZRvLPYC3ImKPLq7HMtOAsWTDLzekdQL+JSJ+WbpjGnJp/XuslQ/Tv2twjuSGr3LpPu4BxknaAkBSX+CPwBFp+3hgdkeNRMQ7wAuSxqV2JGlI2jyLbNwcSetJ2hR4F+hTyzfSTcwGxkjqJWljsp7vbLIQP4Is1Kelfe8EJqS/npC0raQtKzxe699TNb+3OWTj+Eg6EPBfZ13Mgd5NRMQi4IfAfZIeBy4BTgWOlfQEcDTZybByjAeOS+0sIjvpSXr9VyU9Sfan+G4RsQKYI2mhpItr946KLSLmk41bP0J2UnNyRDyWfo99gFciYmna9y6yk5EPpp/9dCoP49uAQyUtkLQv8DvgHEmPSdq5zDYuAg6UtBAYB7xK9sFgXcRXuZhZTUjaAFgTEavT+ZlfeGiua3nsy8xqZXvgRkk9gI+AExpcT7fjHrqZWUF4DN3MrCAc6GZmBeFANzMrCAe6NZykNelyuYWSpknq1Ym2pkoam55PLp0xcB37rjXDYAXHeFHSf/vG97bWt9rnvQqPdaGksyut0bonB7rlwQcRsUdkMzJ+BJxculFSVVdjRcTxEfFUO7uMJJvGwKwQHOiWN7OBz6Xe82xJtwJPpTtPL5b0qKQnJJ0En9yp+nNJz0r6/8And0hKulfSsPR8tKT5kh6XNCvdFn8ycEbLzTSS+kv6fTrGo5K+nF67haS7JC2SNJnsVvt2SbpF0rz0mhNbbftJWj8rzVCIpJ0lzUyvmS3p87X4YVr34uvQLTdST/wgYGZaNRQYHBEvpFB8OyL+Ot3AMkfSXWQzEO4K7AYMIJuB8Fet2u0PXAWMSG31TZOTXcnac4BfB/wkIh6QtD3ZLfX/A7gAeCAiJkr6GnBcGW9nQjrGRsCjkn6f7prdGJgbEWdIOj+1/V2y2RJPjojnJe0FXEE22ZlZ2RzolgcbSVqQns8GppANhTwSES+k9QcCX2gZHyebmncQ2TSx16fZHZdIumcd7e8N3N/SVkS80UYdo4DdSmaR3CTNjzKCNKNgRNxe5iyCp0k6ND3/bKp1Bdl0ti2Tal0L3NTBDJZmZXOgWx580PoW8RRspTMFimwO7jtb7XdwDevoQTZ3+F/WUUvZJI0k+3AYHhHvS7oX2LCN3QPPYGk14jF0axZ3At+RtD6ApF3SLIT3A99IY+xbA19dx2sfAkZI2jG9tm9a33pGwbvIJiwj7dcSsPcDR6V1B9HxLIKbks1V/n4aC9+7ZFsPspkSSW0+0MEMlmZlc6Bbs5hMNj4+P83m90uyvzBvBp5P235D9tVna4mIZcCJZMMbj/PpkEfrGQZPA4alk65P8enVNheRfSAsIht6ebmDWmcCPSU9DfyI7AOlxUrgS+k97A9MTOvbmsHSrGyey8XMrCDcQzczKwgHuplZQTjQzcwKwoFuZlYQDnQzs4JwoJuZFYQD3cysIP4L+Far9liyLegAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "X_train.head(2)\n", "\n", "from imblearn.over_sampling import SMOTE\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "PCAmodel = PCA(n_components=6, random_state=42)\n", "X_train_PCA = PCAmodel.fit_transform(X_train.values)\n", "X_test_PAC = PCAmodel.fit_transform(X_test.values)\n", "\n", "X_train=X_train_PCA\n", "X_test = X_test_PAC\n", "\n", "\n", "from sklearn.utils import shuffle\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "import xgboost as xgb\n", "from sklearn.metrics import recall_score\n", "from sklearn.metrics import f1_score\n", "from sklearn.metrics import precision_score\n", "from sklearn.metrics import accuracy_score\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6,gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=10, eval_metric=\"map\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "\n", "# Plot non-normalized confusion matrix\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  \n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                      patch  groundtruth  label\n", "24          Patch210Time_15            0      0\n", "29   PatchHDRepair10Time_19            0      1\n", "34           Patch83Time_11            1      1\n", "36          Patch182Time_11            1      1\n", "40            Patch80Time_4            1      1\n", "43          Patch186Time_18            1      0\n", "62          Patch183Time_12            1      1\n", "84            Patch81Time_4            1      1\n", "87          Patch185Time_16            1      1\n", "91           Patch181Time_7            1      1\n", "96          Patch187Time_19            1      1\n", "104          Patch82Time_11            1      1\n", "121          Patch180Time_4            1      1\n", "132         Patch184Time_14            1      1\n", "138          Patch84Time_11            1      0\n"]}], "source": ["d={'patch':test_file_name,'groundtruth':Y_test,'label':Y_pred}\n", "fn3 = pd.DataFrame(d)\n", "fn3.to_csv('./data/PatchSimODSTime.csv')\n", "print(fn3.head(20))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# PatchSim Repoducible labels:\n", "\n", "|patchid|project|bug|groundtruth|patchsim-label|\n", "|--|--|--|--|--|\n", "Patch80|Time|4|Incorrect|Incorrect\n", "Patch81|Time|4|Incorrect|Incorrect\n", "Patch82|Time|11|Incorrect|Correct\n", "Patch83|Time|11|Incorrect|Correct\n", "Patch84|Time|11|Incorrect|Correct\n", "Patch180|Time|4|Incorrect|Correct\n", "Patch181|Time|7|Incorrect|Incorrect\n", "Patch182|Time|11|Incorrect|Correct\n", "Patch183|Time|12|Incorrect|Correct\n", "Patch184|Time|14|Incorrect|Incorrect\n", "Patch185|Time|16|Incorrect|Correct\n", "Patch186|Time|18|Incorrect|Incorrect\n", "Patch187|Time|19|Incorrect|Incorrect\n", "Patch210|Time|15|Correct|Correct\n", "PatchHDRepair10|Time|19|Correct|Correct"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.10"}}, "nbformat": 4, "nbformat_minor": 2}