--- /src/com/google/javascript/jscomp/CheckGlobalThis.java
+++ /src/com/google/javascript/jscomp/CheckGlobalThis.java
@@ -112,6 +112,15 @@
       }
 
       // Don't traverse functions that are getting lent to a prototype.
+      Node gramps = parent.getParent();
+      if (NodeUtil.isObjectLitKey(parent, gramps)) {
+        JSDocInfo maybeLends = gramps.getJSDocInfo();
+        if (maybeLends != null &&
+            maybeLends.getLendsName() != null &&
+            maybeLends.getLendsName().endsWith(".prototype")) {
+          return false;
+        }
+      }
     }
 
     if (parent != null && parent.getType() == Token.ASSIGN) {

