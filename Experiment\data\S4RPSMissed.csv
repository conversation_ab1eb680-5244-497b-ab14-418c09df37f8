id,label,methodCallWithNullGuard,faultyClassExceptionType,methodCallWithTryCatch,typeOfFaultyStmtAfter1,typeOfFaultyStmtAfter2,typeOfFaultyStmtAfter3,typeOfFaultyStmtBefore1,typeOfFaultyStmtBefore2,typeOfFaultyStmtBefore3,typeOfFaultyStmtParent,hasObjectiveMethodCall,hasInvocationsProneException,inSynchronizedMethod,localVarNotAssigned,localVarNotUsed,similarObjectTypeWithNormalGuard,similarObjectTypeWithNullGuard,typeOfFaultyStmt,fieldNotAssigned,fieldNotUsed,similarPrimitiveTypeWithNormalGuard,similarPrimitiveTypeWithNullGuard,methodThrowsException,objectUsedInAssignment,primitiveUsedInAssignment,methodCallWithNormalGuard
patch172_Math80,1, false, false, false, Return, , , LocalVariable, , , If, false, false, false, true, false, true, false, For, false, false, true, false, false, false, true, false
patch91_Chart21,1, false, false, false, If, Return, , LocalVariable, If, , Method, false, false, false, false, true, true, true, If, false, false, true, false, false, true, false, false
PatchHDRepair1_Lang57,0, false, false, false, , , , , , , Method, false, false, false, false, false, true, true, Return, true, false, false, false, false, false, false, false
patch177_Math105,1, false, false, false, OperatorAssignment, OperatorAssignment, UnaryOperator, , , , Method, false, false, false, false, false, false, false, If, false, false, false, false, false, false, false, false
patch26_Lang58,1, false, false, false, , , , LocalVariable, LocalVariable, If, If, false, false, false, false, false, false, false, Switch, false, false, false, false, true, false, false, false
