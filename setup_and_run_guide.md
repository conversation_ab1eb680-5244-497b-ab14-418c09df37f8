## 第一步：创建Conda环境

### 1. 创建新的conda环境
```bash
conda create -n ods-experiment python=3.8 -y
conda activate ods-experiment
```

### 2. 安装Python依赖
```bash
# 安装核心机器学习库
conda install pandas numpy scikit-learn matplotlib jupyter -y

# 安装XGBoost和imbalanced-learn
conda install xgboost -y
conda install imbalanced-learn -y
```

### 3. 验证安装
```bash
python test_dependencies.py
```

## 第二步：验证环境

### 1. 检查Python环境
运行测试脚本验证所有依赖：
```bash
python test_dependencies.py
```

### 2. 检查Java环境
```bash
java -version
mvn -version
```

## 第三步：准备数据文件

### 1. 创建训练数据
项目需要训练数据文件，从实验数据中复制：
```bash
copy "Experiment\data\CodeTrain10302.csv" "train.csv"
```

### 2. 验证数据文件
确保以下文件存在：
- `train.csv` - 训练数据（从CodeTrain10302.csv复制）
- `test_example.csv` - 测试数据示例（已存在）

## 第四步：运行Python预测脚本

### 1. 激活环境并运行预测
```bash
conda activate ods-experiment
python predict.py
```

### 2. 检查输出
脚本会生成 `prediction.csv` 文件，包含预测结果：
```bash
type prediction.csv
```

预期输出格式：
```
,patch,prediction_label
0,test_1,1
```

## 第五步：设置Coming工具（用于特征提取）

### 1. 克隆Coming仓库
```bash
cd ..
git clone https://github.com/SpoonLabs/coming.git
cd coming
```

### 2. 构建Coming项目
```bash
mvn install -DskipTests
```

### 3. 运行特征提取示例
```bash
java -classpath ./target/coming-0-SNAPSHOT-jar-with-dependencies.jar fr.inria.coming.main.ComingMain -input files -mode features -location ./src/main/resources/pairsD4j -output ./out
```

## 第六步：运行Jupyter实验

### 1. 启动Jupyter
```bash
cd ../ODSExperiment
jupyter notebook
```

### 2. 运行实验notebook
打开并运行以下notebook：
- `Experiment/RQ1-902-patches.ipynb`
- `Experiment/RQ2-PatchSim-Comparison.ipynb`
- `Experiment/RQ3-large-dataset-projects.ipynb`

## 故障排除

### 常见问题1：train.csv文件缺失
如果train.csv文件不存在，可以使用Experiment/data/目录中的数据：
```bash
# 复制一个可用的训练数据文件
cp Experiment/data/CodeTrain10302.csv train.csv
```

### 常见问题2：Java版本不兼容
确保使用Java 8：
```bash
# 如果有多个Java版本，设置JAVA_HOME
export JAVA_HOME=/path/to/java8
```

### 常见问题3：依赖版本冲突
如果遇到版本冲突，创建新的干净环境：
```bash
conda deactivate
conda remove -n ods-experiment --all
# 重新创建环境
```

## 验证项目运行成功

### 1. 运行完整测试流程
```bash
# 1. 激活环境
conda activate ods-experiment

# 2. 验证依赖
python test_dependencies.py

# 3. 准备训练数据
copy "Experiment\data\CodeTrain10302.csv" "train.csv"

# 4. 运行预测脚本
python predict.py

# 5. 检查输出文件
dir prediction.csv
```

### 2. 检查预测结果
```bash
type prediction.csv
```

### 3. 运行Jupyter实验
```bash
# 启动Jupyter Lab
jupyter lab

# 或启动传统Jupyter Notebook
jupyter notebook
```

## 项目文件结构说明
```
ODSExperiment/
├── predict.py              # 主预测脚本
├── test_example.csv        # 测试数据示例
├── train.csv              # 训练数据（可能需要创建）
├── Experiment/            # 实验数据和notebook
│   ├── data/             # 各种数据集
│   └── *.ipynb           # Jupyter实验notebook
├── Features/             # ODS特征数据
├── Source/               # 源代码文件
└── Tests/                # 测试相关文件
```
