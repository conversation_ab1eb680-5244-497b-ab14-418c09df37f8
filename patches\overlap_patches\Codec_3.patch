--- /src/java/org/apache/commons/codec/language/DoubleMetaphone.java
+++ /src/java/org/apache/commons/codec/language/DoubleMetaphone.java
@@ -452,7 +452,7 @@
             if ((contains(value, 0 ,4, "VAN ", "VON ") || contains(value, 0, 3, "SCH")) || contains(value, index + 1, 2, "ET")) {
                 //-- obvious germanic --//
                 result.append('K');
-            } else if (contains(value, index + 1, 4, "IER")) {
+            } else if (contains(value, index + 1, 3, "IER")) {
                 result.append('J');
             } else {
                 result.append('J', 'K');

