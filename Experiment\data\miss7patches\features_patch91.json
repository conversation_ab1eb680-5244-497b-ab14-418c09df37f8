{"files": [{"features": [{"FEATURES_BINARYOPERATOR": {"0_lower > upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "10_(value >= this.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "11_(value <= this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "12_b0 <= this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "13_b1 > this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "14_(b0 < this.upper) && (b1 >= b0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "15_(b0 < this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "16_(b1 >= b0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "17_value > this.upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "18_value < this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "19_range1 == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "1_(((\"Range(double, double): require lower (\" + lower) + \") <= upper (\") + upper) + \").\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "20_range2 == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "21_range == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "22_value < range.getLowerBound()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "23_value > range.getUpperBound()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "24_range == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "25_length * lowerMargin": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "26_length * upperMargin": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "27_range.getLowerBound() - lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "28_range.getUpperBound() + upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "29_base.getLowerBound() + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "2_(((\"Range(double, double): require lower (\" + lower) + \") <= upper (\") + upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "30_base.getUpperBound() + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "31_value > 0.0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "32_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "33_value < 0.0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "34_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "35_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "36_(obj instanceof org.jfree.data.Range)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "true", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "37_(this.lower == range.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "38_(this.upper == range.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "39_((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "true", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_((\"Range(double, double): require lower (\" + lower) + \") <= upper (\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "40_(temp >>> 32)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "true", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "41_(29 * result) + ((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "42_(29 * result)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "43_((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "true", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "44_(temp >>> 32)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "true", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "45_(((\"Range[\" + this.lower) + \",\") + this.upper) + \"]\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "46_(((\"Range[\" + this.lower) + \",\") + this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "47_((\"Range[\" + this.lower) + \",\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "48_(\"Range[\" + this.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_(\"Range(double, double): require lower (\" + lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "5_this.upper - this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "6_(this.lower / 2.0) + (this.upper / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "7_(this.lower / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "8_(this.upper / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "9_(value >= this.lower) && (value <= this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_LOGICAL_EXPRESSION": {"logical_expression_0_!(this.lower == range.lower)": {"LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "true", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "true", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {"org.jfree.data.Range": {"C3_TYPEACCESS_ACTUAL_VAR": "false", "C4_SIMILAR_TYPEACCESS_ACTUAL_VAR": "false"}}, "FEATURES_VARS": {"lower": {"V10_VAR_TYPE_Similar_VAR": "true", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "true", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "false", "V8_VAR_PRIMITIVE": "true", "V9_VAR_TYPE_Similar_Literal": "true"}, "range": {"V10_VAR_TYPE_Similar_VAR": "true", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "true", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "true", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "true", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "true", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "true", "V8_VAR_PRIMITIVE": "false", "V9_VAR_TYPE_Similar_Literal": "false"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "If", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "Return", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "LocalVariable", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "If", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "Method", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "false", "S1_LOCAL_VAR_NOT_USED": "true", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "S3_TYPE_OF_FAULTY_STATEMENT": "If", "S4_Field_NOT_ASSIGNED": "false", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "true", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S6_METHOD_THROWS_EXCEPTION": "false", "S7_OBJECT_USED_IN_ASSIGNMENT": "true", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "null", "dst_parent": "null", "dst_parent_type": "null", "dst_type": "null", "operator": "DEL", "src": "if (!(this.lower == range.lower)) {\n    return false;\n}", "src_parent": "{\n    if (!(obj instanceof org.jfree.data.Range)) {\n        return false;\n    }\n    org.jfree.data.Range range = ((org.jfree.data.Range) (obj));\n    if (!(this.lower == range.lower)) {\n        return false;\n    }\n    if (!(this.upper == range.upper)) {\n        return false;\n    }\n    return true;\n}", "src_parent_type": "Block", "src_type": "If"}}, {"CONSTANT": {"false": {"C1_SAME_TYPE_CONSTANT": "true", "C2_SAME_TYPE_CONSTANT_VAR": "false", "C2_SAME_TYPE_VAR": "false"}, "true": {"C1_SAME_TYPE_CONSTANT": "true", "C2_SAME_TYPE_CONSTANT_VAR": "false", "C2_SAME_TYPE_VAR": "false"}}, "FEATURES_BINARYOPERATOR": {"0_lower > upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "10_(value >= this.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "11_(value <= this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "12_b0 <= this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "13_b1 > this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "14_(b0 < this.upper) && (b1 >= b0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "15_(b0 < this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "16_(b1 >= b0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "17_value > this.upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "18_value < this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "19_range1 == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "1_(((\"Range(double, double): require lower (\" + lower) + \") <= upper (\") + upper) + \").\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "20_range2 == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "21_range == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "22_value < range.getLowerBound()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "23_value > range.getUpperBound()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "24_range == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "25_length * lowerMargin": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "26_length * upperMargin": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "27_range.getLowerBound() - lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "28_range.getUpperBound() + upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "29_base.getLowerBound() + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "2_(((\"Range(double, double): require lower (\" + lower) + \") <= upper (\") + upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "30_base.getUpperBound() + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "31_value > 0.0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "32_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "33_value < 0.0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "34_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "35_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "36_(obj instanceof org.jfree.data.Range)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "true", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "37_(this.lower == range.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "38_(this.upper == range.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "39_((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "true", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_((\"Range(double, double): require lower (\" + lower) + \") <= upper (\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "40_(temp >>> 32)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "true", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "41_(29 * result) + ((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "42_(29 * result)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "43_((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "true", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "44_(temp >>> 32)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "true", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "45_(((\"Range[\" + this.lower) + \",\") + this.upper) + \"]\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "46_(((\"Range[\" + this.lower) + \",\") + this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "47_((\"Range[\" + this.lower) + \",\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "48_(\"Range[\" + this.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_(\"Range(double, double): require lower (\" + lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "5_this.upper - this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "6_(this.lower / 2.0) + (this.upper / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "7_(this.lower / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "8_(this.upper / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "9_(value >= this.lower) && (value <= this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_LOGICAL_EXPRESSION": {"logical_expression_0_!(obj instanceof org.jfree.data.Range)": {"LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_LEFT": "false", "LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_RIGHT": "false", "LE10_ATOMIC_EXPRESSION_MULTIPLE_VAR_AS_BOOLEAN": "false", "LE10_ATOMIC_EXPRESSION_SAME_INVOCATION_TARGET": "false", "LE10_ATOMIC_EXPRESSION_USED_IN_INVOCATION_COMPARISION_VARIABLE": "false", "LE10_CONTAINS_ALL_INVOCATION_COMPARISION_VARIABLE": "false", "LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "true", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "false", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}, "logical_expression_1_false": {"LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_LEFT": "false", "LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_RIGHT": "false", "LE10_ATOMIC_EXPRESSION_MULTIPLE_VAR_AS_BOOLEAN": "false", "LE10_ATOMIC_EXPRESSION_SAME_INVOCATION_TARGET": "false", "LE10_ATOMIC_EXPRESSION_USED_IN_INVOCATION_COMPARISION_VARIABLE": "false", "LE10_CONTAINS_ALL_INVOCATION_COMPARISION_VARIABLE": "false", "LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "false", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "false", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "false", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "false", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}, "logical_expression_2_!(this.lower == range.lower)": {"LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "true", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "true", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}, "logical_expression_3_!(this.upper == range.upper)": {"LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "true", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "true", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}, "logical_expression_4_true": {"LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "false", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "false", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "false", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "false", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {"org.jfree.data.Range": {"C3_TYPEACCESS_ACTUAL_VAR": "false", "C4_SIMILAR_TYPEACCESS_ACTUAL_VAR": "false"}}, "FEATURES_VARS": {"lower": {"V10_VAR_TYPE_Similar_VAR": "true", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V8_VAR_OBJECT": "false", "V8_VAR_PRIMITIVE": "true", "V9_VAR_TYPE_Similar_Literal": "true"}, "obj": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "false", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "false", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "false", "V8_VAR_OBJECT": "true", "V8_VAR_PRIMITIVE": "false", "V9_VAR_TYPE_Similar_Literal": "false"}, "range": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "true", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "true", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "true", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V8_VAR_OBJECT": "true", "V8_VAR_PRIMITIVE": "false", "V9_VAR_TYPE_Similar_Literal": "false"}, "upper": {"V10_VAR_TYPE_Similar_VAR": "true", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V8_VAR_OBJECT": "false", "V8_VAR_PRIMITIVE": "true", "V9_VAR_TYPE_Similar_Literal": "true"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "Class", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "true", "S1_LOCAL_VAR_NOT_USED": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "S3_TYPE_OF_FAULTY_STATEMENT": "Method", "S4_Field_NOT_ASSIGNED": "false", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S7_OBJECT_USED_IN_ASSIGNMENT": "false", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "null", "dst_parent": "null", "dst_parent_type": "null", "dst_type": "null", "operator": "INS", "src": "if (!(this.lower == range.lower)) {\n    if ((((1 + this.lower) - (-1)) < this.upper) || (this.lower <= 0)) {\n        return false;\n    }\n}", "src_parent": "{\n    if (!(obj instanceof org.jfree.data.Range)) {\n        return false;\n    }\n    org.jfree.data.Range range = ((org.jfree.data.Range) (obj));\n    if (!(this.lower == range.lower)) {\n        if ((((1 + this.lower) - (-1)) < this.upper) || (this.lower <= 0)) {\n            return false;\n        }\n    }\n    if (!(this.upper == range.upper)) {\n        return false;\n    }\n    return true;\n}", "src_parent_type": "Block", "src_type": "If"}}, {"FEATURES_BINARYOPERATOR": {"0_lower > upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "10_(value >= this.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "11_(value <= this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "12_b0 <= this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "13_b1 > this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "14_(b0 < this.upper) && (b1 >= b0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "15_(b0 < this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "16_(b1 >= b0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "17_value > this.upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "18_value < this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "19_range1 == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "1_(((\"Range(double, double): require lower (\" + lower) + \") <= upper (\") + upper) + \").\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "20_range2 == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "21_range == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "22_value < range.getLowerBound()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "23_value > range.getUpperBound()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "24_range == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "25_length * lowerMargin": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "26_length * upperMargin": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "27_range.getLowerBound() - lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "28_range.getUpperBound() + upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "29_base.getLowerBound() + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "2_(((\"Range(double, double): require lower (\" + lower) + \") <= upper (\") + upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "30_base.getUpperBound() + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "31_value > 0.0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "32_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "33_value < 0.0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "34_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "35_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "36_(obj instanceof org.jfree.data.Range)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "true", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "37_(this.lower == range.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "38_(this.upper == range.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "39_((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "true", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_((\"Range(double, double): require lower (\" + lower) + \") <= upper (\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "40_(temp >>> 32)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "true", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "41_(29 * result) + ((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "42_(29 * result)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "43_((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "true", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "44_(temp >>> 32)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "true", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "45_(((\"Range[\" + this.lower) + \",\") + this.upper) + \"]\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "46_(((\"Range[\" + this.lower) + \",\") + this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "47_((\"Range[\" + this.lower) + \",\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "48_(\"Range[\" + this.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_(\"Range(double, double): require lower (\" + lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "5_this.upper - this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "6_(this.lower / 2.0) + (this.upper / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "7_(this.lower / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "8_(this.upper / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "9_(value >= this.lower) && (value <= this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_LOGICAL_EXPRESSION": {"logical_expression_0_!(this.lower == range.lower)": {"LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "true", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "true", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {"org.jfree.data.Range": {"C3_TYPEACCESS_ACTUAL_VAR": "false", "C4_SIMILAR_TYPEACCESS_ACTUAL_VAR": "false"}}, "FEATURES_VARS": {"lower": {"V10_VAR_TYPE_Similar_VAR": "true", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "true", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "false", "V8_VAR_PRIMITIVE": "true", "V9_VAR_TYPE_Similar_Literal": "true"}, "range": {"V10_VAR_TYPE_Similar_VAR": "true", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "true", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "true", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "true", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "true", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "true", "V8_VAR_PRIMITIVE": "false", "V9_VAR_TYPE_Similar_Literal": "false"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "If", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "Return", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "LocalVariable", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "If", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "Method", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "false", "S1_LOCAL_VAR_NOT_USED": "true", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "S3_TYPE_OF_FAULTY_STATEMENT": "If", "S4_Field_NOT_ASSIGNED": "false", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "true", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S6_METHOD_THROWS_EXCEPTION": "false", "S7_OBJECT_USED_IN_ASSIGNMENT": "true", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "!(this.lower == range.lower)", "dst_parent": "if (!(this.lower == range.lower)) {\n    if ((((1 + this.lower) - (-1)) < this.upper) || (this.lower <= 0)) {\n        return false;\n    }\n}", "dst_parent_type": "If", "dst_type": "UnaryOperator", "operator": "MOV", "src": "!(this.lower == range.lower)", "src_parent": "if (!(this.lower == range.lower)) {\n    return false;\n}", "src_parent_type": "If", "src_type": "UnaryOperator"}}, {"FEATURES_BINARYOPERATOR": {"0_lower > upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "10_(value >= this.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "11_(value <= this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "12_b0 <= this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "13_b1 > this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "14_(b0 < this.upper) && (b1 >= b0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "15_(b0 < this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "16_(b1 >= b0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "17_value > this.upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "18_value < this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "19_range1 == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "1_(((\"Range(double, double): require lower (\" + lower) + \") <= upper (\") + upper) + \").\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "20_range2 == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "21_range == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "22_value < range.getLowerBound()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "23_value > range.getUpperBound()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "24_range == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "25_length * lowerMargin": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "26_length * upperMargin": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "27_range.getLowerBound() - lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "28_range.getUpperBound() + upper": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "29_base.getLowerBound() + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "2_(((\"Range(double, double): require lower (\" + lower) + \") <= upper (\") + upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "30_base.getUpperBound() + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "31_value > 0.0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "32_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "33_value < 0.0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "34_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "35_value + delta": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "36_(obj instanceof org.jfree.data.Range)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "true", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "37_(this.lower == range.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "38_(this.upper == range.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "39_((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "true", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_((\"Range(double, double): require lower (\" + lower) + \") <= upper (\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "40_(temp >>> 32)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "true", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "41_(29 * result) + ((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "42_(29 * result)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "43_((int) (temp ^ (temp >>> 32)))": {"O1_IS_BIT": "true", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "44_(temp >>> 32)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "true", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "45_(((\"Range[\" + this.lower) + \",\") + this.upper) + \"]\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "46_(((\"Range[\" + this.lower) + \",\") + this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "47_((\"Range[\" + this.lower) + \",\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "48_(\"Range[\" + this.lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_(\"Range(double, double): require lower (\" + lower)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "5_this.upper - this.lower": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "6_(this.lower / 2.0) + (this.upper / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "7_(this.lower / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "8_(this.upper / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "9_(value >= this.lower) && (value <= this.upper)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_LOGICAL_EXPRESSION": {"logical_expression_0_!(this.lower == range.lower)": {"LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "true", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "true", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {"org.jfree.data.Range": {"C3_TYPEACCESS_ACTUAL_VAR": "false", "C4_SIMILAR_TYPEACCESS_ACTUAL_VAR": "false"}}, "FEATURES_VARS": {"lower": {"V10_VAR_TYPE_Similar_VAR": "true", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "true", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "false", "V8_VAR_PRIMITIVE": "true", "V9_VAR_TYPE_Similar_Literal": "true"}, "range": {"V10_VAR_TYPE_Similar_VAR": "true", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "true", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "true", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "true", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "true", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "true", "V8_VAR_PRIMITIVE": "false", "V9_VAR_TYPE_Similar_Literal": "false"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "If", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "Return", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "LocalVariable", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "If", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "Method", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "false", "S1_LOCAL_VAR_NOT_USED": "true", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "S3_TYPE_OF_FAULTY_STATEMENT": "If", "S4_Field_NOT_ASSIGNED": "false", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "true", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S6_METHOD_THROWS_EXCEPTION": "false", "S7_OBJECT_USED_IN_ASSIGNMENT": "true", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "{\n    return false;\n}", "dst_parent": "if ((((1 + this.lower) - (-1)) < this.upper) || (this.lower <= 0)) {\n    return false;\n}", "dst_parent_type": "If", "dst_type": "Block", "operator": "MOV", "src": "{\n    return false;\n}", "src_parent": "if (!(this.lower == range.lower)) {\n    return false;\n}", "src_parent_type": "If", "src_type": "Block"}}, {"P4J_FORMER_ABST_V_AF": "0", "P4J_FORMER_ASSIGN_CONST_AF": "0", "P4J_FORMER_ASSIGN_LHS_AF": "0", "P4J_FORMER_ASSIGN_ZERO_AF": "0", "P4J_FORMER_CALLEE_AF": "0", "P4J_FORMER_CALL_ARGUMENT_AF": "0", "P4J_FORMER_CHANGED_AF": "0", "P4J_FORMER_DEREF_AF": "0", "P4J_FORMER_FUNC_ARGUMENT_VF": "0", "P4J_FORMER_GLOBAL_VARIABLE_VF": "0", "P4J_FORMER_INDEX_AF": "0", "P4J_FORMER_INSERT_CONTROL_RF": "0", "P4J_FORMER_INSERT_GUARD_RF": "0", "P4J_FORMER_INSERT_STMT_RF": "0", "P4J_FORMER_LOCAL_VARIABLE_VF": "0", "P4J_FORMER_MEMBER_ACCESS_AF": "0", "P4J_FORMER_MEMBER_VF": "0", "P4J_FORMER_MODIFIED_SIMILAR_VF": "0", "P4J_FORMER_MODIFIED_VF": "0", "P4J_FORMER_NONZERO_CONST_VF": "0", "P4J_FORMER_OP_ADD_AF": "0", "P4J_FORMER_OP_DIV_AF": "0", "P4J_FORMER_OP_EQ_AF": "0", "P4J_FORMER_OP_GE_AF": "0", "P4J_FORMER_OP_GT_AF": "0", "P4J_FORMER_OP_LE_AF": "0", "P4J_FORMER_OP_LT_AF": "0", "P4J_FORMER_OP_MOD_AF": "0", "P4J_FORMER_OP_MUL_AF": "0", "P4J_FORMER_OP_NE_AF": "0", "P4J_FORMER_OP_SUB_AF": "0", "P4J_FORMER_REMOVE_PARTIAL_IF": "0", "P4J_FORMER_REMOVE_STMT": "0", "P4J_FORMER_REMOVE_WHOLE_BLOCK": "0", "P4J_FORMER_REMOVE_WHOLE_IF": "0", "P4J_FORMER_REPLACE_COND_RF": "0", "P4J_FORMER_REPLACE_STMT_RF": "0", "P4J_FORMER_R_STMT_ASSIGN_AF": "0", "P4J_FORMER_R_STMT_CALL_AF": "0", "P4J_FORMER_R_STMT_COND_AF": "0", "P4J_FORMER_R_STMT_CONTROL_AF": "0", "P4J_FORMER_SIZE_LITERAL_VF": "0", "P4J_FORMER_STMT_ASSIGN_AF": "0", "P4J_FORMER_STMT_CALL_AF": "0", "P4J_FORMER_STMT_COND_AF": "0", "P4J_FORMER_STMT_CONTROL_AF": "0", "P4J_FORMER_STMT_LABEL_AF": "0", "P4J_FORMER_STMT_LOOP_AF": "0", "P4J_FORMER_STRING_LITERAL_VF": "0", "P4J_FORMER_UOP_DEC_AF": "0", "P4J_FORMER_UOP_INC_AF": "0", "P4J_FORMER_ZERO_CONST_VF": "0", "P4J_LATER_ABST_V_AF": "0", "P4J_LATER_ASSIGN_CONST_AF": "0", "P4J_LATER_ASSIGN_LHS_AF": "0", "P4J_LATER_ASSIGN_ZERO_AF": "0", "P4J_LATER_CALLEE_AF": "0", "P4J_LATER_CALL_ARGUMENT_AF": "0", "P4J_LATER_CHANGED_AF": "0", "P4J_LATER_DEREF_AF": "0", "P4J_LATER_FUNC_ARGUMENT_VF": "0", "P4J_LATER_GLOBAL_VARIABLE_VF": "0", "P4J_LATER_INDEX_AF": "0", "P4J_LATER_INSERT_CONTROL_RF": "0", "P4J_LATER_INSERT_GUARD_RF": "0", "P4J_LATER_INSERT_STMT_RF": "0", "P4J_LATER_LOCAL_VARIABLE_VF": "0", "P4J_LATER_MEMBER_ACCESS_AF": "0", "P4J_LATER_MEMBER_VF": "0", "P4J_LATER_MODIFIED_SIMILAR_VF": "0", "P4J_LATER_MODIFIED_VF": "0", "P4J_LATER_NONZERO_CONST_VF": "0", "P4J_LATER_OP_ADD_AF": "0", "P4J_LATER_OP_DIV_AF": "0", "P4J_LATER_OP_EQ_AF": "0", "P4J_LATER_OP_GE_AF": "0", "P4J_LATER_OP_GT_AF": "0", "P4J_LATER_OP_LE_AF": "0", "P4J_LATER_OP_LT_AF": "0", "P4J_LATER_OP_MOD_AF": "0", "P4J_LATER_OP_MUL_AF": "0", "P4J_LATER_OP_NE_AF": "0", "P4J_LATER_OP_SUB_AF": "0", "P4J_LATER_REMOVE_PARTIAL_IF": "0", "P4J_LATER_REMOVE_STMT": "0", "P4J_LATER_REMOVE_WHOLE_BLOCK": "0", "P4J_LATER_REMOVE_WHOLE_IF": "0", "P4J_LATER_REPLACE_COND_RF": "0", "P4J_LATER_REPLACE_STMT_RF": "0", "P4J_LATER_R_STMT_ASSIGN_AF": "0", "P4J_LATER_R_STMT_CALL_AF": "0", "P4J_LATER_R_STMT_COND_AF": "0", "P4J_LATER_R_STMT_CONTROL_AF": "0", "P4J_LATER_SIZE_LITERAL_VF": "0", "P4J_LATER_STMT_ASSIGN_AF": "0", "P4J_LATER_STMT_CALL_AF": "0", "P4J_LATER_STMT_COND_AF": "0", "P4J_LATER_STMT_CONTROL_AF": "1", "P4J_LATER_STMT_LABEL_AF": "0", "P4J_LATER_STMT_LOOP_AF": "0", "P4J_LATER_STRING_LITERAL_VF": "0", "P4J_LATER_UOP_DEC_AF": "0", "P4J_LATER_UOP_INC_AF": "0", "P4J_LATER_ZERO_CONST_VF": "0", "P4J_SRC_ABST_V_AF": "0", "P4J_SRC_ASSIGN_CONST_AF": "0", "P4J_SRC_ASSIGN_LHS_AF": "0", "P4J_SRC_ASSIGN_ZERO_AF": "0", "P4J_SRC_CALLEE_AF": "0", "P4J_SRC_CALL_ARGUMENT_AF": "0", "P4J_SRC_CHANGED_AF": "0", "P4J_SRC_DEREF_AF": "0", "P4J_SRC_FUNC_ARGUMENT_VF": "0", "P4J_SRC_GLOBAL_VARIABLE_VF": "0", "P4J_SRC_INDEX_AF": "0", "P4J_SRC_INSERT_CONTROL_RF": "0", "P4J_SRC_INSERT_GUARD_RF": "0", "P4J_SRC_INSERT_STMT_RF": "1", "P4J_SRC_LOCAL_VARIABLE_VF": "0", "P4J_SRC_MEMBER_ACCESS_AF": "0", "P4J_SRC_MEMBER_VF": "0", "P4J_SRC_MODIFIED_SIMILAR_VF": "0", "P4J_SRC_MODIFIED_VF": "0", "P4J_SRC_NONZERO_CONST_VF": "0", "P4J_SRC_OP_ADD_AF": "0", "P4J_SRC_OP_DIV_AF": "0", "P4J_SRC_OP_EQ_AF": "0", "P4J_SRC_OP_GE_AF": "0", "P4J_SRC_OP_GT_AF": "0", "P4J_SRC_OP_LE_AF": "0", "P4J_SRC_OP_LT_AF": "0", "P4J_SRC_OP_MOD_AF": "0", "P4J_SRC_OP_MUL_AF": "0", "P4J_SRC_OP_NE_AF": "0", "P4J_SRC_OP_SUB_AF": "0", "P4J_SRC_REMOVE_PARTIAL_IF": "0", "P4J_SRC_REMOVE_STMT": "0", "P4J_SRC_REMOVE_WHOLE_BLOCK": "0", "P4J_SRC_REMOVE_WHOLE_IF": "0", "P4J_SRC_REPLACE_COND_RF": "0", "P4J_SRC_REPLACE_STMT_RF": "0", "P4J_SRC_R_STMT_ASSIGN_AF": "0", "P4J_SRC_R_STMT_CALL_AF": "0", "P4J_SRC_R_STMT_COND_AF": "0", "P4J_SRC_R_STMT_CONTROL_AF": "0", "P4J_SRC_SIZE_LITERAL_VF": "0", "P4J_SRC_STMT_ASSIGN_AF": "0", "P4J_SRC_STMT_CALL_AF": "0", "P4J_SRC_STMT_COND_AF": "0", "P4J_SRC_STMT_CONTROL_AF": "1", "P4J_SRC_STMT_LABEL_AF": "0", "P4J_SRC_STMT_LOOP_AF": "0", "P4J_SRC_STRING_LITERAL_VF": "0", "P4J_SRC_UOP_DEC_AF": "0", "P4J_SRC_UOP_INC_AF": "0", "P4J_SRC_ZERO_CONST_VF": "0"}, {"repairPatterns": {"codeMove": 1, "condBlockExcAdd": 0, "condBlockOthersAdd": 0, "condBlockRem": 0, "condBlockRetAdd": 0, "constChange": 0, "copyPaste": 0, "expArithMod": 0, "expLogicExpand": 0, "expLogicMod": 0, "expLogicReduce": 0, "missNullCheckN": 0, "missNullCheckP": 0, "notClassified": 0, "singleLine": 1, "unwrapIfElse": 0, "unwrapMethod": 0, "unwrapTryCatch": 0, "wrapsElse": 0, "wrapsIf": 0, "wrapsIfElse": 0, "wrapsLoop": 0, "wrapsMethod": 0, "wrapsTryCatch": 0, "wrongMethodRef": 0, "wrongVarRef": 0}}, {"UpdateLiteral": 0, "addLineNo": 2, "addThis": 0, "condLogicReduce": 0, "dupArgsInvocation": 0, "ifTrue": 0, "insertBooleanLiteral": 0, "insertIfFalse": 0, "insertNewConstLiteral": 1, "patchedFileNo": 1, "removeNullinCond": 0, "rmLineNo": 0, "updIfFalse": 0}], "file_name": "chart21"}], "id": "patch91"}