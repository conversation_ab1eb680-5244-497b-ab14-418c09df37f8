--- /src/com/google/javascript/rhino/jstype/JSType.java
+++ /src/com/google/javascript/rhino/jstype/JSType.java
@@ -160,7 +160,9 @@
   }
 
   public final boolean isEmptyType() {
-    return isNoType() || isNoObjectType() || isNoResolvedType();
+    return isNoType() || isNoObjectType() || isNoResolvedType() ||
+        (registry.getNativeFunctionType(
+             JSTypeNative.LEAST_FUNCTION_TYPE) == this);
   }
 
   public boolean isNumberObjectType() {

