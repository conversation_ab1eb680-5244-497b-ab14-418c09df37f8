--- /src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
+++ /src/main/java/org/apache/commons/compress/archivers/tar/TarUtils.java
@@ -95,10 +95,11 @@
         int          end = offset + length;
 
         for (int i = offset; i < end; ++i) {
-            if (buffer[i] == 0) {
+            byte b = buffer[i];
+            if (b == 0) { // Trailing null
                 break;
             }
-            result.append((char) buffer[i]);
+            result.append((char) (b & 0xFF)); // Allow for sign-extension
         }
 
         return result.toString();

