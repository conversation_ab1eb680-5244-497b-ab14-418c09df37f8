--- /src/com/google/javascript/jscomp/CheckSideEffects.java
+++ /src/com/google/javascript/jscomp/CheckSideEffects.java
@@ -98,7 +98,7 @@
     // Do not try to remove a block or an expr result. We already handle
     // these cases when we visit the child, and the peephole passes will
     // fix up the tree in more clever ways when these are removed.
-    if (n.isExprResult()) {
+    if (n.isExprResult() || n.isBlock()) {
       return;
     }
 
@@ -110,24 +110,7 @@
 
     boolean isResultUsed = NodeUtil.isExpressionResultUsed(n);
     boolean isSimpleOp = NodeUtil.isSimpleOperatorType(n.getType());
-    if (parent.getType() == Token.COMMA) {
-      if (isResultUsed) {
-        return;
-      }
-      if (n == parent.getLastChild()) {
-        for (Node an : parent.getAncestors()) {
-          int ancestorType = an.getType();
-          if (ancestorType == Token.COMMA) continue;
-          if (ancestorType != Token.EXPR_RESULT && ancestorType != Token.BLOCK) return;
-          else break;
-        }
-      }
-    } else if (parent.getType() != Token.EXPR_RESULT && parent.getType() != Token.BLOCK) {
-      if (! (parent.getType() == Token.FOR && parent.getChildCount() == 4 && (n == parent.getFirstChild() || n == parent.getFirstChild().getNext().getNext()))) {
-        return;
-      }
-    }
-    if (
+    if (!isResultUsed &&
         (isSimpleOp || !NodeUtil.mayHaveSideEffects(n, t.getCompiler()))) {
       String msg = "This code lacks side-effects. Is there a bug?";
       if (n.isString()) {

