--- /src/com/google/javascript/jscomp/CommandLineRunner.java
+++ /src/com/google/javascript/jscomp/CommandLineRunner.java
@@ -859,6 +859,7 @@
       // so we might as well inline it. But shut off the i18n warnings,
       // because the user didn't really ask for i18n.
       options.messageBundle = new EmptyMessageBundle();
+      options.setWarningLevel(JsMessageVisitor.MSG_CONVENTIONS, CheckLevel.OFF);
     }
 
     return options;

