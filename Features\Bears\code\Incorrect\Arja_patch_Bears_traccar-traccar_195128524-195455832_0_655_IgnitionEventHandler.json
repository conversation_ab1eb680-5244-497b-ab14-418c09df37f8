{"file_name": "<PERSON><PERSON><PERSON>_<PERSON>_Bears_traccar-traccar_195128524-195455832_0_655_IgnitionEventHandler", "id": "<PERSON><PERSON><PERSON>_<PERSON>_Bears_traccar-traccar_195128524-195455832_0_655_IgnitionEventHandler", "features": [{"P4J_LATER_INSERT_CONTROL_RF": "false", "P4J_LATER_INSERT_GUARD_RF": "false", "P4J_LATER_INSERT_STMT_RF": "false", "P4J_LATER_REPLACE_COND_RF": "false", "P4J_LATER_REPLACE_STMT_RF": "false", "P4J_LATER_REMOVE_PARTIAL_IF": "false", "P4J_LATER_REMOVE_STMT": "false", "P4J_LATER_REMOVE_WHOLE_IF": "false", "P4J_LATER_REMOVE_WHOLE_BLOCK": "false", "P4J_LATER_OP_ADD_AF": "false", "P4J_LATER_OP_SUB_AF": "false", "P4J_LATER_OP_MUL_AF": "false", "P4J_LATER_OP_DIV_AF": "false", "P4J_LATER_OP_MOD_AF": "false", "P4J_LATER_OP_LE_AF": "false", "P4J_LATER_OP_LT_AF": "false", "P4J_LATER_OP_GE_AF": "false", "P4J_LATER_OP_GT_AF": "false", "P4J_LATER_OP_EQ_AF": "false", "P4J_LATER_OP_NE_AF": "false", "P4J_LATER_UOP_INC_AF": "false", "P4J_LATER_UOP_DEC_AF": "false", "P4J_LATER_ASSIGN_LHS_AF": "true", "P4J_LATER_ASSIGN_ZERO_AF": "false", "P4J_LATER_ASSIGN_CONST_AF": "false", "P4J_LATER_CHANGED_AF": "true", "P4J_LATER_DEREF_AF": "false", "P4J_LATER_INDEX_AF": "false", "P4J_LATER_MEMBER_ACCESS_AF": "true", "P4J_LATER_CALLEE_AF": "true", "P4J_LATER_CALL_ARGUMENT_AF": "true", "P4J_LATER_ABST_V_AF": "false", "P4J_LATER_STMT_LABEL_AF": "false", "P4J_LATER_STMT_LOOP_AF": "false", "P4J_LATER_STMT_ASSIGN_AF": "true", "P4J_LATER_STMT_CALL_AF": "true", "P4J_LATER_STMT_COND_AF": "true", "P4J_LATER_STMT_CONTROL_AF": "true", "P4J_LATER_R_STMT_ASSIGN_AF": "false", "P4J_LATER_R_STMT_CALL_AF": "false", "P4J_LATER_R_STMT_COND_AF": "false", "P4J_LATER_R_STMT_CONTROL_AF": "false", "P4J_LATER_MODIFIED_VF": "false", "P4J_LATER_MODIFIED_SIMILAR_VF": "false", "P4J_LATER_FUNC_ARGUMENT_VF": "false", "P4J_LATER_MEMBER_VF": "false", "P4J_LATER_LOCAL_VARIABLE_VF": "false", "P4J_LATER_GLOBAL_VARIABLE_VF": "false", "P4J_LATER_ZERO_CONST_VF": "false", "P4J_LATER_NONZERO_CONST_VF": "false", "P4J_LATER_STRING_LITERAL_VF": "false", "P4J_LATER_SIZE_LITERAL_VF": "false", "P4J_SRC_INSERT_CONTROL_RF": "false", "P4J_SRC_INSERT_GUARD_RF": "false", "P4J_SRC_INSERT_STMT_RF": "false", "P4J_SRC_REPLACE_COND_RF": "false", "P4J_SRC_REPLACE_STMT_RF": "false", "P4J_SRC_REMOVE_PARTIAL_IF": "false", "P4J_SRC_REMOVE_STMT": "false", "P4J_SRC_REMOVE_WHOLE_IF": "true", "P4J_SRC_REMOVE_WHOLE_BLOCK": "false", "P4J_SRC_OP_ADD_AF": "false", "P4J_SRC_OP_SUB_AF": "false", "P4J_SRC_OP_MUL_AF": "false", "P4J_SRC_OP_DIV_AF": "false", "P4J_SRC_OP_MOD_AF": "false", "P4J_SRC_OP_LE_AF": "false", "P4J_SRC_OP_LT_AF": "false", "P4J_SRC_OP_GE_AF": "false", "P4J_SRC_OP_GT_AF": "false", "P4J_SRC_OP_EQ_AF": "false", "P4J_SRC_OP_NE_AF": "false", "P4J_SRC_UOP_INC_AF": "false", "P4J_SRC_UOP_DEC_AF": "false", "P4J_SRC_ASSIGN_LHS_AF": "true", "P4J_SRC_ASSIGN_ZERO_AF": "false", "P4J_SRC_ASSIGN_CONST_AF": "false", "P4J_SRC_CHANGED_AF": "true", "P4J_SRC_DEREF_AF": "false", "P4J_SRC_INDEX_AF": "false", "P4J_SRC_MEMBER_ACCESS_AF": "true", "P4J_SRC_CALLEE_AF": "true", "P4J_SRC_CALL_ARGUMENT_AF": "true", "P4J_SRC_ABST_V_AF": "false", "P4J_SRC_STMT_LABEL_AF": "false", "P4J_SRC_STMT_LOOP_AF": "false", "P4J_SRC_STMT_ASSIGN_AF": "true", "P4J_SRC_STMT_CALL_AF": "true", "P4J_SRC_STMT_COND_AF": "true", "P4J_SRC_STMT_CONTROL_AF": "false", "P4J_SRC_R_STMT_ASSIGN_AF": "false", "P4J_SRC_R_STMT_CALL_AF": "false", "P4J_SRC_R_STMT_COND_AF": "false", "P4J_SRC_R_STMT_CONTROL_AF": "false", "P4J_SRC_MODIFIED_VF": "false", "P4J_SRC_MODIFIED_SIMILAR_VF": "false", "P4J_SRC_FUNC_ARGUMENT_VF": "true", "P4J_SRC_MEMBER_VF": "false", "P4J_SRC_LOCAL_VARIABLE_VF": "true", "P4J_SRC_GLOBAL_VARIABLE_VF": "true", "P4J_SRC_ZERO_CONST_VF": "false", "P4J_SRC_NONZERO_CONST_VF": "false", "P4J_SRC_STRING_LITERAL_VF": "false", "P4J_SRC_SIZE_LITERAL_VF": "false", "P4J_FORMER_INSERT_CONTROL_RF": "false", "P4J_FORMER_INSERT_GUARD_RF": "false", "P4J_FORMER_INSERT_STMT_RF": "false", "P4J_FORMER_REPLACE_COND_RF": "false", "P4J_FORMER_REPLACE_STMT_RF": "false", "P4J_FORMER_REMOVE_PARTIAL_IF": "false", "P4J_FORMER_REMOVE_STMT": "false", "P4J_FORMER_REMOVE_WHOLE_IF": "false", "P4J_FORMER_REMOVE_WHOLE_BLOCK": "false", "P4J_FORMER_OP_ADD_AF": "false", "P4J_FORMER_OP_SUB_AF": "false", "P4J_FORMER_OP_MUL_AF": "false", "P4J_FORMER_OP_DIV_AF": "false", "P4J_FORMER_OP_MOD_AF": "false", "P4J_FORMER_OP_LE_AF": "false", "P4J_FORMER_OP_LT_AF": "false", "P4J_FORMER_OP_GE_AF": "false", "P4J_FORMER_OP_GT_AF": "false", "P4J_FORMER_OP_EQ_AF": "false", "P4J_FORMER_OP_NE_AF": "true", "P4J_FORMER_UOP_INC_AF": "false", "P4J_FORMER_UOP_DEC_AF": "false", "P4J_FORMER_ASSIGN_LHS_AF": "true", "P4J_FORMER_ASSIGN_ZERO_AF": "false", "P4J_FORMER_ASSIGN_CONST_AF": "false", "P4J_FORMER_CHANGED_AF": "true", "P4J_FORMER_DEREF_AF": "false", "P4J_FORMER_INDEX_AF": "false", "P4J_FORMER_MEMBER_ACCESS_AF": "false", "P4J_FORMER_CALLEE_AF": "true", "P4J_FORMER_CALL_ARGUMENT_AF": "true", "P4J_FORMER_ABST_V_AF": "false", "P4J_FORMER_STMT_LABEL_AF": "false", "P4J_FORMER_STMT_LOOP_AF": "false", "P4J_FORMER_STMT_ASSIGN_AF": "true", "P4J_FORMER_STMT_CALL_AF": "true", "P4J_FORMER_STMT_COND_AF": "true", "P4J_FORMER_STMT_CONTROL_AF": "false", "P4J_FORMER_R_STMT_ASSIGN_AF": "false", "P4J_FORMER_R_STMT_CALL_AF": "false", "P4J_FORMER_R_STMT_COND_AF": "false", "P4J_FORMER_R_STMT_CONTROL_AF": "false", "P4J_FORMER_MODIFIED_VF": "false", "P4J_FORMER_MODIFIED_SIMILAR_VF": "false", "P4J_FORMER_FUNC_ARGUMENT_VF": "false", "P4J_FORMER_MEMBER_VF": "false", "P4J_FORMER_LOCAL_VARIABLE_VF": "false", "P4J_FORMER_GLOBAL_VARIABLE_VF": "false", "P4J_FORMER_ZERO_CONST_VF": "false", "P4J_FORMER_NONZERO_CONST_VF": "false", "P4J_FORMER_STRING_LITERAL_VF": "false", "P4J_FORMER_SIZE_LITERAL_VF": "false"}]}