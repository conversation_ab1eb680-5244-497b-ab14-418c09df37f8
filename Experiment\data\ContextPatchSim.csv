id,label,methodCallWithNullGuard,faultyClassExceptionType,methodCallWithTryCatch,typeOfFaultyStmtAfter1,typeOfFaultyStmtAfter2,typeOfFaultyStmtAfter3,typeOfFaultyStmtBefore1,typeOfFaultyStmtBefore2,typeOfFaultyStmtBefore3,typeOfFaultyStmtParent,hasObjectiveMethodCall,hasInvocationsProneException,inSynchronizedMethod,localVarNotAssigned,localVarNotUsed,similarObjectTypeWithNormalGuard,similarObjectTypeWithNullGuard,typeOfFaultyStmt,fieldNotAssigned,fieldNotUsed,similarPrimitiveTypeWithNormalGuard,similarPrimitiveTypeWithNullGuard,methodThrowsException,objectUsedInAssignment,primitiveUsedInAssignment,methodCallWithNormalGuard
Patch196Math_5,0, 0, 0, 0, "Return", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch45Math_50,0, 0, 0, 0, "Break", "", "", "", "", "", "Case", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch194Math_3,0, 0, 0, 0, "LocalVariable", "LocalVariable", "LocalVariable", "LocalVariable", "For", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch47Math_53,0, 0, 0, 0, "Return", "", "", "Invocation", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch209Math_99,0, 0, 0, 0, "If", "Return", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch3Chart_3,1, 0, 0, 0, "Assignment", "LocalVariable", "If", "If", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "Invocation", 0, 0, 0, 0, 0, 0, 0, 0
PatchHDRepair7Math_70,0, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 1, 0, 0, 0
Patch190Lang_7,0, 0, 0, 0, "Return", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch189Chart_19,0, 0, 0, 0, "Return", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch191Lang_24,0, 0, 0, 0, "Return", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch188Chart_14,0, 0, 0, 0, "LocalVariable", "If", "Return", "If", "LocalVariable", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch205Math_89,0, 0, 0, 0, "Invocation", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch25Lang_55,0, 0, 0, 0, "Assignment", "", "", "If", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch46Math_50,0, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "Switch", 0, 0, 0, 0, 0, 0, 0, 0
Patch54Math_73,0, 0, 0, 0, "", "", "", "If", "If", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 1, 0, 0, 0
Patch203Math_82,0, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch29Math_5,0, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 0, 0, 0, 0
Patch21Lang_44,0, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "If", "If", 0, 0, 0, 0, 0, 0, 0, "Switch", 0, 0, 0, 0, 1, 0, 0, 0
Patch207Math_93,0, 0, 0, 0, "", "", "", "", "", "", "Class", 0, 0, 0, 0, 0, 0, 0, "Method", 0, 0, 0, 0, 0, 0, 0, 0
PatchHDRepair4Math_34,0, 0, 0, 0, "", "", "", "", "", "", "Method", 1, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 0, 0, 0, 0
Patch51Math_70,0, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 1, 0, 0, 0
Patch44Math_50,0, 0, 0, 0, "Break", "", "", "", "", "", "Case", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch206Math_90,0, 0, 0, 0, "", "", "", "If", "LocalVariable", "", "Method", 1, 0, 0, 0, 0, 0, 0, "Try", 0, 0, 0, 0, 0, 0, 0, 0
Patch210Time_15,0, 0, 0, 0, "LocalVariable", "If", "Return", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "Switch", 0, 0, 0, 0, 0, 0, 0, 0
Patch197Math_25,0, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch195Math_4,0, 0, 0, 0, "LocalVariable", "LocalVariable", "If", "LocalVariable", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch204Math_85,0, 0, 0, 0, "Return", "", "", "Do", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch7Chart_5,0, 0, 0, 0, "If", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
PatchHDRepair10Time_19,0, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch42Math_49,1, 0, 0, 0, "", "", "", "Invocation", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch154Math_2,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch17Chart_25,1, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "If", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch9Chart_13,1, 0, 0, 0, "Assignment", "Assignment", "LocalVariable", "LocalVariable", "If", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch83Time_11,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch10Chart_13,1, 0, 0, 0, "", "", "", "", "", "", "Class", 1, 0, 0, 0, 0, 0, 0, "Method", 0, 0, 0, 0, 0, 0, 0
Patch182Time_11,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch32Math_28,1, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "", "For", 1, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
PatchHDRepair6Math_53,1, 0, 0, 0, "", "", "", "Invocation", "", "", "Method", 1, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 1, 0, 0, 0
Patch201Math_73,1, 0, 0, 0, "Return", "", "", "If", "If", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch80Time_4,1, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 0, 0, 0, 0
Patch202Math_81,1, 0, 0, 0, "If", "", "", "LocalVariable", "If", "LocalVariable", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch1Chart_1,1, 0, 0, 0, "", "", "", "", "", "", "Class", 1, 0, 0, 0, 0, 0, 0, "Method", 0, 0, 0, 0, 0, 0, 0
Patch186Time_18,1, 0, 0, 0, "Return", "", "", "Invocation", "Invocation", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch166Math_50,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch16Chart_25,1, 0, 0, 0, "If", "LocalVariable", "LocalVariable", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch72Math_85,1, 0, 0, 0, "Return", "", "", "Do", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch63Math_81,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch199Math_35,0, 0, 0, 0, "LocalVariable", "LocalVariable", "ForEach", "Assignment", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch176Math_88,1, 0, 0, 0, "", "", "", "", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch90Chart_17,1, 0, 0, 0, "LocalVariable", "Assignment", "If", "If", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch5Chart_5,1, 0, 0, 0, "If", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch58Math_78,1, 0, 0, 0, "", "", "", "If", "If", "LocalVariable", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch22Lang_46,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "Switch", 0, 0, 0, 0, 1, 0, 0, 0
Patch11Chart_13,1, 0, 0, 0, "Assignment", "Assignment", "LocalVariable", "Assignment", "If", "Assignment", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
PatchHDRepair2Lang_6,1, 0, 0, 0, "If", "LocalVariable", "LocalVariable", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch74Math_88,1, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch192Lang_35,1, 0, 0, 0, "Return", "", "", "Assignment", "LocalVariable", "If", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch19Chart_26,1, 0, 0, 0, "", "", "", "If", "If", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch168Math_58,1, 0, 0, 0, "", "", "", "If", "If", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch6Chart_5,1, 0, 0, 0, "If", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch208Math_97,1, 0, 0, 0, "Return", "", "", "LocalVariable", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch92Chart_25,1, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "If", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch183Time_12,1, 0, 0, 0, "If", "Return", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch53Math_71,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch73Math_87,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch67Math_82,1, 0, 0, 0, "", "", "", "", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch2Chart_1,1, 0, 0, 0, "", "", "", "", "", "", "Class", 1, 0, 0, 0, 0, 0, 0, "Method", 0, 0, 0, 0, 0, 0, 0
Patch68Math_84,1, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "While", 0, 0, 0, 0, 1, 0, 0, 0
Patch14Chart_21,1, 0, 0, 0, "If", "Return", "", "LocalVariable", "If", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch18Chart_26,1, 0, 0, 0, "Invocation", "LocalVariable", "Invocation", "If", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch152Lang_53,1, 0, 0, 0, "LocalVariable", "For", "Throw", "If", "LocalVariable", "If", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch159Math_33,1, 0, 0, 0, "", "", "", "LocalVariable", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch8Chart_7,1, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 0, 0, 0, 0
PatchHDRepair3Math_22,1, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 0, 0, 0, 0
PatchHDRepair8Math_82,1, 0, 0, 0, "", "", "", "LocalVariable", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch30Math_8,1, 0, 0, 0, "", "", "", "", "", "", "Class", 1, 0, 0, 0, 0, 0, 0, "Method", 0, 0, 0, 0, 0, 0, 0
Patch78Math_104,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch20Lang_39,1, 0, 0, 0, "Assignment", "LocalVariable", "While", "LocalVariable", "LocalVariable", "If", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch171Math_78,1, 0, 0, 0, "If", "If", "", "LocalVariable", "LocalVariable", "LocalVariable", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch79Math_105,1, 0, 0, 0, "OperatorAssignment", "OperatorAssignment", "UnaryOperator", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch41Math_49,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch75Math_95,1, 0, 0, 0, "", "", "", "Assignment", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "Return", 0, 0, 0, 0, 0, 0, 0, 0
Patch161Math_40,1, 0, 0, 0, "Assignment", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch37Math_40,1, 0, 0, 0, "LocalVariable", "LocalVariable", "LocalVariable", "LocalVariable", "If", "LocalVariable", "While", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch81Time_4,1, 0, 0, 0, "Return", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch169Math_69,1, 0, 0, 0, "Invocation", "", "", "", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "For", 0, 0, 0, 0, 0, 0, 0, 0
Patch24Lang_53,1, 0, 0, 0, "LocalVariable", "For", "Throw", "If", "LocalVariable", "If", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch185Time_16,1, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "LocalVariable", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch163Math_42,1, 0, 0, 0, "", "", "", "LocalVariable", "", "", "For", 1, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch77Math_97,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch13Chart_15,1, 0, 0, 0, "LocalVariable", "Invocation", "LocalVariable", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch181Time_7,1, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "LocalVariable", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch65Math_82,1, 0, 0, 0, "", "", "", "", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch62Math_81,1, 0, 0, 0, "Assignment", "For", "Assignment", "LocalVariable", "LocalVariable", "LocalVariable", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch93Chart_26,1, 0, 0, 0, "If", "Invocation", "LocalVariable", "LocalVariable", "LocalVariable", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch150Lang_44,1, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "If", "If", 0, 0, 0, 0, 0, 0, 0, "Switch", 0, 0, 0, 0, 1, 0, 0, 0
Patch187Time_19,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch12Chart_15,1, 0, 0, 0, "Invocation", "Invocation", "", "If", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "Invocation", 0, 0, 0, 0, 0, 0, 0, 0
Patch198Math_28,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch33Math_28,1, 0, 0, 0, "", "", "", "", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch153Lang_58,1, 0, 0, 0, "", "", "", "LocalVariable", "LocalVariable", "If", "If", 0, 0, 0, 0, 0, 0, 0, "Switch", 0, 0, 0, 0, 1, 0, 0, 0
Patch170Math_73,1, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch173Math_81,1, 0, 0, 0, "Break", "", "", "", "", "", "Case", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch28Math_2,1, 0, 0, 0, "Return", "", "", "LocalVariable", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch82Time_11,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch175Math_87,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch59Math_80,1, 0, 0, 0, "Return", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch23Lang_51,1, 0, 0, 0, "", "", "", "If", "LocalVariable", "", "Case", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch36Math_33,1, 0, 0, 0, "", "", "", "LocalVariable", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch49Math_58,1, 0, 0, 0, "", "", "", "If", "Assignment", "If", "Method", 0, 0, 0, 0, 0, 0, 0, "For", 0, 0, 0, 0, 0, 0, 0, 0
Patch165Math_49,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
PatchHDRepair9Math_85,1, 0, 0, 0, "Return", "", "", "Do", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch43Math_49,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch157Math_24,1, 0, 0, 0, "Return", "", "", "LocalVariable", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "For", 0, 0, 0, 0, 0, 0, 0, 0
Patch174Math_85,1, 0, 0, 0, "Return", "", "", "Do", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch162Math_41,1, 0, 0, 0, "Return", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch31Math_8,1, 0, 0, 0, "", "", "", "", "", "", "Class", 1, 0, 0, 0, 0, 0, 0, "Method", 0, 0, 0, 0, 0, 0, 0
Patch151Lang_51,1, 0, 0, 0, "", "", "", "If", "LocalVariable", "", "Case", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch55Math_73,1, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch64Math_81,1, 0, 0, 0, "If", "", "", "LocalVariable", "If", "LocalVariable", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch38Math_40,1, 0, 0, 0, "Assignment", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch180Time_4,1, 0, 0, 0, "If", "LocalVariable", "Assignment", "LocalVariable", "If", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch4Chart_3,1, 0, 0, 0, "", "", "", "While", "LocalVariable", "LocalVariable", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch48Math_57,1, 0, 0, 0, "", "", "", "", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch156Math_7,1, 0, 0, 0, "Assignment", "Invocation", "Invocation", "LocalVariable", "If", "ForEach", "While", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch69Math_84,1, 0, 0, 0, "", "", "", "", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "While", 0, 0, 0, 0, 1, 0, 0, 0
Patch89Chart_13,1, 0, 0, 0, "Assignment", "Assignment", "LocalVariable", "Assignment", "If", "Assignment", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch15Chart_25,1, 0, 0, 0, "", "", "", "", "", "", "Class", 0, 0, 0, 0, 0, 0, 0, "Method", 0, 0, 0, 0, 0, 0, 0
Patch193Lang_39,1, 0, 0, 0, "Assignment", "If", "", "", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch27Math_2,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "Assignment", 0, 0, 0, 0, 1, 0, 0, 0
Patch66Math_82,1, 0, 0, 0, "", "", "", "", "", "", "For", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch167Math_57,1, 0, 0, 0, "Return", "", "", "If", "LocalVariable", "If", "Method", 0, 0, 0, 0, 0, 0, 0, "For", 0, 0, 0, 0, 0, 0, 0, 0
Patch184Time_14,1, 0, 0, 0, "", "", "", "If", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch88Chart_9,1, 0, 0, 0, "LocalVariable", "Assignment", "If", "If", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 1, 0, 0, 0
Patch158Math_28,1, 0, 0, 0, "", "", "", "", "", "", "If", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch160Math_39,1, 0, 0, 0, "LocalVariable", "If", "For", "LocalVariable", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "For", 0, 0, 0, 0, 0, 0, 0, 0
Patch76Math_95,1, 0, 0, 0, "Return", "", "", "Assignment", "LocalVariable", "LocalVariable", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch155Math_4,1, 0, 0, 0, "Return", "", "", "LocalVariable", "", "", "Method", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
Patch84Time_11,1, 0, 0, 0, "", "", "", "Assignment", "Assignment", "If", "While", 0, 0, 0, 0, 0, 0, 0, "If", 0, 0, 0, 0, 0, 0, 0, 0
