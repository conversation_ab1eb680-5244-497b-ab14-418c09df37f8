{"cells": [{"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["from pandas import DataFrame, read_excel\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.metrics import confusion_matrix\n", "#IMPORT MODELS\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.ensemble import AdaBoostClassifier\n", "from sklearn import svm\n", "from sklearn.metrics import roc_auc_score\n", "from sklearn.metrics import accuracy_score\n", "from sklearn.metrics import fbeta_score\n", "from sklearn.metrics import recall_score\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.decomposition import PCA\n", "import seaborn as sns\n", "from sklearn.metrics import recall_score\n", "from sklearn.metrics import f1_score\n", "from sklearn.metrics import precision_score\n", "from sklearn.metrics import accuracy_score\n", "\n", "import matplotlib.pyplot as plt\n", "import itertools\n", "def plot_confusion_matrix(cm, classes,\n", "                          title='Confusion matrix',\n", "                          cmap=plt.cm.binary):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    \"\"\"\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "    plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=0)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        plt.text(j, i, cm[i, j],\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "\n", "    plt.tight_layout()\n", "    plt.ylabel('True label')\n", "    plt.xlabel('Predicted label')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loading data"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["training_list= [\"./data/ase20.csv\"]\n", "training = pd.DataFrame()\n", "\n", "for f in training_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    training = training.append(data, ignore_index=True)\n", "    \n", "    \n", "training=training.iloc[:,:204]\n"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (902, 204)\n", "overfitting in train (654, 204)\n", "correct in train (248, 204)\n", "====================\n"]}], "source": ["#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["# 202 features\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# process category features"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['patchedFileNo', 'addLineNo', 'rmLineNo', 'insertIfFalse', 'updIfFalse',\n", "       'ifFalse', 'dupArgsInvocation', 'remove<PERSON><PERSON>inCond', 'condLogicReduce',\n", "       'insertBooleanLiteral',\n", "       ...\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Class',\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Constructor',\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Do',\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ For',\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ ForEach',\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ If',\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Method',\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Package',\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Try',\n", "       'S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ While'],\n", "      dtype='object', length=302)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patchedFileNo</th>\n", "      <th>addLineNo</th>\n", "      <th>rmLineNo</th>\n", "      <th>insertIfFalse</th>\n", "      <th>updIfFalse</th>\n", "      <th>ifFalse</th>\n", "      <th>dupArgsInvocation</th>\n", "      <th>remove<PERSON><PERSON><PERSON><PERSON>ond</th>\n", "      <th>condLogicReduce</th>\n", "      <th>insertBooleanLiteral</th>\n", "      <th>...</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Class</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Constructor</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Do</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ For</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ ForEach</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ If</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Method</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Package</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Try</th>\n", "      <th>S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ While</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>21.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 302 columns</p>\n", "</div>"], "text/plain": ["   patchedFileNo  addLineNo  rmLineNo  insertIfFalse  updIfFalse  ifFalse  \\\n", "0            1.0        1.0       1.0            0.0         0.0      0.0   \n", "1            1.0        0.0      21.0            0.0         0.0      0.0   \n", "\n", "   dupArgsInvocation  removeNullinCond  condLogicReduce  insertBooleanLiteral  \\\n", "0                0.0               0.0              0.0                   0.0   \n", "1                0.0               0.0              0.0                   0.0   \n", "\n", "   ...  S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Class  \\\n", "0  ...                                           0.0   \n", "1  ...                                           0.0   \n", "\n", "   S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Constructor  \\\n", "0                                                0.0    \n", "1                                                0.0    \n", "\n", "   S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Do  \\\n", "0                                        0.0   \n", "1                                        0.0   \n", "\n", "   S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ For  \\\n", "0                                         0.0   \n", "1                                         1.0   \n", "\n", "   S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ ForEach  \\\n", "0                                             1.0   \n", "1                                             0.0   \n", "\n", "   S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ If  \\\n", "0                                        0.0   \n", "1                                        0.0   \n", "\n", "   S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Method  \\\n", "0                                            0.0   \n", "1                                            0.0   \n", "\n", "   S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Package  \\\n", "0                                             0.0   \n", "1                                             0.0   \n", "\n", "   S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ Try  \\\n", "0                                         0.0   \n", "1                                         0.0   \n", "\n", "   S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1_ While  \n", "0                                           0.0  \n", "1                                           0.0  \n", "\n", "[2 rows x 302 columns]"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train = pd.get_dummies(X_train)\n", "X_train.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "\n", "X_train.head(2)"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Length of X (train): 902 | Length of y (train): 902\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/model_selection/_search.py:282: UserWarning: The total space of parameters 1 is smaller than n_iter=10. Running 1 iterations. For exhaustive searches, use GridSearchCV.\n", "  % (grid_size, self.n_iter, grid_size), UserWarning)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["---------------------------------------------------------------------------------------------------------------------------------------\n", "\n", "accuracy: 0.8913797313797314\n", "precision: 0.9058874751702518\n", "recall: 0.9495571095571096\n", "f1: 0.9269271057952748\n", "tp: 621\n", "tn: 183\n", "fp: 65\n", "fn: 33\n", "---------------------------------------------------------------------------------------------------------------------------------------\n"]}], "source": ["from imblearn.over_sampling import SMOTE\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "from imblearn.pipeline import make_pipeline as imbalanced_make_pipeline\n", "from sklearn.model_selection import GridSearchCV, cross_val_score, StratifiedKFold, learning_curve\n", "random_state = 42\n", "sss = StratifiedKFold(n_splits=10, random_state=42, shuffle=False)\n", "from sklearn import svm\n", "\n", "\n", "\n", "# X_train = X_train.values\n", "# Y_train = Y_train.values\n", "\n", "\n", "print('Length of X (train): {} | Length of y (train): {}'.format(len(X_train), len(Y_train)))\n", "\n", "# List to append the score and then find the average\n", "accuracy_lst = []\n", "precision_lst = []\n", "recall_lst = []\n", "f1_lst = []\n", "auc_lst = []\n", "tnsum = []\n", "fpsum= []\n", "fnsum= []\n", "tpsum = []\n", "\n", "rf = RandomForestClassifier(bootstrap=False, random_state=42)\n", "\n", "\n", "log_reg_params = {}\n", "\n", "rand_log_reg = RandomizedSearchCV(rf, log_reg_params, n_iter=10)\n", "\n", "# Implementing SMOTE Technique \n", "# Cross Validating the right way\n", "# Parameters\n", "for train, test in sss.split(X_train, Y_train):\n", "    pipeline = imbalanced_make_pipeline(SMOTE(sampling_strategy='minority'), rand_log_reg) # SMOTE happens during Cross Validation not before..\n", "    \n", "    model = pipeline.fit(X_train[train], Y_train[train])\n", "    best_est = rand_log_reg.best_estimator_\n", "    prediction = best_est.predict(X_train[test])\n", "    \n", "    accuracy_lst.append(pipeline.score(X_train[test], Y_train[test]))\n", "    precision_lst.append(precision_score(Y_train[test], prediction))\n", "    recall_lst.append(recall_score(Y_train[test], prediction))\n", "    f1_lst.append(f1_score(Y_train[test], prediction))\n", "    auc_lst.append(roc_auc_score(Y_train[test], prediction))\n", "\n", "\n", "    cnf_matrix = confusion_matrix(Y_train[test],prediction)\n", "    tn, fp, fn, tp = cnf_matrix.ravel()\n", "    tnsum.append(tn)\n", "    fpsum.append(fp)\n", "    fnsum.append(fn)\n", "    tpsum.append(tp)\n", "\n", "\n", "    \n", "print('---' * 45)\n", "print('')\n", "print(\"accuracy: {}\".format(np.mean(accuracy_lst)))\n", "print(\"precision: {}\".format(np.mean(precision_lst)))\n", "print(\"recall: {}\".format(np.mean(recall_lst)))\n", "print(\"f1: {}\".format(np.mean(f1_lst)))\n", "print(\"tp: {}\".format(np.sum(tpsum)))\n", "print(\"tn: {}\".format(np.sum(tnsum)))\n", "print(\"fp: {}\".format(np.sum(fpsum)))\n", "print(\"fn: {}\".format(np.sum(fnsum)))\n", "\n", "\n", "print('---' * 45)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.10"}}, "nbformat": 4, "nbformat_minor": 2}