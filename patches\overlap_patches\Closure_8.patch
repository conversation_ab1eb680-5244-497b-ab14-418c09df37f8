--- /src/com/google/javascript/jscomp/CollapseVariableDeclarations.java
+++ /src/com/google/javascript/jscomp/CollapseVariableDeclarations.java
@@ -201,10 +201,14 @@
       Var var = s.getVar(lhs.getString());
       return var != null
           && var.getScope() == s
+          && !isNamedParameter(var)
           && !blacklistedVars.contains(var);
     }
   }
 
+  private boolean isNamedParameter(Var v) {
+    return v.getParentNode().isParamList();
+  }
 
   private void applyCollapses() {
     for (Collapse collapse : collapses) {

