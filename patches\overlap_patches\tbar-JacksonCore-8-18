--- /src/main/java/com/fasterxml/jackson/core/util/TextBuffer.java
+++ /src/main/java/com/fasterxml/jackson/core/util/TextBuffer.java
@@ -299,12 +299,13 @@
     {
         // Are we just using shared input buffer?
         if (_inputStart >= 0) return _inputBuffer;
-        if (_resultArray != null)  return _resultArray;
+        if (!_hasSegments)  return _currentSegment;
+	if (_resultArray != null)  return _resultArray;
         if (_resultString != null) {
             return (_resultArray = _resultString.toCharArray());
         }
         // Nope; but does it fit in just one segment?
-        if (!_hasSegments)  return _currentSegment;
+        
         // Nope, need to have/create a non-segmented array and return it
         return contentsAsArray();
     }
