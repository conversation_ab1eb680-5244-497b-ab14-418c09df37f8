--- /src/com/google/javascript/jscomp/CodeGenerator.java
+++ /src/com/google/javascript/jscomp/CodeGenerator.java
@@ -1012,7 +1012,7 @@
     for (int i = 0; i < s.length(); i++) {
       char c = s.charAt(i);
       switch (c) {
-        case '\0': sb.append("\\0"); break;
+        case '\0': sb.append("\\000"); break;
         case '\n': sb.append("\\n"); break;
         case '\r': sb.append("\\r"); break;
         case '\t': sb.append("\\t"); break;

