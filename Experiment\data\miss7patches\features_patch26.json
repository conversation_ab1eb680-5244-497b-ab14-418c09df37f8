{"files": [{"features": [{"FEATURES_BINARYOPERATOR": {"0_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "100_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "101_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "102_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "103_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "104_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "105_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "106_org.apache.commons.lang.math.NumberUtils.compare(array1[i], array2[i]) != 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "107_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "108_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "109_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "10_expPos > (-1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "110_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "111_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "112_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "113_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "114_array[j] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "115_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "116_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "117_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "118_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "119_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "11_expPos < decPos": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "120_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "121_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "122_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "123_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "124_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "125_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "126_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "127_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "128_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "129_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "12_str + \" is not a valid number.\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "130_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "131_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "132_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "133_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "134_array[j] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "135_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "136_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "137_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "138_array[j] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "139_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "13_decPos + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "140_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "141_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "142_array[i] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "143_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "144_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "145_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "146_array[i] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "147_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "148_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "149_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "14_decPos + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "150_array[j] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "151_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "152_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "153_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "154_array[j] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "155_b < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "156_c < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "157_b < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "158_c < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "159_b < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "15_expPos > (-1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "160_c < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "161_b < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "162_c < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "163_b > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "164_c > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "165_b > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "166_c > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "167_b > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "168_c > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "169_b > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "16_(expPos > (-1)) && (expPos < (str.length() - 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "170_c > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "171_lhs < rhs": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "172_lhs > rhs": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "173_lhsBits == rhsBits": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "174_lhsBits < rhsBits": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "175_lhs < rhs": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "176_lhs > rhs": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "177_lhsBits == rhsBits": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "178_lhsBits < rhsBits": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "179_i < str.length()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "17_(expPos > (-1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "180_chars[0] == '-'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "181_sz > (start + 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "182_(start + 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "183_(chars[start] == '0') && (chars[start + 1] == 'x')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "184_(chars[start] == '0')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "185_(chars[start + 1] == 'x')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "186_start + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "187_start + 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "188_i == sz": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "189_i < chars.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "18_(expPos < (str.length() - 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "190_(((chars[i] < '0') || (chars[i] > '9')) && ((chars[i] < 'a') || (chars[i] > 'f'))) && ((chars[i] < 'A') || (chars[i] > 'F'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "191_(((chars[i] < '0') || (chars[i] > '9')) && ((chars[i] < 'a') || (chars[i] > 'f')))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "192_((chars[i] < '0') || (chars[i] > '9'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "193_(chars[i] < '0')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "194_(chars[i] > '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "195_((chars[i] < 'a') || (chars[i] > 'f'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "196_(chars[i] < 'a')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "197_(chars[i] > 'f')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "198_((chars[i] < 'A') || (chars[i] > 'F'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "199_(chars[i] < 'A')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "19_(str.length() - 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "1_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "200_(chars[i] > 'F')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "201_(i < sz) || (((i < (sz + 1)) && allowSigns) && (!foundDigit))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "202_(i < sz)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "203_(((i < (sz + 1)) && allowSigns) && (!foundDigit))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "204_((i < (sz + 1)) && allowSigns)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "205_(i < (sz + 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "206_(sz + 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "207_(chars[i] >= '0') && (chars[i] <= '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "208_(chars[i] >= '0')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "209_(chars[i] <= '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "20_expPos + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "210_chars[i] == '.'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "211_hasDecPoint || hasExp": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "212_(chars[i] == 'e') || (chars[i] == 'E')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "213_(chars[i] == 'e')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "214_(chars[i] == 'E')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "215_(chars[i] == '+') || (chars[i] == '-')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "216_(chars[i] == '+')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "217_(chars[i] == '-')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "218_i < chars.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "219_(chars[i] >= '0') && (chars[i] <= '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "21_str.length() - 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "220_(chars[i] >= '0')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "221_(chars[i] <= '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "222_(chars[i] == 'e') || (chars[i] == 'E')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "223_(chars[i] == 'e')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "224_(chars[i] == 'E')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "225_(!allowSigns) && ((((chars[i] == 'd') || (chars[i] == 'D')) || (chars[i] == 'f')) || (chars[i] == 'F'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "226_((((chars[i] == 'd') || (chars[i] == 'D')) || (chars[i] == 'f')) || (chars[i] == 'F'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "227_(((chars[i] == 'd') || (chars[i] == 'D')) || (chars[i] == 'f'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "228_((chars[i] == 'd') || (chars[i] == 'D'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "229_(chars[i] == 'd')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "22_str.length() - 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "230_(chars[i] == 'D')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "231_(chars[i] == 'f')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "232_(chars[i] == 'F')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "233_(chars[i] == 'l') || (chars[i] == 'L')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "234_(chars[i] == 'l')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "235_(chars[i] == 'L')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "236_foundDigit && (!hasExp)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "237_(!allowSigns) && foundDigit": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "23_org.apache.commons.lang.math.NumberUtils.isAllZeros(mant) && org.apache.commons.lang.math.NumberUtils.isAllZeros(exp)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "24_(((dec == null) && (exp == null)) && org.apache.commons.lang.math.NumberUtils.isDigits(numeric.substring(1))) && ((numeric.charAt(0) == '-') || java.lang.Character.isDigit(numeric.charAt(0)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "25_(((dec == null) && (exp == null)) && org.apache.commons.lang.math.NumberUtils.isDigits(numeric.substring(1)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "26_((dec == null) && (exp == null))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "27_(dec == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "28_(exp == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "29_((numeric.charAt(0) == '-') || java.lang.Character.isDigit(numeric.charAt(0)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "2_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "30_(numeric.charAt(0) == '-')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "31_str + \" is not a valid number.\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "32_(f.isInfinite() || ((f.floatValue() == 0.0F) && (!allZeros)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "33_((f.floatValue() == 0.0F) && (!allZeros))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "34_(f.floatValue() == 0.0F)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "35_(d.isInfinite() || ((d.floatValue() == 0.0) && (!allZeros)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "36_((d.floatValue() == 0.0) && (!allZeros))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "37_(d.floatValue() == 0.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "38_str + \" is not a valid number.\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "39_(expPos > (-1)) && (expPos < (str.length() - 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "40_(expPos > (-1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "41_(expPos < (str.length() - 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "42_(str.length() - 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "43_expPos + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "44_(dec == null) && (exp == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "45_(dec == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "46_(exp == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "47_org.apache.commons.lang.math.NumberUtils.isAllZeros(mant) && org.apache.commons.lang.math.NumberUtils.isAllZeros(exp)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "48_(f.isInfinite() || ((f.floatValue() == 0.0F) && (!allZeros)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "49_((f.floatValue() == 0.0F) && (!allZeros))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "50_(f.floatValue() == 0.0F)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "51_(d.isInfinite() || ((d.doubleValue() == 0.0) && (!allZeros)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "52_((d.doubleValue() == 0.0) && (!allZeros))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "53_(d.doubleValue() == 0.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "54_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "55_str.length() - 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "56_i >= 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "57_str.charAt(i) != '0'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "58_str.length() > 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "59_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "5_str.startsWith(\"0x\") || str.startsWith(\"-0x\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "60_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "61_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "62_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "63_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "64_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "65_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "66_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "67_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "68_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "69_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "6_str.length() - 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "70_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "71_array1[i] != array2[i]": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "72_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "73_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "74_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "75_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "76_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "77_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "78_array1[i] != array2[i]": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "79_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "7_(str.indexOf('e') + str.indexOf('E')) + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "80_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "81_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "82_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "83_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "84_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "85_array1[i] != array2[i]": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "86_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "87_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "88_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "89_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "8_(str.indexOf('e') + str.indexOf('E'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "90_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "91_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "92_array1[i] != array2[i]": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "93_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "94_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "95_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "96_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "97_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "98_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "99_org.apache.commons.lang.math.NumberUtils.compare(array1[i], array2[i]) != 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "9_decPos > (-1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {}, "FEATURES_VARS": {"lastChar": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "false", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "false", "V8_VAR_PRIMITIVE": "true", "V9_VAR_TYPE_Similar_Literal": "true"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "LocalVariable", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "LocalVariable", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "If", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "If", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "false", "S1_LOCAL_VAR_NOT_USED": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "S3_TYPE_OF_FAULTY_STATEMENT": "Switch", "S4_Field_NOT_ASSIGNED": "false", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S6_METHOD_THROWS_EXCEPTION": "true", "S7_OBJECT_USED_IN_ASSIGNMENT": "false", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "null", "dst_parent": "null", "dst_parent_type": "null", "dst_type": "null", "operator": "INS", "src": "if ((-1) < expPos) {\n    throw new java.lang.NumberFormatException(str + \" is not a valid number.\");\n}", "src_parent": "case 'L' :\n    if ((((dec == null) && (exp == null)) && org.apache.commons.lang.math.NumberUtils.isDigits(numeric.substring(1))) && ((numeric.charAt(0) == '-') || java.lang.Character.isDigit(numeric.charAt(0)))) {\n        try {\n            return org.apache.commons.lang.math.NumberUtils.createLong(numeric);\n        } catch (java.lang.NumberFormatException nfe) {\n        }\n        return org.apache.commons.lang.math.NumberUtils.createBigInteger(numeric);\n    }\n    if ((-1) < expPos) {\n        throw new java.lang.NumberFormatException(str + \" is not a valid number.\");\n    }", "src_parent_type": "Case", "src_type": "If"}}, {"FEATURES_BINARYOPERATOR": {"0_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "100_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "101_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "102_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "103_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "104_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "105_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "106_org.apache.commons.lang.math.NumberUtils.compare(array1[i], array2[i]) != 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "107_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "108_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "109_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "10_expPos > (-1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "110_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "111_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "112_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "113_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "114_array[j] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "115_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "116_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "117_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "118_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "119_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "11_expPos < decPos": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "120_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "121_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "122_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "123_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "124_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "125_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "126_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "127_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "128_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "129_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "12_str + \" is not a valid number.\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "130_array[i] < min": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "131_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "132_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "133_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "134_array[j] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "135_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "136_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "137_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "138_array[j] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "139_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "13_decPos + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "140_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "141_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "142_array[i] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "143_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "144_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "145_i < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "146_array[i] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "147_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "148_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "149_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "14_decPos + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "150_array[j] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "151_array == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "152_array.length == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "153_j < array.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "154_array[j] > max": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "155_b < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "156_c < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "157_b < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "158_c < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "159_b < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "15_expPos > (-1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "160_c < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "161_b < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "162_c < a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "163_b > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "164_c > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "165_b > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "166_c > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "167_b > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "168_c > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "169_b > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "16_(expPos > (-1)) && (expPos < (str.length() - 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "170_c > a": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "171_lhs < rhs": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "172_lhs > rhs": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "173_lhsBits == rhsBits": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "174_lhsBits < rhsBits": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "175_lhs < rhs": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "176_lhs > rhs": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "177_lhsBits == rhsBits": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "178_lhsBits < rhsBits": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "179_i < str.length()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "17_(expPos > (-1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "180_chars[0] == '-'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "181_sz > (start + 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "182_(start + 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "183_(chars[start] == '0') && (chars[start + 1] == 'x')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "184_(chars[start] == '0')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "185_(chars[start + 1] == 'x')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "186_start + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "187_start + 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "188_i == sz": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "189_i < chars.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "18_(expPos < (str.length() - 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "190_(((chars[i] < '0') || (chars[i] > '9')) && ((chars[i] < 'a') || (chars[i] > 'f'))) && ((chars[i] < 'A') || (chars[i] > 'F'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "191_(((chars[i] < '0') || (chars[i] > '9')) && ((chars[i] < 'a') || (chars[i] > 'f')))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "192_((chars[i] < '0') || (chars[i] > '9'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "193_(chars[i] < '0')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "194_(chars[i] > '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "195_((chars[i] < 'a') || (chars[i] > 'f'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "196_(chars[i] < 'a')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "197_(chars[i] > 'f')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "198_((chars[i] < 'A') || (chars[i] > 'F'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "199_(chars[i] < 'A')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "19_(str.length() - 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "1_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "200_(chars[i] > 'F')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "201_(i < sz) || (((i < (sz + 1)) && allowSigns) && (!foundDigit))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "202_(i < sz)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "203_(((i < (sz + 1)) && allowSigns) && (!foundDigit))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "204_((i < (sz + 1)) && allowSigns)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "205_(i < (sz + 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "206_(sz + 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "207_(chars[i] >= '0') && (chars[i] <= '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "208_(chars[i] >= '0')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "209_(chars[i] <= '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "20_expPos + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "210_chars[i] == '.'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "211_hasDecPoint || hasExp": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "212_(chars[i] == 'e') || (chars[i] == 'E')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "213_(chars[i] == 'e')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "214_(chars[i] == 'E')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "215_(chars[i] == '+') || (chars[i] == '-')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "216_(chars[i] == '+')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "217_(chars[i] == '-')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "218_i < chars.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "219_(chars[i] >= '0') && (chars[i] <= '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "21_str.length() - 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "220_(chars[i] >= '0')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "221_(chars[i] <= '9')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "222_(chars[i] == 'e') || (chars[i] == 'E')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "223_(chars[i] == 'e')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "224_(chars[i] == 'E')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "225_(!allowSigns) && ((((chars[i] == 'd') || (chars[i] == 'D')) || (chars[i] == 'f')) || (chars[i] == 'F'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "226_((((chars[i] == 'd') || (chars[i] == 'D')) || (chars[i] == 'f')) || (chars[i] == 'F'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "227_(((chars[i] == 'd') || (chars[i] == 'D')) || (chars[i] == 'f'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "228_((chars[i] == 'd') || (chars[i] == 'D'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "229_(chars[i] == 'd')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "22_str.length() - 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "230_(chars[i] == 'D')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "231_(chars[i] == 'f')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "232_(chars[i] == 'F')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "233_(chars[i] == 'l') || (chars[i] == 'L')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "234_(chars[i] == 'l')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "235_(chars[i] == 'L')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "236_foundDigit && (!hasExp)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "237_(!allowSigns) && foundDigit": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "23_org.apache.commons.lang.math.NumberUtils.isAllZeros(mant) && org.apache.commons.lang.math.NumberUtils.isAllZeros(exp)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "24_(((dec == null) && (exp == null)) && org.apache.commons.lang.math.NumberUtils.isDigits(numeric.substring(1))) && ((numeric.charAt(0) == '-') || java.lang.Character.isDigit(numeric.charAt(0)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "25_(((dec == null) && (exp == null)) && org.apache.commons.lang.math.NumberUtils.isDigits(numeric.substring(1)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "26_((dec == null) && (exp == null))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "27_(dec == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "28_(exp == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "29_((numeric.charAt(0) == '-') || java.lang.Character.isDigit(numeric.charAt(0)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "2_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "30_(numeric.charAt(0) == '-')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "31_str + \" is not a valid number.\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "32_(f.isInfinite() || ((f.floatValue() == 0.0F) && (!allZeros)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "33_((f.floatValue() == 0.0F) && (!allZeros))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "34_(f.floatValue() == 0.0F)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "35_(d.isInfinite() || ((d.floatValue() == 0.0) && (!allZeros)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "36_((d.floatValue() == 0.0) && (!allZeros))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "37_(d.floatValue() == 0.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "38_str + \" is not a valid number.\"": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "39_(expPos > (-1)) && (expPos < (str.length() - 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "40_(expPos > (-1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "41_(expPos < (str.length() - 1))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "42_(str.length() - 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "43_expPos + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "44_(dec == null) && (exp == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "45_(dec == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "46_(exp == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "47_org.apache.commons.lang.math.NumberUtils.isAllZeros(mant) && org.apache.commons.lang.math.NumberUtils.isAllZeros(exp)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "48_(f.isInfinite() || ((f.floatValue() == 0.0F) && (!allZeros)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "49_((f.floatValue() == 0.0F) && (!allZeros))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "50_(f.floatValue() == 0.0F)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "51_(d.isInfinite() || ((d.doubleValue() == 0.0) && (!allZeros)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "52_((d.doubleValue() == 0.0) && (!allZeros))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "true", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "53_(d.doubleValue() == 0.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "54_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "55_str.length() - 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "56_i >= 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "57_str.charAt(i) != '0'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "58_str.length() > 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "59_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "5_str.startsWith(\"0x\") || str.startsWith(\"-0x\")": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "60_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "61_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "62_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "63_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "64_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "65_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "66_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "67_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "68_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "69_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "6_str.length() - 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "70_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "71_array1[i] != array2[i]": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "72_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "73_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "74_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "75_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "76_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "77_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "78_array1[i] != array2[i]": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "79_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "7_(str.indexOf('e') + str.indexOf('E')) + 1": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "80_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "81_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "82_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "83_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "84_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "85_array1[i] != array2[i]": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "86_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "87_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "88_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "89_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "8_(str.indexOf('e') + str.indexOf('E'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "90_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "91_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "92_array1[i] != array2[i]": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "93_array1 == array2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "94_(array1 == null) || (array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "95_(array1 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "96_(array2 == null)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "97_array1.length != array2.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "98_i < array1.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "99_org.apache.commons.lang.math.NumberUtils.compare(array1[i], array2[i]) != 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "9_decPos > (-1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {}, "FEATURES_VARS": {"lastChar": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "false", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "false", "V8_VAR_PRIMITIVE": "true", "V9_VAR_TYPE_Similar_Literal": "true"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "LocalVariable", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "LocalVariable", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "If", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "If", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "false", "S1_LOCAL_VAR_NOT_USED": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "S3_TYPE_OF_FAULTY_STATEMENT": "Switch", "S4_Field_NOT_ASSIGNED": "false", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S6_METHOD_THROWS_EXCEPTION": "true", "S7_OBJECT_USED_IN_ASSIGNMENT": "false", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "throw new java.lang.NumberFormatException(str + \" is not a valid number.\")", "dst_parent": "{\n    throw new java.lang.NumberFormatException(str + \" is not a valid number.\");\n}", "dst_parent_type": "Block", "dst_type": "<PERSON>hrow", "operator": "MOV", "src": "throw new java.lang.NumberFormatException(str + \" is not a valid number.\")", "src_parent": "case 'L' :\n    if ((((dec == null) && (exp == null)) && org.apache.commons.lang.math.NumberUtils.isDigits(numeric.substring(1))) && ((numeric.charAt(0) == '-') || java.lang.Character.isDigit(numeric.charAt(0)))) {\n        try {\n            return org.apache.commons.lang.math.NumberUtils.createLong(numeric);\n        } catch (java.lang.NumberFormatException nfe) {\n        }\n        return org.apache.commons.lang.math.NumberUtils.createBigInteger(numeric);\n    }\n    throw new java.lang.NumberFormatException(str + \" is not a valid number.\");", "src_parent_type": "Case", "src_type": "<PERSON>hrow"}}, {"P4J_FORMER_ABST_V_AF": "0", "P4J_FORMER_ASSIGN_CONST_AF": "0", "P4J_FORMER_ASSIGN_LHS_AF": "0", "P4J_FORMER_ASSIGN_ZERO_AF": "0", "P4J_FORMER_CALLEE_AF": "1", "P4J_FORMER_CALL_ARGUMENT_AF": "1", "P4J_FORMER_CHANGED_AF": "0", "P4J_FORMER_DEREF_AF": "0", "P4J_FORMER_FUNC_ARGUMENT_VF": "0", "P4J_FORMER_GLOBAL_VARIABLE_VF": "0", "P4J_FORMER_INDEX_AF": "0", "P4J_FORMER_INSERT_CONTROL_RF": "0", "P4J_FORMER_INSERT_GUARD_RF": "0", "P4J_FORMER_INSERT_STMT_RF": "0", "P4J_FORMER_LOCAL_VARIABLE_VF": "0", "P4J_FORMER_MEMBER_ACCESS_AF": "0", "P4J_FORMER_MEMBER_VF": "0", "P4J_FORMER_MODIFIED_SIMILAR_VF": "0", "P4J_FORMER_MODIFIED_VF": "0", "P4J_FORMER_NONZERO_CONST_VF": "0", "P4J_FORMER_OP_ADD_AF": "0", "P4J_FORMER_OP_DIV_AF": "0", "P4J_FORMER_OP_EQ_AF": "1", "P4J_FORMER_OP_GE_AF": "0", "P4J_FORMER_OP_GT_AF": "0", "P4J_FORMER_OP_LE_AF": "0", "P4J_FORMER_OP_LT_AF": "0", "P4J_FORMER_OP_MOD_AF": "0", "P4J_FORMER_OP_MUL_AF": "0", "P4J_FORMER_OP_NE_AF": "0", "P4J_FORMER_OP_SUB_AF": "0", "P4J_FORMER_REMOVE_PARTIAL_IF": "0", "P4J_FORMER_REMOVE_STMT": "0", "P4J_FORMER_REMOVE_WHOLE_BLOCK": "0", "P4J_FORMER_REMOVE_WHOLE_IF": "0", "P4J_FORMER_REPLACE_COND_RF": "0", "P4J_FORMER_REPLACE_STMT_RF": "0", "P4J_FORMER_R_STMT_ASSIGN_AF": "0", "P4J_FORMER_R_STMT_CALL_AF": "0", "P4J_FORMER_R_STMT_COND_AF": "0", "P4J_FORMER_R_STMT_CONTROL_AF": "0", "P4J_FORMER_SIZE_LITERAL_VF": "0", "P4J_FORMER_STMT_ASSIGN_AF": "0", "P4J_FORMER_STMT_CALL_AF": "1", "P4J_FORMER_STMT_COND_AF": "1", "P4J_FORMER_STMT_CONTROL_AF": "1", "P4J_FORMER_STMT_LABEL_AF": "0", "P4J_FORMER_STMT_LOOP_AF": "0", "P4J_FORMER_STRING_LITERAL_VF": "0", "P4J_FORMER_UOP_DEC_AF": "0", "P4J_FORMER_UOP_INC_AF": "0", "P4J_FORMER_ZERO_CONST_VF": "0", "P4J_LATER_ABST_V_AF": "0", "P4J_LATER_ASSIGN_CONST_AF": "0", "P4J_LATER_ASSIGN_LHS_AF": "0", "P4J_LATER_ASSIGN_ZERO_AF": "0", "P4J_LATER_CALLEE_AF": "0", "P4J_LATER_CALL_ARGUMENT_AF": "0", "P4J_LATER_CHANGED_AF": "0", "P4J_LATER_DEREF_AF": "0", "P4J_LATER_FUNC_ARGUMENT_VF": "0", "P4J_LATER_GLOBAL_VARIABLE_VF": "0", "P4J_LATER_INDEX_AF": "0", "P4J_LATER_INSERT_CONTROL_RF": "0", "P4J_LATER_INSERT_GUARD_RF": "0", "P4J_LATER_INSERT_STMT_RF": "0", "P4J_LATER_LOCAL_VARIABLE_VF": "0", "P4J_LATER_MEMBER_ACCESS_AF": "0", "P4J_LATER_MEMBER_VF": "0", "P4J_LATER_MODIFIED_SIMILAR_VF": "0", "P4J_LATER_MODIFIED_VF": "0", "P4J_LATER_NONZERO_CONST_VF": "0", "P4J_LATER_OP_ADD_AF": "1", "P4J_LATER_OP_DIV_AF": "0", "P4J_LATER_OP_EQ_AF": "0", "P4J_LATER_OP_GE_AF": "0", "P4J_LATER_OP_GT_AF": "0", "P4J_LATER_OP_LE_AF": "0", "P4J_LATER_OP_LT_AF": "0", "P4J_LATER_OP_MOD_AF": "0", "P4J_LATER_OP_MUL_AF": "0", "P4J_LATER_OP_NE_AF": "0", "P4J_LATER_OP_SUB_AF": "0", "P4J_LATER_REMOVE_PARTIAL_IF": "0", "P4J_LATER_REMOVE_STMT": "0", "P4J_LATER_REMOVE_WHOLE_BLOCK": "0", "P4J_LATER_REMOVE_WHOLE_IF": "0", "P4J_LATER_REPLACE_COND_RF": "0", "P4J_LATER_REPLACE_STMT_RF": "0", "P4J_LATER_R_STMT_ASSIGN_AF": "0", "P4J_LATER_R_STMT_CALL_AF": "0", "P4J_LATER_R_STMT_COND_AF": "0", "P4J_LATER_R_STMT_CONTROL_AF": "0", "P4J_LATER_SIZE_LITERAL_VF": "0", "P4J_LATER_STMT_ASSIGN_AF": "0", "P4J_LATER_STMT_CALL_AF": "0", "P4J_LATER_STMT_COND_AF": "0", "P4J_LATER_STMT_CONTROL_AF": "1", "P4J_LATER_STMT_LABEL_AF": "0", "P4J_LATER_STMT_LOOP_AF": "0", "P4J_LATER_STRING_LITERAL_VF": "0", "P4J_LATER_UOP_DEC_AF": "0", "P4J_LATER_UOP_INC_AF": "0", "P4J_LATER_ZERO_CONST_VF": "0", "P4J_SRC_ABST_V_AF": "0", "P4J_SRC_ASSIGN_CONST_AF": "0", "P4J_SRC_ASSIGN_LHS_AF": "0", "P4J_SRC_ASSIGN_ZERO_AF": "0", "P4J_SRC_CALLEE_AF": "0", "P4J_SRC_CALL_ARGUMENT_AF": "0", "P4J_SRC_CHANGED_AF": "0", "P4J_SRC_DEREF_AF": "0", "P4J_SRC_FUNC_ARGUMENT_VF": "1", "P4J_SRC_GLOBAL_VARIABLE_VF": "1", "P4J_SRC_INDEX_AF": "0", "P4J_SRC_INSERT_CONTROL_RF": "0", "P4J_SRC_INSERT_GUARD_RF": "0", "P4J_SRC_INSERT_STMT_RF": "1", "P4J_SRC_LOCAL_VARIABLE_VF": "0", "P4J_SRC_MEMBER_ACCESS_AF": "0", "P4J_SRC_MEMBER_VF": "0", "P4J_SRC_MODIFIED_SIMILAR_VF": "0", "P4J_SRC_MODIFIED_VF": "0", "P4J_SRC_NONZERO_CONST_VF": "0", "P4J_SRC_OP_ADD_AF": "1", "P4J_SRC_OP_DIV_AF": "0", "P4J_SRC_OP_EQ_AF": "0", "P4J_SRC_OP_GE_AF": "0", "P4J_SRC_OP_GT_AF": "0", "P4J_SRC_OP_LE_AF": "0", "P4J_SRC_OP_LT_AF": "0", "P4J_SRC_OP_MOD_AF": "0", "P4J_SRC_OP_MUL_AF": "0", "P4J_SRC_OP_NE_AF": "0", "P4J_SRC_OP_SUB_AF": "0", "P4J_SRC_REMOVE_PARTIAL_IF": "0", "P4J_SRC_REMOVE_STMT": "0", "P4J_SRC_REMOVE_WHOLE_BLOCK": "0", "P4J_SRC_REMOVE_WHOLE_IF": "0", "P4J_SRC_REPLACE_COND_RF": "0", "P4J_SRC_REPLACE_STMT_RF": "0", "P4J_SRC_R_STMT_ASSIGN_AF": "0", "P4J_SRC_R_STMT_CALL_AF": "0", "P4J_SRC_R_STMT_COND_AF": "0", "P4J_SRC_R_STMT_CONTROL_AF": "0", "P4J_SRC_SIZE_LITERAL_VF": "0", "P4J_SRC_STMT_ASSIGN_AF": "0", "P4J_SRC_STMT_CALL_AF": "0", "P4J_SRC_STMT_COND_AF": "0", "P4J_SRC_STMT_CONTROL_AF": "1", "P4J_SRC_STMT_LABEL_AF": "0", "P4J_SRC_STMT_LOOP_AF": "0", "P4J_SRC_STRING_LITERAL_VF": "1", "P4J_SRC_UOP_DEC_AF": "0", "P4J_SRC_UOP_INC_AF": "0", "P4J_SRC_ZERO_CONST_VF": "0"}, {"repairPatterns": {"codeMove": 1, "condBlockExcAdd": 0, "condBlockOthersAdd": 0, "condBlockRem": 0, "condBlockRetAdd": 0, "constChange": 0, "copyPaste": 0, "expArithMod": 0, "expLogicExpand": 0, "expLogicMod": 0, "expLogicReduce": 0, "missNullCheckN": 0, "missNullCheckP": 0, "notClassified": 0, "singleLine": 1, "unwrapIfElse": 0, "unwrapMethod": 0, "unwrapTryCatch": 0, "wrapsElse": 0, "wrapsIf": 0, "wrapsIfElse": 0, "wrapsLoop": 0, "wrapsMethod": 0, "wrapsTryCatch": 0, "wrongMethodRef": 0, "wrongVarRef": 0}}, {"UpdateLiteral": 0, "addLineNo": 1, "addThis": 0, "condLogicReduce": 0, "dupArgsInvocation": 0, "ifTrue": 0, "insertBooleanLiteral": 0, "insertIfFalse": 0, "insertNewConstLiteral": 1, "patchedFileNo": 1, "removeNullinCond": 0, "rmLineNo": 0, "updIfFalse": 0}], "file_name": "lang58"}], "id": "patch26"}