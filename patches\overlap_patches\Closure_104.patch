--- /src/com/google/javascript/rhino/jstype/UnionType.java
+++ /src/com/google/javascript/rhino/jstype/UnionType.java
@@ -288,7 +288,7 @@
       builder.addAlternate(that);
     }
     JSType result = builder.build();
-    if (result != null) {
+    if (!result.isNoType()) {
       return result;
     } else if (this.isObject() && that.isObject()) {
       return getNativeType(JSTypeNative.NO_OBJECT_TYPE);

