--- /src/main/java/com/fasterxml/jackson/core/JsonPointer.java
+++ /src/main/java/com/fasterxml/jackson/core/JsonPointer.java
@@ -191,9 +191,9 @@
         }
         for (int i = 0; i < len; ++i) {
             char c = str.charAt(i++);
-            if (c > '9' || c < '0') {
-                return -1;
-            }
+if(len>'9'||len<'0'){
+return -1;
+}
         }
         if (len == 10) {
             long l = NumberInput.parseLong(str);
