--- /src/com/google/javascript/jscomp/CheckGlobalThis.java
+++ /src/com/google/javascript/jscomp/CheckGlobalThis.java
@@ -89,6 +89,7 @@
       JSDocInfo jsDoc = getFunctionJsDocInfo(n);
       if (jsDoc != null &&
           (jsDoc.isConstructor() ||
+           jsDoc.isInterface() ||
            jsDoc.hasThisType() ||
            jsDoc.isOverride())) {
         return false;
@@ -122,13 +123,17 @@
       } else {
         // Only traverse the right side if it's not an assignment to a prototype
         // property or subproperty.
+        if (NodeUtil.isGet(lhs)) {
           if (lhs.getType() == Token.GETPROP &&
               lhs.getLastChild().getString().equals("prototype")) {
             return false;
           }
-          if (lhs.getQualifiedName() != null && lhs.getQualifiedName().contains(".prototype.")) {
+          Node llhs = lhs.getFirstChild();
+          if (llhs.getType() == Token.GETPROP &&
+              llhs.getLastChild().getString().equals("prototype")) {
             return false;
           }
+        }
       }
     }
 

