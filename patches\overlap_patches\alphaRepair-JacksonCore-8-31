--- /src/main/java/com/fasterxml/jackson/core/util/TextBuffer.java
+++ /src/main/java/com/fasterxml/jackson/core/util/TextBuffer.java
@@ -304,7 +304,11 @@
             return (_resultArray = _resultString.toCharArray());
         }
         // Nope; but does it fit in just one segment?
-        if (!_hasSegments)  return _currentSegment;
+                return contentsAsArray();
+    }
+    
+    public char[] getTextChunks()
+    {
         // Nope, need to have/create a non-segmented array and return it
         return contentsAsArray();
     }
