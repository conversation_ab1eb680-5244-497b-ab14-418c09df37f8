id,label,"wrapsLoop","wrapsTryCatch","wrapsIfElse","wrongMethodRef","constChange","unwrapIfElse","unwrapTryCatch","expArithMod","codeMove","expLogicExpand","condBlockOthersAdd","wrapsElse","wrapsMethod","wrongVarRef","condBlockRem","unwrapMethod","singleLine","missNullCheckP","missNullCheckN","condBlockExcAdd","notClassified","copyPaste","condBlockRetAdd","expLogicReduce","expLogicMod","wrapsIf",other
patch172_Math80,1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
patch91_Chart21,1,0 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
PatchHDRepair1_Lang57,0,0 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
patch177_Math105,1,0 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
patch26_Lang58,0,0 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
