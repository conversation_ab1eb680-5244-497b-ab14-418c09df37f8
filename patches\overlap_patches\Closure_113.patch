--- /src/com/google/javascript/jscomp/ProcessClosurePrimitives.java
+++ /src/com/google/javascript/jscomp/ProcessClosurePrimitives.java
@@ -326,7 +326,7 @@
       // the checks for broken requires turned off. In these cases, we
       // allow broken requires to be preserved by the first run to
       // let them be caught in the subsequent run.
-      if (provided != null) {
+      if (provided != null || requiresLevel.isOn()) {
         parent.detachFromParent();
         compiler.reportCodeChange();
       }

