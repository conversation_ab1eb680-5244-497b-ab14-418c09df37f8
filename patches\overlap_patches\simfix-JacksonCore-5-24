--- /src/main/java/com/fasterxml/jackson/core/JsonPointer.java
+++ /src/main/java/com/fasterxml/jackson/core/JsonPointer.java
@@ -189,12 +189,12 @@
         if (len == 0 || len > 10) {
             return -1;
         }
-        for (int i = 0; i < len; ++i) {
-            char c = str.charAt(i++);
-            if (c > '9' || c < '0') {
-                return -1;
-            }
-        }
+for(int i=0;i<len;++i){
+int c=str.charAt(i++);
+if(c>'9'||c<'0'){
+return -1;
+}
+}
         if (len == 10) {
             long l = NumberInput.parseLong(str);
             if (l > Integer.MAX_VALUE) {
