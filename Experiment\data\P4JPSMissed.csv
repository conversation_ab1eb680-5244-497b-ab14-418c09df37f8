id,label,srcABSTV,srcAssignConst,srcAssignLHS,srcAssignZero,srcCALLEE,srcCallArgument,srcChanged,srcDEREF,srcFuncArgument,srcGlobalVariable,srcINDEX,srcInsertControlRF,srcInsertGUARDRF,srcInsertStmtRF,srcLocalVariable,srcMemberACCESS,srcMember,srcMODIFIEDSIMILAR,srcMODIFIED,srcNONZeroConst,srcOPADD,srcOPDIV,srcOPEQ,srcOPGE,srcOPGT,srcOPLE,srcOPLT,srcOPMOD,srcOPMUL,srcOPNE,srcOPSUB,srcRemovePARTIALIF,srcRemoveStmt,srcRemoveWHOLEBLOCK,srcRemoveWHOLEIF,srcReplaceCondRF,srcReplaceStmtRF,srcRStmtAssign,srcRStmtCALL,srcRStmt<PERSON>ond,srcRStmtControl,srcSizeLiteral,srcStmtAssign,srcStmtCALL,srcStmtCond,srcStmtControl,srcStmtLabel,srcStmtLoop,srcStringLiteral,srcUOPDEC,srcUOPINC,srcZeroConst,formerABSTV,formerAssignConst,formerAssignLHS,formerAssignZero,formerCALLEE,formerCallArgument,formerChanged,formerDEREF,formerFuncArgument,formerGlobalVariable,formerINDEX,formerInsertControlRF,formerInsertGUARDRF,formerInsertStmtRF,formerLocalVariable,formerMemberACCESS,formerMember,formerMODIFIEDSIMILAR,formerMODIFIED,formerNONZeroConst,formerOPADD,formerOPDIV,formerOPEQ,formerOPGE,formerOPGT,formerOPLE,formerOPLT,formerOPMOD,formerOPMUL,formerOPNE,formerOPSUB,formerRemovePARTIALIF,formerRemoveStmt,formerRemoveWHOLEBLOCK,formerRemoveWHOLEIF,formerReplaceCondRF,formerReplaceStmtRF,formerRStmtAssign,formerRStmtCALL,formerRStmtCond,formerRStmtControl,formerSizeLiteral,formerStmtAssign,formerStmtCALL,formerStmtCond,formerStmtControl,formerStmtLabel,formerStmtLoop,formerStringLiteral,formerUOPDEC,formerUOPINC,formerZeroConst,laterABSTV,laterAssignConst,laterAssignLHS,laterAssignZero,laterCALLEE,laterCallArgument,laterChanged,laterDEREF,laterFuncArgument,laterGlobalVariable,laterINDEX,laterInsertControlRF,laterInsertGUARDRF,laterInsertStmtRF,laterLocalVariable,laterMemberACCESS,laterMember,laterMODIFIEDSIMILAR,laterMODIFIED,laterNONZeroConst,laterOPADD,laterOPDIV,laterOPEQ,laterOPGE,laterOPGT,laterOPLE,laterOPLT,laterOPMOD,laterOPMUL,laterOPNE,laterOPSUB,laterRemovePARTIALIF,laterRemoveStmt,laterRemoveWHOLEBLOCK,laterRemoveWHOLEIF,laterReplaceCondRF,laterReplaceStmtRF,laterRStmtAssign,laterRStmtCALL,laterRStmtCond,laterRStmtControl,laterSizeLiteral,laterStmtAssign,laterStmtCALL,laterStmtCond,laterStmtControl,laterStmtLabel,laterStmtLoop,laterStringLiteral,laterUOPDEC,
patch172_Math80,1, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
patch91_Chart21,1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0,
PatchHDRepair1_Lang57,0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
patch177_Math105,1, 0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
patch26_Lang58,0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0,
