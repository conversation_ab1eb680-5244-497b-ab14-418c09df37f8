--- /src/main/java/com/fasterxml/jackson/core/JsonPointer.java
+++ /src/main/java/com/fasterxml/jackson/core/JsonPointer.java
@@ -190,10 +190,13 @@
             return -1;
         }
         for (int i = 0; i < len; ++i) {
-            char c = str.charAt(i++);
+            if (str != null) {
+	char c = str.charAt(i++);
             if (c > '9' || c < '0') {
                 return -1;
             }
+	}
+
         }
         if (len == 10) {
             long l = NumberInput.parseLong(str);
