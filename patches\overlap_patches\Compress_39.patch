--- /src/main/java/org/apache/commons/compress/utils/ArchiveUtils.java
+++ /src/main/java/org/apache/commons/compress/utils/ArchiveUtils.java
@@ -28,6 +28,7 @@
  */
 public class ArchiveUtils {
 
+    private static final int MAX_SANITIZED_NAME_LENGTH = 255;
 
     /** Private constructor to prevent instantiation of this utility class. */
     private ArchiveUtils(){
@@ -270,7 +271,13 @@
      * @since Compress 1.12
      */
     public static String sanitize(String s) {
-        final char[] chars = s.toCharArray();
+        final char[] cs = s.toCharArray();
+        final char[] chars = cs.length <= MAX_SANITIZED_NAME_LENGTH ? cs : Arrays.copyOf(cs, MAX_SANITIZED_NAME_LENGTH);
+        if (cs.length > MAX_SANITIZED_NAME_LENGTH) {
+            for (int i = MAX_SANITIZED_NAME_LENGTH - 3; i < MAX_SANITIZED_NAME_LENGTH; i++) {
+                chars[i] = '.';
+            }
+        }
         final int len = chars.length;
         final StringBuilder sb = new StringBuilder();
         for (int i = 0; i < len; i++) {

