#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版overlap_patches测试，使用更丰富的特征
"""

import pandas as pd
import numpy as np
import os
import glob
import re
from sklearn.utils import shuffle
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
import xgboost as xgb
from imblearn.over_sampling import SMOTE

def extract_enhanced_features(patch_file):
    """
    提取更丰富的补丁特征
    """
    features = {}
    
    try:
        with open(patch_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        lines = content.split('\n')
        
        # 基本统计特征
        features['file_size'] = len(content)
        features['line_count'] = len(lines)
        features['add_lines'] = content.count('\n+')
        features['remove_lines'] = content.count('\n-')
        features['modified_files'] = content.count('@@')
        
        # 代码结构特征
        features['has_if'] = 1 if re.search(r'\bif\s*\(', content) else 0
        features['has_else'] = 1 if re.search(r'\belse\b', content) else 0
        features['has_for'] = 1 if re.search(r'\bfor\s*\(', content) else 0
        features['has_while'] = 1 if re.search(r'\bwhile\s*\(', content) else 0
        features['has_try'] = 1 if re.search(r'\btry\s*\{', content) else 0
        features['has_catch'] = 1 if re.search(r'\bcatch\s*\(', content) else 0
        features['has_finally'] = 1 if re.search(r'\bfinally\s*\{', content) else 0
        features['has_return'] = 1 if re.search(r'\breturn\b', content) else 0
        features['has_break'] = 1 if re.search(r'\bbreak\b', content) else 0
        features['has_continue'] = 1 if re.search(r'\bcontinue\b', content) else 0
        
        # 变量和类型特征
        features['has_null'] = 1 if re.search(r'\bnull\b', content) else 0
        features['has_new'] = 1 if re.search(r'\bnew\s+\w+', content) else 0
        features['has_this'] = 1 if re.search(r'\bthis\b', content) else 0
        features['has_super'] = 1 if re.search(r'\bsuper\b', content) else 0
        features['has_static'] = 1 if re.search(r'\bstatic\b', content) else 0
        features['has_final'] = 1 if re.search(r'\bfinal\b', content) else 0
        features['has_private'] = 1 if re.search(r'\bprivate\b', content) else 0
        features['has_public'] = 1 if re.search(r'\bpublic\b', content) else 0
        features['has_protected'] = 1 if re.search(r'\bprotected\b', content) else 0
        
        # 方法调用特征
        features['method_calls'] = len(re.findall(r'\w+\s*\(', content))
        features['has_equals'] = 1 if re.search(r'\.equals\s*\(', content) else 0
        features['has_toString'] = 1 if re.search(r'\.toString\s*\(', content) else 0
        features['has_length'] = 1 if re.search(r'\.length\b', content) else 0
        features['has_size'] = 1 if re.search(r'\.size\s*\(', content) else 0
        
        # 操作符特征
        features['has_assignment'] = 1 if '=' in content and '==' not in content else 0
        features['has_comparison'] = 1 if any(op in content for op in ['==', '!=', '<', '>', '<=', '>=']) else 0
        features['has_logical_and'] = 1 if '&&' in content else 0
        features['has_logical_or'] = 1 if '||' in content else 0
        features['has_logical_not'] = 1 if '!' in content and '!=' not in content else 0
        
        # 异常处理特征
        features['has_throw'] = 1 if re.search(r'\bthrow\b', content) else 0
        features['has_throws'] = 1 if re.search(r'\bthrows\b', content) else 0
        features['exception_types'] = len(re.findall(r'\b\w*Exception\b', content))
        
        # 集合和数组特征
        features['has_array'] = 1 if '[' in content and ']' in content else 0
        features['has_list'] = 1 if re.search(r'\bList\b', content) else 0
        features['has_map'] = 1 if re.search(r'\bMap\b', content) else 0
        features['has_set'] = 1 if re.search(r'\bSet\b', content) else 0
        
        # 字符串特征
        features['string_literals'] = len(re.findall(r'"[^"]*"', content))
        features['has_string_concat'] = 1 if '+' in content and '"' in content else 0
        
        # 数字特征
        features['numeric_literals'] = len(re.findall(r'\b\d+\b', content))
        features['has_zero'] = 1 if re.search(r'\b0\b', content) else 0
        features['has_one'] = 1 if re.search(r'\b1\b', content) else 0
        features['has_negative'] = 1 if re.search(r'-\d+', content) else 0
        
        # 注释特征
        features['has_line_comment'] = 1 if '//' in content else 0
        features['has_block_comment'] = 1 if '/*' in content and '*/' in content else 0
        
        # 复杂度特征
        features['brace_pairs'] = min(content.count('{'), content.count('}'))
        features['paren_pairs'] = min(content.count('('), content.count(')'))
        features['bracket_pairs'] = min(content.count('['), content.count(']'))
        
        # 修改类型特征
        add_content = '\n'.join([line for line in lines if line.startswith('+')])
        remove_content = '\n'.join([line for line in lines if line.startswith('-')])
        
        features['add_method_calls'] = len(re.findall(r'\w+\s*\(', add_content))
        features['remove_method_calls'] = len(re.findall(r'\w+\s*\(', remove_content))
        features['add_has_if'] = 1 if re.search(r'\bif\s*\(', add_content) else 0
        features['remove_has_if'] = 1 if re.search(r'\bif\s*\(', remove_content) else 0
        
    except Exception as e:
        print(f"Error processing {patch_file}: {e}")
        # 返回默认特征
        feature_names = ['file_size', 'line_count', 'add_lines', 'remove_lines', 'modified_files',
                        'has_if', 'has_else', 'has_for', 'has_while', 'has_try', 'has_catch', 'has_finally',
                        'has_return', 'has_break', 'has_continue', 'has_null', 'has_new', 'has_this', 'has_super',
                        'has_static', 'has_final', 'has_private', 'has_public', 'has_protected',
                        'method_calls', 'has_equals', 'has_toString', 'has_length', 'has_size',
                        'has_assignment', 'has_comparison', 'has_logical_and', 'has_logical_or', 'has_logical_not',
                        'has_throw', 'has_throws', 'exception_types', 'has_array', 'has_list', 'has_map', 'has_set',
                        'string_literals', 'has_string_concat', 'numeric_literals', 'has_zero', 'has_one', 'has_negative',
                        'has_line_comment', 'has_block_comment', 'brace_pairs', 'paren_pairs', 'bracket_pairs',
                        'add_method_calls', 'remove_method_calls', 'add_has_if', 'remove_has_if']
        for name in feature_names:
            features[name] = 0
    
    return features

def load_enhanced_overlap_data():
    """
    加载overlap_patches数据集并提取增强特征
    """
    patches_dir = "patches/overlap_patches"
    stat_file = os.path.join(patches_dir, "patches_stat.csv")
    
    # 读取统计文件
    if not os.path.exists(stat_file):
        print(f"统计文件不存在: {stat_file}")
        return None, None, None
    
    stats_df = pd.read_csv(stat_file)
    print(f"加载了 {len(stats_df)} 个补丁的统计信息")
    
    # 收集所有patch文件
    patch_files = []
    
    # 获取所有.patch文件
    patch_pattern = os.path.join(patches_dir, "*.patch")
    patch_files.extend(glob.glob(patch_pattern))
    
    # 获取所有没有扩展名的文件（也是patch文件）
    all_files = glob.glob(os.path.join(patches_dir, "*"))
    for f in all_files:
        if os.path.isfile(f) and not f.endswith('.csv') and not f.endswith('.patch'):
            patch_files.append(f)
    
    print(f"找到 {len(patch_files)} 个补丁文件")
    
    # 提取特征
    patch_data = []
    for i, patch_file in enumerate(patch_files):
        if i % 100 == 0:
            print(f"处理进度: {i}/{len(patch_files)}")
            
        patch_name = os.path.basename(patch_file)
        features = extract_enhanced_features(patch_file)
        
        # 从统计文件中查找标签
        label = 0  # 默认为过拟合
        for _, row in stats_df.iterrows():
            # 尝试匹配补丁名称
            if (patch_name.startswith(f"{row['proj']}_{row['bid']}") or 
                patch_name.startswith(f"patch1-{row['proj']}-{row['bid']}") or
                patch_name.startswith(f"{row['tool']}-{row['proj']}-{row['bid']}")):
                label = 1 if row['genuine'] else 0
                break
        
        patch_data.append({
            'patch_name': patch_name,
            'label': label,
            **features
        })
    
    # 转换为DataFrame
    df = pd.DataFrame(patch_data)
    
    # 分离特征和标签
    feature_columns = [col for col in df.columns if col not in ['patch_name', 'label']]
    X = df[feature_columns]
    y = df['label']
    
    print(f"数据集大小: {len(df)}")
    print(f"特征数量: {len(feature_columns)}")
    print(f"标签分布: {y.value_counts().to_dict()}")
    
    return X, y, df

def main():
    
    # 加载数据
    X_test, y_test, test_df = load_enhanced_overlap_data()
    
    if X_test is None:
        print("无法加载测试数据")
        return
    
    # 加载训练数据
    print("加载训练数据...")
    training_file = "./train.csv"
    
    if not os.path.exists(training_file):
        print(f"训练文件不存在: {training_file}")
        return
    
    training = pd.read_csv(training_file, encoding='latin1', index_col=False)
    X_train = training.iloc[:,2:]
    y_train = training.iloc[:,1]
    
    print(f"训练数据: {X_train.shape}")
    print(f"测试数据: {X_test.shape}")
    
    # 特征对齐
    if X_train.shape[1] != X_test.shape[1]:
        print(f"特征数量不匹配: 训练{X_train.shape[1]} vs 测试{X_test.shape[1]}")
        
        # 使用测试数据的特征数量
        if X_train.shape[1] > X_test.shape[1]:
            X_train = X_train.iloc[:, :X_test.shape[1]]
        elif X_test.shape[1] > X_train.shape[1]:
            # 补充缺失特征
            for i in range(X_train.shape[1], X_test.shape[1]):
                X_train[f'feature_{i}'] = 0
        
        print(f"调整后 - 训练: {X_train.shape}, 测试: {X_test.shape}")
    
    # 重置列名
    X_train.columns = range(X_train.shape[1])
    X_test.columns = range(X_test.shape[1])
    
    # 训练模型
    print("训练增强XGBoost模型...")
    X_train, y_train = shuffle(X_train, y_train, random_state=0)
    
    # 使用SMOTE处理不平衡数据
    smote = SMOTE(random_state=42)
    X_train_balanced, y_train_balanced = smote.fit_resample(X_train, y_train)
    
    model = xgb.XGBClassifier(
        random_state=42,
        max_depth=8,
        gamma=0.3,
        learning_rate=0.1,
        n_estimators=200,
        early_stopping_rounds=30,
        eval_metric="logloss"
    )
    
    eval_set = [(X_train_balanced, y_train_balanced)]
    model.fit(X_train_balanced, y_train_balanced, eval_set=eval_set, verbose=False)
    
    # 预测
    print("进行预测...")
    y_pred = model.predict(X_test)
    y_pred_proba = model.predict_proba(X_test)[:, 1]
    
    # 评估结果
    print("\n=== 评估结果 ===")
    print(f"准确率: {accuracy_score(y_test, y_pred):.4f}")
    print(f"精确率: {precision_score(y_test, y_pred):.4f}")
    print(f"召回率: {recall_score(y_test, y_pred):.4f}")
    print(f"F1分数: {f1_score(y_test, y_pred):.4f}")
    
    print("\n详细分类报告:")
    print(classification_report(y_test, y_pred, target_names=['过拟合', '正确']))
    
    # 保存结果
    results_df = test_df.copy()
    results_df['predicted_label'] = y_pred
    results_df['predicted_proba'] = y_pred_proba
    results_df['correct'] = (results_df['label'] == results_df['predicted_label'])
    
    output_file = "enhanced_overlap_results.csv"
    results_df.to_csv(output_file, index=False)
    print(f"\n结果已保存到: {output_file}")
    
    # 特征重要性
    feature_importance = model.feature_importances_
    feature_names = [f'feature_{i}' for i in range(len(feature_importance))]
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False)
    
    print("\n=== 前10个重要特征 ===")
    print(importance_df.head(10))
    
    # 显示预测示例
    print("\n=== 预测示例 ===")
    sample_results = results_df.sample(min(10, len(results_df)))
    for _, row in sample_results.iterrows():
        status = "✓" if row['correct'] else "✗"
        print(f"{status} {row['patch_name']}: 真实={row['label']}, 预测={row['predicted_label']}, 概率={row['predicted_proba']:.3f}")

if __name__ == '__main__':
    main()
