--- /src/com/google/javascript/rhino/jstype/PrototypeObjectType.java
+++ /src/com/google/javascript/rhino/jstype/PrototypeObjectType.java
@@ -554,6 +554,9 @@
   @Override
   public void matchConstraint(ObjectType constraintObj) {
     // We only want to match contraints on anonymous types.
+    if (hasReferenceName()) {
+      return;
+    }
 
     // Handle the case where the constraint object is a record type.
     //

