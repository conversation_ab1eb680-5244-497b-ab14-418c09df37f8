{"files": [{"features": [{"FEATURES_BINARYOPERATOR": {"0_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "10_(ch0 < 'a')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "11_(ch0 > 'z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "12_(ch1 < 'a')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "13_(ch1 > 'z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "14_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "15_len == 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "16_str.charAt(2) != '_'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "17_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "18_(((ch3 < 'A') || (ch3 > 'Z')) || (ch4 < 'A')) || (ch4 > 'Z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "19_(((ch3 < 'A') || (ch3 > 'Z')) || (ch4 < 'A'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "1_((len != 2) && (len != 5)) && (len < 7)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "20_((ch3 < 'A') || (ch3 > 'Z'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "21_(ch3 < 'A')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "22_(ch3 > 'Z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "23_(ch4 < 'A')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "24_(ch4 > 'Z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "25_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "26_len == 5": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "27_str.charAt(5) != '_'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "28_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "29_locale != null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "2_((len != 2) && (len != 5))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "30_locale.getVariant().length() > 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "31_locale.getCountry().length() > 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "32_list.contains(defaultLocale) == false": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "33_set == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "34_langs == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "35_countryCode != null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "36_i < locales.size()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "37_countryCode.equals(locale.getCountry()) && (locale.getVariant().length() == 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "38_(locale.getVariant().length() == 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "39_countries == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_(len != 2)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "40_languageCode != null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "41_i < locales.size()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "42_(languageCode.equals(locale.getLanguage()) && (locale.getCountry().length() != 0)) && (locale.getVariant().length() == 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "43_(languageCode.equals(locale.getLanguage()) && (locale.getCountry().length() != 0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "44_(locale.getCountry().length() != 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "45_(locale.getVariant().length() == 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_(len != 5)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "5_(len < 7)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "6_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "7_(((ch0 < 'a') || (ch0 > 'z')) || (ch1 < 'a')) || (ch1 > 'z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "8_(((ch0 < 'a') || (ch0 > 'z')) || (ch1 < 'a'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "9_((ch0 < 'a') || (ch0 > 'z'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_LOGICAL_EXPRESSION": {"logical_expression_0_org.apache.commons.lang.LocaleUtils.cAvailableLocaleSet.contains(locale)": {"LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_LEFT": "false", "LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_RIGHT": "false", "LE10_ATOMIC_EXPRESSION_MULTIPLE_VAR_AS_BOOLEAN": "false", "LE10_ATOMIC_EXPRESSION_SAME_INVOCATION_TARGET": "false", "LE10_ATOMIC_EXPRESSION_USED_IN_INVOCATION_COMPARISION_VARIABLE": "false", "LE10_CONTAINS_ALL_INVOCATION_COMPARISION_VARIABLE": "false", "LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "true", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "false", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "false", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}}, "FEATURES_METHODS": {"contains": {"M10_METHOD_CALL_WITH_NULL_GUARD": "false", "M10_WRAPTTED_IN_OTHER_CALLS": "false", "M11_Satrt_With_Get": "false", "M12_Has_Var_Similar_In_Name": "false", "M12_METHOD_CALL_WITH_TRY_CATCH": "false", "M13_Argument_Has_Primitive": "true", "M1_LOCAL_VAR_NOT_ASSIGNED": "false", "M1_LOCAL_VAR_NOT_USED": "false", "M1_OVERLOADED_METHOD": "false", "M2_SIMILAR_METHOD_WITH_SAME_RETURN": "false", "M2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "M2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "M3_ANOTHER_METHOD_WITH_PARAMETER_RETURN_COMP": "false", "M4_Field_NOT_ASSIGNED": "true", "M4_Field_NOT_USED": "false", "M4_PARAMETER_RETURN_COMPABILITY": "false", "M5_MI_WITH_COMPATIBLE_VAR_TYPE": "false", "M5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "M5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "M6_INV_Invocation_INV_REPLACE_BY_VAR": "false", "M7_INV_Invocation_INV_REPLACE_BY_INV": "false", "M7_OBJECT_USED_IN_ASSIGNMENT": "false", "M8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "M8_RETURN_PRIMITIVE": "true", "M9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "M9_RETURN_OBJECTIVE": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {"spoon.support.reflect.code.CtTypeAccessImpl@1": {"C3_TYPEACCESS_ACTUAL_VAR": "false", "C4_SIMILAR_TYPEACCESS_ACTUAL_VAR": "false"}}, "FEATURES_VARS": {"cAvailableLocaleSet": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "true", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "true", "V8_VAR_PRIMITIVE": "false", "V9_VAR_TYPE_Similar_Literal": "false"}, "locale": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "true", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "true", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "false", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_FIRST_TIME_USED_AS_PARAMETER": "true", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "true", "V8_VAR_PRIMITIVE": "false", "V9_VAR_TYPE_Similar_Literal": "false"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "Method", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "false", "S1_LOCAL_VAR_NOT_USED": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "S3_TYPE_OF_FAULTY_STATEMENT": "Return", "S4_Field_NOT_ASSIGNED": "true", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S6_METHOD_THROWS_EXCEPTION": "false", "S7_OBJECT_USED_IN_ASSIGNMENT": "false", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "null", "dst_parent": "null", "dst_parent_type": "null", "dst_type": "null", "operator": "DEL", "src": "org.apache.commons.lang.LocaleUtils.cAvailableLocaleSet", "src_parent": "org.apache.commons.lang.LocaleUtils.cAvailableLocaleSet.contains(locale)", "src_parent_type": "Invocation", "src_type": "FieldRead"}}, {"FEATURES_BINARYOPERATOR": {"0_str == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "10_(ch0 < 'a')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "11_(ch0 > 'z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "12_(ch1 < 'a')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "13_(ch1 > 'z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "14_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "15_len == 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "16_str.charAt(2) != '_'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "17_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "18_(((ch3 < 'A') || (ch3 > 'Z')) || (ch4 < 'A')) || (ch4 > 'Z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "19_(((ch3 < 'A') || (ch3 > 'Z')) || (ch4 < 'A'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "1_((len != 2) && (len != 5)) && (len < 7)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "20_((ch3 < 'A') || (ch3 > 'Z'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "21_(ch3 < 'A')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "22_(ch3 > 'Z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "23_(ch4 < 'A')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "24_(ch4 > 'Z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "25_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "26_len == 5": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "27_str.charAt(5) != '_'": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "28_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "29_locale != null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "2_((len != 2) && (len != 5))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "30_locale.getVariant().length() > 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "31_locale.getCountry().length() > 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "32_list.contains(defaultLocale) == false": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "33_set == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "34_langs == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "35_countryCode != null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "36_i < locales.size()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "37_countryCode.equals(locale.getCountry()) && (locale.getVariant().length() == 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "38_(locale.getVariant().length() == 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "39_countries == null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_(len != 2)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "40_languageCode != null": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "true", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "41_i < locales.size()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "42_(languageCode.equals(locale.getLanguage()) && (locale.getCountry().length() != 0)) && (locale.getVariant().length() == 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "43_(languageCode.equals(locale.getLanguage()) && (locale.getCountry().length() != 0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "44_(locale.getCountry().length() != 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "45_(locale.getVariant().length() == 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_(len != 5)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "5_(len < 7)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "6_\"Invalid locale format: \" + str": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "7_(((ch0 < 'a') || (ch0 > 'z')) || (ch1 < 'a')) || (ch1 > 'z')": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "8_(((ch0 < 'a') || (ch0 > 'z')) || (ch1 < 'a'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "9_((ch0 < 'a') || (ch0 > 'z'))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_LOGICAL_EXPRESSION": {"logical_expression_0_org.apache.commons.lang.LocaleUtils.cAvailableLocaleSet.contains(locale)": {"LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_LEFT": "false", "LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_RIGHT": "false", "LE10_ATOMIC_EXPRESSION_MULTIPLE_VAR_AS_BOOLEAN": "false", "LE10_ATOMIC_EXPRESSION_SAME_INVOCATION_TARGET": "false", "LE10_ATOMIC_EXPRESSION_USED_IN_INVOCATION_COMPARISION_VARIABLE": "false", "LE10_CONTAINS_ALL_INVOCATION_COMPARISION_VARIABLE": "false", "LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "true", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "false", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "false", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}}, "FEATURES_METHODS": {"contains": {"M10_METHOD_CALL_WITH_NULL_GUARD": "false", "M10_WRAPTTED_IN_OTHER_CALLS": "false", "M11_Satrt_With_Get": "false", "M12_Has_Var_Similar_In_Name": "false", "M12_METHOD_CALL_WITH_TRY_CATCH": "false", "M13_Argument_Has_Primitive": "true", "M1_LOCAL_VAR_NOT_ASSIGNED": "false", "M1_LOCAL_VAR_NOT_USED": "false", "M1_OVERLOADED_METHOD": "false", "M2_SIMILAR_METHOD_WITH_SAME_RETURN": "false", "M2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "M2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "M3_ANOTHER_METHOD_WITH_PARAMETER_RETURN_COMP": "false", "M4_Field_NOT_ASSIGNED": "true", "M4_Field_NOT_USED": "false", "M4_PARAMETER_RETURN_COMPABILITY": "false", "M5_MI_WITH_COMPATIBLE_VAR_TYPE": "false", "M5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "M5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "M6_INV_Invocation_INV_REPLACE_BY_VAR": "false", "M7_INV_Invocation_INV_REPLACE_BY_INV": "false", "M7_OBJECT_USED_IN_ASSIGNMENT": "false", "M8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "M8_RETURN_PRIMITIVE": "true", "M9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "M9_RETURN_OBJECTIVE": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {"spoon.support.reflect.code.CtTypeAccessImpl@1": {"C3_TYPEACCESS_ACTUAL_VAR": "false", "C4_SIMILAR_TYPEACCESS_ACTUAL_VAR": "false"}}, "FEATURES_VARS": {"cAvailableLocaleSet": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "true", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "true", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "true", "V8_VAR_PRIMITIVE": "false", "V9_VAR_TYPE_Similar_Literal": "false"}, "locale": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "true", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "true", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "false", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "false", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_FIRST_TIME_USED_AS_PARAMETER": "true", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "true", "V8_VAR_PRIMITIVE": "false", "V9_VAR_TYPE_Similar_Literal": "false"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "Method", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "false", "S1_LOCAL_VAR_NOT_USED": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "true", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "true", "S3_TYPE_OF_FAULTY_STATEMENT": "Return", "S4_Field_NOT_ASSIGNED": "true", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S6_METHOD_THROWS_EXCEPTION": "false", "S7_OBJECT_USED_IN_ASSIGNMENT": "false", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "null", "dst_parent": "null", "dst_parent_type": "null", "dst_type": "null", "operator": "INS", "src": "org.apache.commons.lang.LocaleUtils.availableLocaleList()", "src_parent": "org.apache.commons.lang.LocaleUtils.availableLocaleList().contains(locale)", "src_parent_type": "Invocation", "src_type": "Invocation"}}, {"P4J_FORMER_ABST_V_AF": "0", "P4J_FORMER_ASSIGN_CONST_AF": "0", "P4J_FORMER_ASSIGN_LHS_AF": "0", "P4J_FORMER_ASSIGN_ZERO_AF": "0", "P4J_FORMER_CALLEE_AF": "0", "P4J_FORMER_CALL_ARGUMENT_AF": "0", "P4J_FORMER_CHANGED_AF": "0", "P4J_FORMER_DEREF_AF": "0", "P4J_FORMER_FUNC_ARGUMENT_VF": "0", "P4J_FORMER_GLOBAL_VARIABLE_VF": "0", "P4J_FORMER_INDEX_AF": "0", "P4J_FORMER_INSERT_CONTROL_RF": "0", "P4J_FORMER_INSERT_GUARD_RF": "0", "P4J_FORMER_INSERT_STMT_RF": "0", "P4J_FORMER_LOCAL_VARIABLE_VF": "0", "P4J_FORMER_MEMBER_ACCESS_AF": "0", "P4J_FORMER_MEMBER_VF": "0", "P4J_FORMER_MODIFIED_SIMILAR_VF": "0", "P4J_FORMER_MODIFIED_VF": "0", "P4J_FORMER_NONZERO_CONST_VF": "0", "P4J_FORMER_OP_ADD_AF": "0", "P4J_FORMER_OP_DIV_AF": "0", "P4J_FORMER_OP_EQ_AF": "0", "P4J_FORMER_OP_GE_AF": "0", "P4J_FORMER_OP_GT_AF": "0", "P4J_FORMER_OP_LE_AF": "0", "P4J_FORMER_OP_LT_AF": "0", "P4J_FORMER_OP_MOD_AF": "0", "P4J_FORMER_OP_MUL_AF": "0", "P4J_FORMER_OP_NE_AF": "0", "P4J_FORMER_OP_SUB_AF": "0", "P4J_FORMER_REMOVE_PARTIAL_IF": "0", "P4J_FORMER_REMOVE_STMT": "0", "P4J_FORMER_REMOVE_WHOLE_BLOCK": "0", "P4J_FORMER_REMOVE_WHOLE_IF": "0", "P4J_FORMER_REPLACE_COND_RF": "0", "P4J_FORMER_REPLACE_STMT_RF": "0", "P4J_FORMER_R_STMT_ASSIGN_AF": "0", "P4J_FORMER_R_STMT_CALL_AF": "0", "P4J_FORMER_R_STMT_COND_AF": "0", "P4J_FORMER_R_STMT_CONTROL_AF": "0", "P4J_FORMER_SIZE_LITERAL_VF": "0", "P4J_FORMER_STMT_ASSIGN_AF": "0", "P4J_FORMER_STMT_CALL_AF": "0", "P4J_FORMER_STMT_COND_AF": "0", "P4J_FORMER_STMT_CONTROL_AF": "0", "P4J_FORMER_STMT_LABEL_AF": "0", "P4J_FORMER_STMT_LOOP_AF": "0", "P4J_FORMER_STRING_LITERAL_VF": "0", "P4J_FORMER_UOP_DEC_AF": "0", "P4J_FORMER_UOP_INC_AF": "0", "P4J_FORMER_ZERO_CONST_VF": "0", "P4J_LATER_ABST_V_AF": "0", "P4J_LATER_ASSIGN_CONST_AF": "0", "P4J_LATER_ASSIGN_LHS_AF": "0", "P4J_LATER_ASSIGN_ZERO_AF": "0", "P4J_LATER_CALLEE_AF": "0", "P4J_LATER_CALL_ARGUMENT_AF": "0", "P4J_LATER_CHANGED_AF": "0", "P4J_LATER_DEREF_AF": "0", "P4J_LATER_FUNC_ARGUMENT_VF": "0", "P4J_LATER_GLOBAL_VARIABLE_VF": "0", "P4J_LATER_INDEX_AF": "0", "P4J_LATER_INSERT_CONTROL_RF": "0", "P4J_LATER_INSERT_GUARD_RF": "0", "P4J_LATER_INSERT_STMT_RF": "0", "P4J_LATER_LOCAL_VARIABLE_VF": "0", "P4J_LATER_MEMBER_ACCESS_AF": "0", "P4J_LATER_MEMBER_VF": "0", "P4J_LATER_MODIFIED_SIMILAR_VF": "0", "P4J_LATER_MODIFIED_VF": "0", "P4J_LATER_NONZERO_CONST_VF": "0", "P4J_LATER_OP_ADD_AF": "0", "P4J_LATER_OP_DIV_AF": "0", "P4J_LATER_OP_EQ_AF": "0", "P4J_LATER_OP_GE_AF": "0", "P4J_LATER_OP_GT_AF": "0", "P4J_LATER_OP_LE_AF": "0", "P4J_LATER_OP_LT_AF": "0", "P4J_LATER_OP_MOD_AF": "0", "P4J_LATER_OP_MUL_AF": "0", "P4J_LATER_OP_NE_AF": "0", "P4J_LATER_OP_SUB_AF": "0", "P4J_LATER_REMOVE_PARTIAL_IF": "0", "P4J_LATER_REMOVE_STMT": "0", "P4J_LATER_REMOVE_WHOLE_BLOCK": "0", "P4J_LATER_REMOVE_WHOLE_IF": "0", "P4J_LATER_REPLACE_COND_RF": "0", "P4J_LATER_REPLACE_STMT_RF": "0", "P4J_LATER_R_STMT_ASSIGN_AF": "0", "P4J_LATER_R_STMT_CALL_AF": "0", "P4J_LATER_R_STMT_COND_AF": "0", "P4J_LATER_R_STMT_CONTROL_AF": "0", "P4J_LATER_SIZE_LITERAL_VF": "0", "P4J_LATER_STMT_ASSIGN_AF": "0", "P4J_LATER_STMT_CALL_AF": "0", "P4J_LATER_STMT_COND_AF": "0", "P4J_LATER_STMT_CONTROL_AF": "0", "P4J_LATER_STMT_LABEL_AF": "0", "P4J_LATER_STMT_LOOP_AF": "0", "P4J_LATER_STRING_LITERAL_VF": "0", "P4J_LATER_UOP_DEC_AF": "0", "P4J_LATER_UOP_INC_AF": "0", "P4J_LATER_ZERO_CONST_VF": "0", "P4J_SRC_ABST_V_AF": "0", "P4J_SRC_ASSIGN_CONST_AF": "0", "P4J_SRC_ASSIGN_LHS_AF": "0", "P4J_SRC_ASSIGN_ZERO_AF": "0", "P4J_SRC_CALLEE_AF": "0", "P4J_SRC_CALL_ARGUMENT_AF": "0", "P4J_SRC_CHANGED_AF": "0", "P4J_SRC_DEREF_AF": "0", "P4J_SRC_FUNC_ARGUMENT_VF": "0", "P4J_SRC_GLOBAL_VARIABLE_VF": "0", "P4J_SRC_INDEX_AF": "0", "P4J_SRC_INSERT_CONTROL_RF": "0", "P4J_SRC_INSERT_GUARD_RF": "0", "P4J_SRC_INSERT_STMT_RF": "0", "P4J_SRC_LOCAL_VARIABLE_VF": "0", "P4J_SRC_MEMBER_ACCESS_AF": "1", "P4J_SRC_MEMBER_VF": "0", "P4J_SRC_MODIFIED_SIMILAR_VF": "0", "P4J_SRC_MODIFIED_VF": "0", "P4J_SRC_NONZERO_CONST_VF": "0", "P4J_SRC_OP_ADD_AF": "0", "P4J_SRC_OP_DIV_AF": "0", "P4J_SRC_OP_EQ_AF": "0", "P4J_SRC_OP_GE_AF": "0", "P4J_SRC_OP_GT_AF": "0", "P4J_SRC_OP_LE_AF": "0", "P4J_SRC_OP_LT_AF": "0", "P4J_SRC_OP_MOD_AF": "0", "P4J_SRC_OP_MUL_AF": "0", "P4J_SRC_OP_NE_AF": "0", "P4J_SRC_OP_SUB_AF": "0", "P4J_SRC_REMOVE_PARTIAL_IF": "0", "P4J_SRC_REMOVE_STMT": "0", "P4J_SRC_REMOVE_WHOLE_BLOCK": "0", "P4J_SRC_REMOVE_WHOLE_IF": "0", "P4J_SRC_REPLACE_COND_RF": "0", "P4J_SRC_REPLACE_STMT_RF": "1", "P4J_SRC_R_STMT_ASSIGN_AF": "0", "P4J_SRC_R_STMT_CALL_AF": "0", "P4J_SRC_R_STMT_COND_AF": "0", "P4J_SRC_R_STMT_CONTROL_AF": "0", "P4J_SRC_SIZE_LITERAL_VF": "0", "P4J_SRC_STMT_ASSIGN_AF": "0", "P4J_SRC_STMT_CALL_AF": "1", "P4J_SRC_STMT_COND_AF": "0", "P4J_SRC_STMT_CONTROL_AF": "0", "P4J_SRC_STMT_LABEL_AF": "0", "P4J_SRC_STMT_LOOP_AF": "0", "P4J_SRC_STRING_LITERAL_VF": "0", "P4J_SRC_UOP_DEC_AF": "0", "P4J_SRC_UOP_INC_AF": "0", "P4J_SRC_ZERO_CONST_VF": "0"}, {"repairPatterns": {"codeMove": 0, "condBlockExcAdd": 0, "condBlockOthersAdd": 0, "condBlockRem": 0, "condBlockRetAdd": 0, "constChange": 0, "copyPaste": 0, "expArithMod": 0, "expLogicExpand": 0, "expLogicMod": 0, "expLogicReduce": 0, "missNullCheckN": 0, "missNullCheckP": 0, "notClassified": 0, "singleLine": 1, "unwrapIfElse": 0, "unwrapMethod": 0, "unwrapTryCatch": 0, "wrapsElse": 0, "wrapsIf": 0, "wrapsIfElse": 0, "wrapsLoop": 0, "wrapsMethod": 0, "wrapsTryCatch": 0, "wrongMethodRef": 0, "wrongVarRef": 0}}, {"UpdateLiteral": 0, "addLineNo": 1, "addThis": 0, "condLogicReduce": 0, "dupArgsInvocation": 0, "ifTrue": 0, "insertBooleanLiteral": 0, "insertIfFalse": 0, "insertNewConstLiteral": 0, "patchedFileNo": 1, "removeNullinCond": 0, "rmLineNo": 1, "updIfFalse": 0}], "file_name": "lang57"}], "id": "PatchHDRepair1"}