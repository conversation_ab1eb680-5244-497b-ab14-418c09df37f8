--- /src/com/google/javascript/jscomp/TypeCheck.java
+++ /src/com/google/javascript/jscomp/TypeCheck.java
@@ -513,7 +513,9 @@
         // Object literal keys are handled with OBJECTLIT
         if (!NodeUtil.isObjectLitKey(n, n.getParent())) {
           ensureTyped(t, n, STRING_TYPE);
+        } else {
           // Object literal keys are not typeable
+          typeable = false;
         }
         break;
 

