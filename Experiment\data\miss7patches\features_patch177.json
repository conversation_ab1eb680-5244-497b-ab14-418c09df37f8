{"files": [{"features": [{"CONSTANT": {"0": {"C1_SAME_TYPE_CONSTANT": "true", "C2_SAME_TYPE_CONSTANT_VAR": "false", "C2_SAME_TYPE_VAR": "true"}}, "FEATURES_BINARYOPERATOR": {"0_n == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "10_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "11_((dx * dy) * ((double) (n))) / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "12_((dx * dy) * ((double) (n)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "13_(dx * dy)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "14_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "15_dx / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "16_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "17_dy / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "18_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "19_i < data.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "1_x - xbar": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "20_getIntercept(b1) + (b1 * x)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "21_(b1 * x)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "22_n < 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "23_java.lang.Math.abs(sumXX) < (10 * java.lang.Double.MIN_VALUE)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "24_(10 * java.lang.Double.MIN_VALUE)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "25_sumXY / sumXX": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "26_sumYY - ((sumXY * sumXY) / sumXX)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "27_((sumXY * sumXY) / sumXX)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "28_(sumXY * sumXY)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "29_n < 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "2_y - ybar": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "30_n < 3": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "31_getSumSquaredErrors() / ((double) (n - 2))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "32_((double) (n - 2))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "33_b1 < 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "34_(ssto - getSumSquaredErrors()) / ssto": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "35_(ssto - getSumSquaredErrors())": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "36_getMeanSquareError() * ((1.0 / ((double) (n))) + ((xbar * xbar) / sumXX))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "37_((1.0 / ((double) (n))) + ((xbar * xbar) / sumXX))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "38_(1.0 / ((double) (n)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "39_((xbar * xbar) / sumXX)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_((dx * dx) * ((double) (n))) / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "40_(xbar * xbar)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "41_getMeanSquareError() / sumXX": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "42_(alpha >= 1) || (alpha <= 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "43_(alpha >= 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "44_(alpha <= 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "45_getSlopeStdErr() * getTDistribution().inverseCumulativeProbability(1.0 - (alpha / 2.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "46_1.0 - (alpha / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "47_(alpha / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "48_2.0 * (1.0 - getTDistribution().cumulativeProbability(java.lang.Math.abs(getSlope()) / getSlopeStdErr()))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "49_(1.0 - getTDistribution().cumulativeProbability(java.lang.Math.abs(getSlope()) / getSlopeStdErr()))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_((dx * dx) * ((double) (n)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "50_java.lang.Math.abs(getSlope()) / getSlopeStdErr()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "51_(sumY - (slope * sumX)) / ((double) (n))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "52_(sumY - (slope * sumX))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "53_(slope * sumX)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "54_(slope * slope) * sumXX": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "55_(slope * slope)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "56_n - 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "5_(dx * dx)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "6_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "7_((dy * dy) * ((double) (n))) / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "8_((dy * dy) * ((double) (n)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "9_(dy * dy)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_LOGICAL_EXPRESSION": {"logical_expression_0_n == 0": {"LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_LEFT": "false", "LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_RIGHT": "false", "LE10_ATOMIC_EXPRESSION_MULTIPLE_VAR_AS_BOOLEAN": "false", "LE10_ATOMIC_EXPRESSION_SAME_INVOCATION_TARGET": "false", "LE10_ATOMIC_EXPRESSION_USED_IN_INVOCATION_COMPARISION_VARIABLE": "false", "LE10_CONTAINS_ALL_INVOCATION_COMPARISION_VARIABLE": "false", "LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "false", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "false", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {"org.apache.commons.math.stat.regression.SimpleRegression": {"C3_TYPEACCESS_ACTUAL_VAR": "false", "C4_SIMILAR_TYPEACCESS_ACTUAL_VAR": "false"}}, "FEATURES_VARS": {"n": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "false", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "true", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "false", "V8_VAR_PRIMITIVE": "true", "V9_VAR_TYPE_Similar_Literal": "true"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "OperatorAssignment", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "OperatorAssignment", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "UnaryOperator", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "Method", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "false", "S1_LOCAL_VAR_NOT_USED": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "S3_TYPE_OF_FAULTY_STATEMENT": "If", "S4_Field_NOT_ASSIGNED": "false", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S6_METHOD_THROWS_EXCEPTION": "false", "S7_OBJECT_USED_IN_ASSIGNMENT": "false", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "null", "dst_parent": "null", "dst_parent_type": "null", "dst_type": "null", "operator": "INS", "src": "if (y <= x) {\n    ybar = y;\n}", "src_parent": "{\n    xbar = x;\n    if (y <= x) {\n        ybar = y;\n    }\n}", "src_parent_type": "Block", "src_type": "If"}}, {"CONSTANT": {"0": {"C1_SAME_TYPE_CONSTANT": "true", "C2_SAME_TYPE_CONSTANT_VAR": "false", "C2_SAME_TYPE_VAR": "true"}}, "FEATURES_BINARYOPERATOR": {"0_n == 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "10_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "11_((dx * dy) * ((double) (n))) / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "12_((dx * dy) * ((double) (n)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "13_(dx * dy)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "14_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "15_dx / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "16_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "17_dy / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "18_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "19_i < data.length": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "1_x - xbar": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "20_getIntercept(b1) + (b1 * x)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "21_(b1 * x)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "22_n < 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "23_java.lang.Math.abs(sumXX) < (10 * java.lang.Double.MIN_VALUE)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "24_(10 * java.lang.Double.MIN_VALUE)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "25_sumXY / sumXX": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "26_sumYY - ((sumXY * sumXY) / sumXX)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "27_((sumXY * sumXY) / sumXX)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "28_(sumXY * sumXY)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "29_n < 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "2_y - ybar": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "30_n < 3": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "31_getSumSquaredErrors() / ((double) (n - 2))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "32_((double) (n - 2))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "33_b1 < 0": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "34_(ssto - getSumSquaredErrors()) / ssto": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "35_(ssto - getSumSquaredErrors())": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "36_getMeanSquareError() * ((1.0 / ((double) (n))) + ((xbar * xbar) / sumXX))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "37_((1.0 / ((double) (n))) + ((xbar * xbar) / sumXX))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "38_(1.0 / ((double) (n)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "39_((xbar * xbar) / sumXX)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "3_((dx * dx) * ((double) (n))) / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "40_(xbar * xbar)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "41_getMeanSquareError() / sumXX": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "42_(alpha >= 1) || (alpha <= 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "true", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "43_(alpha >= 1)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "44_(alpha <= 0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "true", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "false", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "true", "O5_IS_MATH_ROOT": "false"}, "45_getSlopeStdErr() * getTDistribution().inverseCumulativeProbability(1.0 - (alpha / 2.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "46_1.0 - (alpha / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "47_(alpha / 2.0)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "48_2.0 * (1.0 - getTDistribution().cumulativeProbability(java.lang.Math.abs(getSlope()) / getSlopeStdErr()))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "49_(1.0 - getTDistribution().cumulativeProbability(java.lang.Math.abs(getSlope()) / getSlopeStdErr()))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "4_((dx * dx) * ((double) (n)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "50_java.lang.Math.abs(getSlope()) / getSlopeStdErr()": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "51_(sumY - (slope * sumX)) / ((double) (n))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "52_(sumY - (slope * sumX))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "53_(slope * sumX)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "54_(slope * slope) * sumXX": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "55_(slope * slope)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "56_n - 2": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "5_(dx * dx)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "6_((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "true", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "7_((dy * dy) * ((double) (n))) / ((double) (n + 1.0))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "true"}, "8_((dy * dy) * ((double) (n)))": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}, "9_(dy * dy)": {"O1_IS_BIT": "false", "O1_IS_COMPARE": "false", "O1_IS_LOGICAL": "false", "O1_IS_MATH": "true", "O1_IS_OTHERS": "false", "O1_IS_SHIFT": "false", "O2_LOGICAL_CONTAIN_NOT": "false", "O3_CONTAIN_01": "false", "O3_CONTAIN_NULL": "false", "O4_COMPARE_IN_CONDITION": "false", "O5_IS_MATH_ROOT": "false"}}, "FEATURES_LOGICAL_EXPRESSION": {"logical_expression_0_n == 0": {"LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_LEFT": "false", "LE10_ATOMIC_EXPRESSION_COMPARISION_SAME_RIGHT": "false", "LE10_ATOMIC_EXPRESSION_MULTIPLE_VAR_AS_BOOLEAN": "false", "LE10_ATOMIC_EXPRESSION_SAME_INVOCATION_TARGET": "false", "LE10_ATOMIC_EXPRESSION_USED_IN_INVOCATION_COMPARISION_VARIABLE": "false", "LE10_CONTAINS_ALL_INVOCATION_COMPARISION_VARIABLE": "false", "LE1_EXISTS_RELATED_BOOLEAN_EXPRESSION": "true", "LE2_IS_BOOLEAN_METHOD_PARAM_TYPE_VAR": "false", "LE3_IS_COMPATIBLE_VAR_NOT_INCLUDED": "false", "LE4_EXISTS_LOCAL_UNUSED_VARIABLES": "false", "LE5_COMPLEX_REFERENCE": "true", "LE6_HAS_NEGATION": "false", "LE7_SIMPLE_VAR_OR_METHOD_IN_LOGIC": "false", "LE8_SCOPE_VAR_USED_OTHER_BOOLEXPER": "true", "LE9_EQUAL_NOTEQUAL_NULL_CHECK": "false", "LE9_MIX_CHECK": "false", "LE9_NORMAL_CHECK": "false", "LE9_NULL_CHECK": "false"}}, "FEATURES_METHOD_INVOCATION": {}, "FEATURES_TYPEACCESS": {"org.apache.commons.math.stat.regression.SimpleRegression": {"C3_TYPEACCESS_ACTUAL_VAR": "false", "C4_SIMILAR_TYPEACCESS_ACTUAL_VAR": "false"}}, "FEATURES_VARS": {"n": {"V10_VAR_TYPE_Similar_VAR": "false", "V11_VAR_COMPATIBLE_TYPE_IN_CONDITION": "false", "V12_VAR_Invocation_VAR_REPLACE_BY_VAR": "false", "V13_VAR_Invocation_VAR_REPLACE_BY_INVOCATION": "false", "V14_VAR_INSTANCE_OF_CLASS": "false", "V15_VAR_LAST_THREE_SAME_TYPE_LOC": "false", "V16_IS_METHOD_PARAMETER_TYPE_VAR": "true", "V17_VAR_IS_ENUMERATION": "false", "V18_Has_Method_Similar_In_Name": "false", "V19_With_Special_Name": "false", "V1_IS_TYPE_COMPATIBLE_METHOD_CALL_PARAM_RETURN": "false", "V1_LOCAL_VAR_NOT_ASSIGNED": "false", "V1_LOCAL_VAR_NOT_USED": "false", "V2_HAS_VAR_SIM_NAME": "true", "V2_HAS_VAR_SIM_NAME_COMP_TYPE": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "V2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "V3_HAS_CONSTANT": "false", "V4B_USED_MULTIPLE_AS_PARAMETER": "false", "V4_Field_NOT_ASSIGNED": "false", "V4_Field_NOT_USED": "false", "V5_HAS_VAR_IN_TRANSFORMATION": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "V5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "V6_IS_METHOD_RETURN_TYPE_VAR": "true", "V7_OBJECT_USED_IN_ASSIGNMENT": "false", "V8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "V8_VAR_OBJECT": "false", "V8_VAR_PRIMITIVE": "true", "V9_VAR_TYPE_Similar_Literal": "true"}}, "S10_METHOD_CALL_WITH_NULL_GUARD": "false", "S11_FAULTY_CLASS_EXCEPTION_TYPE": "false", "S12_METHOD_CALL_WITH_TRY_CATCH": "false", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_1": "OperatorAssignment", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_2": "OperatorAssignment", "S13_TYPE_OF_FAULTY_STATEMENT_AFTER_3": "UnaryOperator", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_1": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_2": "", "S13_TYPE_OF_FAULTY_STATEMENT_BEFORE_3": "", "S14_TYPE_OF_FAULTY_STATEMENT_PARENT": "Method", "S15_HAS_OBJECTIVE_METHOD_CALL": "false", "S16_HAS_Invocations_Prone_Exception": "false", "S18_In_Synchronized_Method": "false", "S1_LOCAL_VAR_NOT_ASSIGNED": "false", "S1_LOCAL_VAR_NOT_USED": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NORMAL_GUARD": "false", "S2_SIMILAR_OBJECT_TYPE_WITH_NULL_GUARD": "false", "S3_TYPE_OF_FAULTY_STATEMENT": "If", "S4_Field_NOT_ASSIGNED": "false", "S4_Field_NOT_USED": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NORMAL_GUARD": "false", "S5_SIMILAR_PRIMITIVE_TYPE_WITH_NULL_GUARD": "false", "S6_METHOD_THROWS_EXCEPTION": "false", "S7_OBJECT_USED_IN_ASSIGNMENT": "false", "S8_PRIMITIVE_USED_IN_ASSIGNMENT": "false", "S9_METHOD_CALL_WITH_NORMAL_GUARD": "false", "ast_info": {"dst": "ybar = y", "dst_parent": "{\n    ybar = y;\n}", "dst_parent_type": "Block", "dst_type": "Assignment", "operator": "MOV", "src": "ybar = y", "src_parent": "{\n    xbar = x;\n    ybar = y;\n}", "src_parent_type": "Block", "src_type": "Assignment"}}, {"P4J_FORMER_ABST_V_AF": "0", "P4J_FORMER_ASSIGN_CONST_AF": "0", "P4J_FORMER_ASSIGN_LHS_AF": "1", "P4J_FORMER_ASSIGN_ZERO_AF": "0", "P4J_FORMER_CALLEE_AF": "0", "P4J_FORMER_CALL_ARGUMENT_AF": "0", "P4J_FORMER_CHANGED_AF": "1", "P4J_FORMER_DEREF_AF": "0", "P4J_FORMER_FUNC_ARGUMENT_VF": "0", "P4J_FORMER_GLOBAL_VARIABLE_VF": "0", "P4J_FORMER_INDEX_AF": "0", "P4J_FORMER_INSERT_CONTROL_RF": "0", "P4J_FORMER_INSERT_GUARD_RF": "0", "P4J_FORMER_INSERT_STMT_RF": "0", "P4J_FORMER_LOCAL_VARIABLE_VF": "0", "P4J_FORMER_MEMBER_ACCESS_AF": "1", "P4J_FORMER_MEMBER_VF": "0", "P4J_FORMER_MODIFIED_SIMILAR_VF": "0", "P4J_FORMER_MODIFIED_VF": "0", "P4J_FORMER_NONZERO_CONST_VF": "0", "P4J_FORMER_OP_ADD_AF": "0", "P4J_FORMER_OP_DIV_AF": "0", "P4J_FORMER_OP_EQ_AF": "0", "P4J_FORMER_OP_GE_AF": "0", "P4J_FORMER_OP_GT_AF": "0", "P4J_FORMER_OP_LE_AF": "0", "P4J_FORMER_OP_LT_AF": "0", "P4J_FORMER_OP_MOD_AF": "0", "P4J_FORMER_OP_MUL_AF": "0", "P4J_FORMER_OP_NE_AF": "0", "P4J_FORMER_OP_SUB_AF": "0", "P4J_FORMER_REMOVE_PARTIAL_IF": "0", "P4J_FORMER_REMOVE_STMT": "0", "P4J_FORMER_REMOVE_WHOLE_BLOCK": "0", "P4J_FORMER_REMOVE_WHOLE_IF": "0", "P4J_FORMER_REPLACE_COND_RF": "0", "P4J_FORMER_REPLACE_STMT_RF": "0", "P4J_FORMER_R_STMT_ASSIGN_AF": "0", "P4J_FORMER_R_STMT_CALL_AF": "0", "P4J_FORMER_R_STMT_COND_AF": "0", "P4J_FORMER_R_STMT_CONTROL_AF": "0", "P4J_FORMER_SIZE_LITERAL_VF": "0", "P4J_FORMER_STMT_ASSIGN_AF": "1", "P4J_FORMER_STMT_CALL_AF": "0", "P4J_FORMER_STMT_COND_AF": "0", "P4J_FORMER_STMT_CONTROL_AF": "0", "P4J_FORMER_STMT_LABEL_AF": "0", "P4J_FORMER_STMT_LOOP_AF": "0", "P4J_FORMER_STRING_LITERAL_VF": "0", "P4J_FORMER_UOP_DEC_AF": "0", "P4J_FORMER_UOP_INC_AF": "0", "P4J_FORMER_ZERO_CONST_VF": "0", "P4J_LATER_ABST_V_AF": "0", "P4J_LATER_ASSIGN_CONST_AF": "0", "P4J_LATER_ASSIGN_LHS_AF": "1", "P4J_LATER_ASSIGN_ZERO_AF": "0", "P4J_LATER_CALLEE_AF": "0", "P4J_LATER_CALL_ARGUMENT_AF": "0", "P4J_LATER_CHANGED_AF": "1", "P4J_LATER_DEREF_AF": "0", "P4J_LATER_FUNC_ARGUMENT_VF": "0", "P4J_LATER_GLOBAL_VARIABLE_VF": "0", "P4J_LATER_INDEX_AF": "0", "P4J_LATER_INSERT_CONTROL_RF": "0", "P4J_LATER_INSERT_GUARD_RF": "0", "P4J_LATER_INSERT_STMT_RF": "0", "P4J_LATER_LOCAL_VARIABLE_VF": "0", "P4J_LATER_MEMBER_ACCESS_AF": "1", "P4J_LATER_MEMBER_VF": "0", "P4J_LATER_MODIFIED_SIMILAR_VF": "0", "P4J_LATER_MODIFIED_VF": "0", "P4J_LATER_NONZERO_CONST_VF": "0", "P4J_LATER_OP_ADD_AF": "0", "P4J_LATER_OP_DIV_AF": "0", "P4J_LATER_OP_EQ_AF": "0", "P4J_LATER_OP_GE_AF": "0", "P4J_LATER_OP_GT_AF": "0", "P4J_LATER_OP_LE_AF": "0", "P4J_LATER_OP_LT_AF": "0", "P4J_LATER_OP_MOD_AF": "0", "P4J_LATER_OP_MUL_AF": "0", "P4J_LATER_OP_NE_AF": "0", "P4J_LATER_OP_SUB_AF": "0", "P4J_LATER_REMOVE_PARTIAL_IF": "0", "P4J_LATER_REMOVE_STMT": "0", "P4J_LATER_REMOVE_WHOLE_BLOCK": "0", "P4J_LATER_REMOVE_WHOLE_IF": "0", "P4J_LATER_REPLACE_COND_RF": "0", "P4J_LATER_REPLACE_STMT_RF": "0", "P4J_LATER_R_STMT_ASSIGN_AF": "0", "P4J_LATER_R_STMT_CALL_AF": "0", "P4J_LATER_R_STMT_COND_AF": "0", "P4J_LATER_R_STMT_CONTROL_AF": "0", "P4J_LATER_SIZE_LITERAL_VF": "0", "P4J_LATER_STMT_ASSIGN_AF": "1", "P4J_LATER_STMT_CALL_AF": "0", "P4J_LATER_STMT_COND_AF": "0", "P4J_LATER_STMT_CONTROL_AF": "0", "P4J_LATER_STMT_LABEL_AF": "0", "P4J_LATER_STMT_LOOP_AF": "0", "P4J_LATER_STRING_LITERAL_VF": "0", "P4J_LATER_UOP_DEC_AF": "0", "P4J_LATER_UOP_INC_AF": "0", "P4J_LATER_ZERO_CONST_VF": "0", "P4J_SRC_ABST_V_AF": "0", "P4J_SRC_ASSIGN_CONST_AF": "0", "P4J_SRC_ASSIGN_LHS_AF": "1", "P4J_SRC_ASSIGN_ZERO_AF": "0", "P4J_SRC_CALLEE_AF": "0", "P4J_SRC_CALL_ARGUMENT_AF": "0", "P4J_SRC_CHANGED_AF": "1", "P4J_SRC_DEREF_AF": "0", "P4J_SRC_FUNC_ARGUMENT_VF": "1", "P4J_SRC_GLOBAL_VARIABLE_VF": "1", "P4J_SRC_INDEX_AF": "0", "P4J_SRC_INSERT_CONTROL_RF": "0", "P4J_SRC_INSERT_GUARD_RF": "0", "P4J_SRC_INSERT_STMT_RF": "1", "P4J_SRC_LOCAL_VARIABLE_VF": "0", "P4J_SRC_MEMBER_ACCESS_AF": "1", "P4J_SRC_MEMBER_VF": "0", "P4J_SRC_MODIFIED_SIMILAR_VF": "0", "P4J_SRC_MODIFIED_VF": "0", "P4J_SRC_NONZERO_CONST_VF": "0", "P4J_SRC_OP_ADD_AF": "0", "P4J_SRC_OP_DIV_AF": "0", "P4J_SRC_OP_EQ_AF": "0", "P4J_SRC_OP_GE_AF": "0", "P4J_SRC_OP_GT_AF": "0", "P4J_SRC_OP_LE_AF": "0", "P4J_SRC_OP_LT_AF": "0", "P4J_SRC_OP_MOD_AF": "0", "P4J_SRC_OP_MUL_AF": "0", "P4J_SRC_OP_NE_AF": "0", "P4J_SRC_OP_SUB_AF": "0", "P4J_SRC_REMOVE_PARTIAL_IF": "0", "P4J_SRC_REMOVE_STMT": "0", "P4J_SRC_REMOVE_WHOLE_BLOCK": "0", "P4J_SRC_REMOVE_WHOLE_IF": "0", "P4J_SRC_REPLACE_COND_RF": "0", "P4J_SRC_REPLACE_STMT_RF": "0", "P4J_SRC_R_STMT_ASSIGN_AF": "0", "P4J_SRC_R_STMT_CALL_AF": "0", "P4J_SRC_R_STMT_COND_AF": "0", "P4J_SRC_R_STMT_CONTROL_AF": "0", "P4J_SRC_SIZE_LITERAL_VF": "0", "P4J_SRC_STMT_ASSIGN_AF": "1", "P4J_SRC_STMT_CALL_AF": "0", "P4J_SRC_STMT_COND_AF": "0", "P4J_SRC_STMT_CONTROL_AF": "0", "P4J_SRC_STMT_LABEL_AF": "0", "P4J_SRC_STMT_LOOP_AF": "0", "P4J_SRC_STRING_LITERAL_VF": "0", "P4J_SRC_UOP_DEC_AF": "0", "P4J_SRC_UOP_INC_AF": "0", "P4J_SRC_ZERO_CONST_VF": "0"}, {"repairPatterns": {"codeMove": 1, "condBlockExcAdd": 0, "condBlockOthersAdd": 0, "condBlockRem": 0, "condBlockRetAdd": 0, "constChange": 0, "copyPaste": 0, "expArithMod": 0, "expLogicExpand": 0, "expLogicMod": 0, "expLogicReduce": 0, "missNullCheckN": 0, "missNullCheckP": 0, "notClassified": 0, "singleLine": 1, "unwrapIfElse": 0, "unwrapMethod": 0, "unwrapTryCatch": 0, "wrapsElse": 0, "wrapsIf": 0, "wrapsIfElse": 0, "wrapsLoop": 0, "wrapsMethod": 0, "wrapsTryCatch": 0, "wrongMethodRef": 0, "wrongVarRef": 0}}, {"UpdateLiteral": 0, "addLineNo": 2, "addThis": 0, "condLogicReduce": 0, "dupArgsInvocation": 0, "ifTrue": 0, "insertBooleanLiteral": 0, "insertIfFalse": 0, "insertNewConstLiteral": 0, "patchedFileNo": 1, "removeNullinCond": 0, "rmLineNo": 0, "updIfFalse": 0}], "file_name": "math105"}], "id": "patch177"}