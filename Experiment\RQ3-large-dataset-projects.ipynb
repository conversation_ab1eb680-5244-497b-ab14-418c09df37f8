{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from pandas import DataFrame, read_excel\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.metrics import confusion_matrix\n", "#IMPORT MODELS\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.ensemble import AdaBoostClassifier\n", "from sklearn import svm\n", "from sklearn.metrics import roc_auc_score\n", "from sklearn.metrics import accuracy_score\n", "from sklearn.metrics import fbeta_score\n", "from sklearn.metrics import recall_score\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.decomposition import PCA\n", "import seaborn as sns\n", "from sklearn.metrics import recall_score\n", "from sklearn.metrics import f1_score\n", "from sklearn.metrics import precision_score\n", "from sklearn.metrics import accuracy_score\n", "\n", "import matplotlib.pyplot as plt\n", "import itertools\n", "def plot_confusion_matrix(cm, classes,\n", "                          title='Confusion matrix',\n", "                          cmap=plt.cm.binary):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    \"\"\"\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "    plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=0)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        plt.text(j, i, cm[i, j],\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "\n", "    plt.tight_layout()\n", "    plt.ylabel('True label')\n", "    plt.xlabel('Predicted label')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loading data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#read training and test data\n", "p4jtraining = pd.DataFrame()\n", "addtraining = pd.DataFrame()\n", "s4rtraining = pd.DataFrame()\n", "p4jtest = pd.DataFrame()\n", "addtest = pd.DataFrame()\n", "s4rtest = pd.DataFrame()\n", "\n", "P4J_training_list= [\"./data/CodeTrain10302.csv\"]\n", "ADD_training_list= [\"./data/PatternsTrain10302.csv\"]\n", "S4R_training_list= [\"./data/ContextTrain10302.csv\"]\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(10302, 204)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["for f in P4J_training_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    p4jtraining = p4jtraining.append(data, ignore_index=True)\n", "    \n", "for f in ADD_training_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    addtraining = addtraining.append(data, ignore_index=True)\n", "    \n", "    \n", "for f in S4R_training_list:\n", "    data = pd.read_csv(f, encoding='latin1',index_col=False)\n", "    s4rtraining = s4rtraining.append(data, ignore_index=True)    \n", "    \n", "    \n", "p4jtraining=p4jtraining.iloc[:,:152]\n", "addtraining=addtraining.iloc[:,:28]\n", "s4rtest=s4rtest.iloc[:,:28]\n", "p4jtraining=p4jtraining.drop(columns='label')\n", "s4rtraining=s4rtraining.drop(columns='label')\n", "totaltraining = addtraining.merge(p4jtraining, on=\"id\").merge(s4rtraining, on=\"id\")  \n", "totaltraining.shape"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["test =  totaltraining[ totaltraining['id'].str.contains(r'traccar-traccar') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'traccar-traccar')]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10094, 204)\n", "overfitting in train (8132, 204)\n", "correct in train (1962, 204)\n", "====================\n", "Total test:  (208, 204)\n", "overfitting in test (167, 204)\n", "correct in test (41, 204)\n"]}], "source": ["#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>label</th>\n", "      <th>wrapsLoop</th>\n", "      <th>wrapsTryCatch</th>\n", "      <th>wrapsIfElse</th>\n", "      <th>wrongMethodRef</th>\n", "      <th>constChange</th>\n", "      <th>unwrapIfElse</th>\n", "      <th>unwrapTryCatch</th>\n", "      <th>expArithMod</th>\n", "      <th>...</th>\n", "      <th>similarObjectTypeWithNullGuard</th>\n", "      <th>typeOfFaultyStmt</th>\n", "      <th>fieldNotAssigned</th>\n", "      <th>fieldNotUsed</th>\n", "      <th>similarPrimitiveTypeWithNormalGuard</th>\n", "      <th>similarPrimitiveTypeWithNullGuard</th>\n", "      <th>methodThrowsException</th>\n", "      <th>objectUsedInAssignment</th>\n", "      <th>primitiveUsedInAssignment</th>\n", "      <th>methodCallWithNormalGuard</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5519</th>\n", "      <td>Jsou_52</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>If</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5556</th>\n", "      <td>Compress_29</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>Field</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5562</th>\n", "      <td>Math_78</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>If</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5566</th>\n", "      <td>Math_6</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>While</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5589</th>\n", "      <td>Compress_21</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>If</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1466</th>\n", "      <td>GenProg_patch_Defects4J_Math_8_0_130</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>LocalVariable</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1788</th>\n", "      <td>GenProg_patch_Defects4J_Math_8_0_230</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>LocalVariable</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2123</th>\n", "      <td>GenProg_patch_Defects4J_Math_8_0_344</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>LocalVariable</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2386</th>\n", "      <td>Gen<PERSON>rog_patch_Defects4J_Math_8_0_201</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>LocalVariable</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3901</th>\n", "      <td><PERSON><PERSON><PERSON>_patch_Defects4J_Math_81_0_444</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>If</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>910 rows × 204 columns</p>\n", "</div>"], "text/plain": ["                                        id  label  wrapsLoop  wrapsTryCatch  \\\n", "5519                               Jsou_52      0        0.0            0.0   \n", "5556                           Compress_29      0        0.0            0.0   \n", "5562                               Math_78      0        0.0            0.0   \n", "5566                                Math_6      0        0.0            0.0   \n", "5589                           Compress_21      0        0.0            0.0   \n", "...                                    ...    ...        ...            ...   \n", "1466  GenProg_patch_Defects4J_Math_8_0_130      1        0.0            0.0   \n", "1788  GenProg_patch_Defects4J_Math_8_0_230      1        0.0            0.0   \n", "2123  GenProg_patch_Defects4J_Math_8_0_344      1        0.0            0.0   \n", "2386  GenProg_patch_Defects4J_Math_8_0_201      1        0.0            0.0   \n", "3901    A<PERSON><PERSON>_patch_Defects4J_Math_81_0_444      1        0.0            0.0   \n", "\n", "      wrapsIfElse  wrongMethodRef  constChange  unwrapIfElse  unwrapTryCatch  \\\n", "5519          0.0             2.0          0.0           0.0             0.0   \n", "5556          0.0             0.0          0.0           0.0             0.0   \n", "5562          0.0             0.0          0.0           0.0             0.0   \n", "5566          0.0             1.0          1.0           0.0             0.0   \n", "5589          0.0             0.0          1.0           0.0             0.0   \n", "...           ...             ...          ...           ...             ...   \n", "1466          0.0             0.0          0.0           0.0             0.0   \n", "1788          0.0             0.0          0.0           0.0             0.0   \n", "2123          0.0             0.0          0.0           0.0             0.0   \n", "2386          0.0             0.0          0.0           0.0             0.0   \n", "3901          0.0             0.0          0.0           0.0             0.0   \n", "\n", "      expArithMod  ...  similarObjectTypeWithNullGuard  typeOfFaultyStmt  \\\n", "5519          0.0  ...                               0                If   \n", "5556          0.0  ...                               0             Field   \n", "5562          0.0  ...                               0                If   \n", "5566          2.0  ...                               0             While   \n", "5589          0.0  ...                               0                If   \n", "...           ...  ...                             ...               ...   \n", "1466          0.0  ...                               0     LocalVariable   \n", "1788          0.0  ...                               0     LocalVariable   \n", "2123          0.0  ...                               0     LocalVariable   \n", "2386          0.0  ...                               0     LocalVariable   \n", "3901          0.0  ...                               0                If   \n", "\n", "      fieldNotAssigned  fieldNotUsed  similarPrimitiveTypeWithNormalGuard  \\\n", "5519                 0             0                                    0   \n", "5556                 0             0                                    0   \n", "5562                 0             0                                    0   \n", "5566                 0             0                                    0   \n", "5589                 0             0                                    0   \n", "...                ...           ...                                  ...   \n", "1466                 0             0                                    0   \n", "1788                 0             0                                    0   \n", "2123                 0             0                                    0   \n", "2386                 0             0                                    0   \n", "3901                 0             0                                    0   \n", "\n", "      similarPrimitiveTypeWithNullGuard  methodThrowsException  \\\n", "5519                                  0                      0   \n", "5556                                  0                      0   \n", "5562                                  0                      0   \n", "5566                                  0                      0   \n", "5589                                  0                      1   \n", "...                                 ...                    ...   \n", "1466                                  0                      1   \n", "1788                                  0                      1   \n", "2123                                  0                      1   \n", "2386                                  0                      1   \n", "3901                                  0                      0   \n", "\n", "      objectUsedInAssignment  primitiveUsedInAssignment  \\\n", "5519                       0                        0.0   \n", "5556                       0                        0.0   \n", "5562                       0                        0.0   \n", "5566                       0                        0.0   \n", "5589                       0                        0.0   \n", "...                      ...                        ...   \n", "1466                       0                        0.0   \n", "1788                       0                        0.0   \n", "2123                       0                        0.0   \n", "2386                       0                        0.0   \n", "3901                       0                        0.0   \n", "\n", "      methodCallWithNormalGuard  \n", "5519                        0.0  \n", "5556                        0.0  \n", "5562                        0.0  \n", "5566                        0.0  \n", "5589                        0.0  \n", "...                         ...  \n", "1466                        0.0  \n", "1788                        0.0  \n", "2123                        0.0  \n", "2386                        0.0  \n", "3901                        0.0  \n", "\n", "[910 rows x 204 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Outlier detection \n", "from collections import Counter\n", "\n", "def detect_outliers(df,n,features):\n", "    \"\"\"\n", "    Takes a dataframe df of features and returns a list of the indices\n", "    corresponding to the observations containing more than n outliers according\n", "    to the Tukey method.\n", "    \"\"\"\n", "    outlier_indices = []\n", "    \n", "    # iterate over features(columns)\n", "    for col in features:\n", "        # 1st quartile (25%)\n", "        Q1 = np.percentile(df[col], 25)\n", "        # 3rd quartile (75%)\n", "        Q3 = np.percentile(df[col],75)\n", "        # Interquartile range (IQR)\n", "        IQR = Q3 - Q1\n", "        \n", "        # outlier step\n", "        outlier_step = 1.5 * IQR\n", "        \n", "        # Determine a list of indices of outliers for feature col\n", "        outlier_list_col = df[(df[col] < Q1 - outlier_step) | (df[col] > Q3 + outlier_step )].index\n", "        \n", "        # append the found outlier indices for col to the list of outlier indices \n", "        outlier_indices.extend(outlier_list_col)\n", "        \n", "    # select observations containing more than 2 outliers\n", "    outlier_indices = Counter(outlier_indices)        \n", "    multiple_outliers = list( k for k, v in outlier_indices.items() if v > n )\n", "    \n", "    return multiple_outliers  \n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# process category features"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=448)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>wrapsLoop</th>\n", "      <th>wrapsTryCatch</th>\n", "      <th>wrapsIfElse</th>\n", "      <th>wrongMethodRef</th>\n", "      <th>constChange</th>\n", "      <th>unwrapIfElse</th>\n", "      <th>unwrapTryCatch</th>\n", "      <th>expArithMod</th>\n", "      <th>codeMove</th>\n", "      <th>expLogicExpand</th>\n", "      <th>...</th>\n", "      <th>typeOfFaultyStmt_ NewClass</th>\n", "      <th>typeOfFaultyStmt_ OperatorAssignment</th>\n", "      <th>typeOfFaultyStmt_ Parameter</th>\n", "      <th>typeOfFaultyStmt_ Return</th>\n", "      <th>typeOfFaultyStmt_ Switch</th>\n", "      <th>typeOfFaultyStmt_ Throw</th>\n", "      <th>typeOfFaultyStmt_ Try</th>\n", "      <th>typeOfFaultyStmt_ TypeReference</th>\n", "      <th>typeOfFaultyStmt_ UnaryOperator</th>\n", "      <th>typeOfFaultyStmt_ While</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 448 columns</p>\n", "</div>"], "text/plain": ["   wrapsLoop  wrapsTryCatch  wrapsIfElse  wrongMethodRef  constChange  \\\n", "0        0.0            0.0          0.0             0.0          0.0   \n", "1        0.0            0.0          0.0             0.0          0.0   \n", "\n", "   unwrapIfElse  unwrapTryCatch  expArithMod  codeMove  expLogicExpand  ...  \\\n", "0           0.0             0.0          0.0       0.0             0.0  ...   \n", "1           0.0             0.0          0.0       0.0             0.0  ...   \n", "\n", "   typeOfFaultyStmt_ NewClass  typeOfFaultyStmt_ OperatorAssignment  \\\n", "0                         0.0                                   0.0   \n", "1                         0.0                                   0.0   \n", "\n", "   typeOfFaultyStmt_ Parameter  typeOfFaultyStmt_ Return  \\\n", "0                          0.0                       0.0   \n", "1                          0.0                       0.0   \n", "\n", "   typeOfFaultyStmt_ Switch  typeOfFaultyStmt_ Throw  typeOfFaultyStmt_ Try  \\\n", "0                       0.0                      0.0                    0.0   \n", "1                       0.0                      0.0                    0.0   \n", "\n", "   typeOfFaultyStmt_ TypeReference  typeOfFaultyStmt_ UnaryOperator  \\\n", "0                              0.0                              0.0   \n", "1                              0.0                              0.0   \n", "\n", "   typeOfFaultyStmt_ While  \n", "0                      0.0  \n", "1                      0.0  \n", "\n", "[2 rows x 448 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "X_train.head(2)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": true}, "outputs": [], "source": ["from imblearn.over_sampling import SMOTE\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Shuffle the training and test data"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"scrolled": true}, "outputs": [], "source": ["from sklearn.utils import shuffle\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0]\tvalidation_0-mae:0.45576\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.50589\n", "[2]\tvalidation_0-mae:0.53598\n", "[3]\tvalidation_0-mae:0.55462\n", "[4]\tvalidation_0-mae:0.53331\n", "[5]\tvalidation_0-mae:0.49818\n", "[6]\tvalidation_0-mae:0.53738\n", "[7]\tvalidation_0-mae:0.56470\n", "[8]\tvalidation_0-mae:0.53174\n", "[9]\tvalidation_0-mae:0.53358\n", "[10]\tvalidation_0-mae:0.53259\n", "[11]\tvalidation_0-mae:0.51714\n", "[12]\tvalidation_0-mae:0.51483\n", "[13]\tvalidation_0-mae:0.49568\n", "[14]\tvalidation_0-mae:0.49606\n", "[15]\tvalidation_0-mae:0.46589\n", "[16]\tvalidation_0-mae:0.46474\n", "[17]\tvalidation_0-mae:0.46564\n", "[18]\tvalidation_0-mae:0.45268\n", "[19]\tvalidation_0-mae:0.45596\n", "[20]\tvalidation_0-mae:0.42860\n", "[21]\tvalidation_0-mae:0.40329\n", "[22]\tvalidation_0-mae:0.40459\n", "[23]\tvalidation_0-mae:0.37975\n", "[24]\tvalidation_0-mae:0.38146\n", "[25]\tvalidation_0-mae:0.38056\n", "[26]\tvalidation_0-mae:0.37607\n", "[27]\tvalidation_0-mae:0.36858\n", "[28]\tvalidation_0-mae:0.35731\n", "[29]\tvalidation_0-mae:0.34211\n", "[30]\tvalidation_0-mae:0.34137\n", "[31]\tvalidation_0-mae:0.33702\n", "[32]\tvalidation_0-mae:0.33661\n", "[33]\tvalidation_0-mae:0.33474\n", "[34]\tvalidation_0-mae:0.33861\n", "[35]\tvalidation_0-mae:0.33442\n", "[36]\tvalidation_0-mae:0.33318\n", "[37]\tvalidation_0-mae:0.33350\n", "[38]\tvalidation_0-mae:0.34423\n", "[39]\tvalidation_0-mae:0.34817\n", "[40]\tvalidation_0-mae:0.34726\n", "[41]\tvalidation_0-mae:0.34895\n", "[42]\tvalidation_0-mae:0.33717\n", "[43]\tvalidation_0-mae:0.33719\n", "[44]\tvalidation_0-mae:0.33700\n", "[45]\tvalidation_0-mae:0.32581\n", "[46]\tvalidation_0-mae:0.32352\n", "[47]\tvalidation_0-mae:0.32476\n", "[48]\tvalidation_0-mae:0.32585\n", "[49]\tvalidation_0-mae:0.33355\n", "[50]\tvalidation_0-mae:0.32482\n", "[51]\tvalidation_0-mae:0.32347\n", "[52]\tvalidation_0-mae:0.33003\n", "[53]\tvalidation_0-mae:0.33556\n", "[54]\tvalidation_0-mae:0.33626\n", "[55]\tvalidation_0-mae:0.34398\n", "[56]\tvalidation_0-mae:0.34310\n", "[57]\tvalidation_0-mae:0.33676\n", "[58]\tvalidation_0-mae:0.33581\n", "[59]\tvalidation_0-mae:0.33574\n", "[60]\tvalidation_0-mae:0.32911\n", "[61]\tvalidation_0-mae:0.33458\n", "[62]\tvalidation_0-mae:0.33401\n", "[63]\tvalidation_0-mae:0.33123\n", "[64]\tvalidation_0-mae:0.33136\n", "[65]\tvalidation_0-mae:0.32986\n", "[66]\tvalidation_0-mae:0.33035\n", "[67]\tvalidation_0-mae:0.32937\n", "[68]\tvalidation_0-mae:0.32975\n", "[69]\tvalidation_0-mae:0.33218\n", "[70]\tvalidation_0-mae:0.32457\n", "[71]\tvalidation_0-mae:0.32364\n", "[72]\tvalidation_0-mae:0.32265\n", "[73]\tvalidation_0-mae:0.31559\n", "[74]\tvalidation_0-mae:0.31479\n", "[75]\tvalidation_0-mae:0.31570\n", "[76]\tvalidation_0-mae:0.31321\n", "[77]\tvalidation_0-mae:0.31269\n", "[78]\tvalidation_0-mae:0.31195\n", "[79]\tvalidation_0-mae:0.31211\n", "[80]\tvalidation_0-mae:0.31116\n", "[81]\tvalidation_0-mae:0.31098\n", "[82]\tvalidation_0-mae:0.30706\n", "[83]\tvalidation_0-mae:0.30907\n", "[84]\tvalidation_0-mae:0.29778\n", "[85]\tvalidation_0-mae:0.30038\n", "[86]\tvalidation_0-mae:0.30821\n", "[87]\tvalidation_0-mae:0.30681\n", "[88]\tvalidation_0-mae:0.30625\n", "[89]\tvalidation_0-mae:0.30608\n", "[90]\tvalidation_0-mae:0.30533\n", "[91]\tvalidation_0-mae:0.30584\n", "[92]\tvalidation_0-mae:0.31098\n", "[93]\tvalidation_0-mae:0.31175\n", "[94]\tvalidation_0-mae:0.31107\n", "[95]\tvalidation_0-mae:0.31169\n", "[96]\tvalidation_0-mae:0.30986\n", "[97]\tvalidation_0-mae:0.31202\n", "[98]\tvalidation_0-mae:0.31103\n", "[99]\tvalidation_0-mae:0.31131\n", "f1 score: 0.8304498269896193\n", "acc score: 0.7644230769230769\n", "precision score: 0.9836065573770492\n", "recall score: 0.718562874251497\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import xgboost as xgb\n", "from sklearn.metrics import recall_score\n", "from sklearn.metrics import f1_score\n", "from sklearn.metrics import precision_score\n", "from sklearn.metrics import accuracy_score\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  \n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (8955, 204)\n", "overfitting in train (6977, 204)\n", "correct in train (1978, 204)\n", "====================\n", "Total test:  (1347, 204)\n", "overfitting in test (1322, 204)\n", "correct in test (25, 204)\n", "Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=449)\n", "[0]\tvalidation_0-mae:0.47401\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.42596\n", "[2]\tvalidation_0-mae:0.40332\n", "[3]\tvalidation_0-mae:0.40045\n", "[4]\tvalidation_0-mae:0.39066\n", "[5]\tvalidation_0-mae:0.38721\n", "[6]\tvalidation_0-mae:0.39180\n", "[7]\tvalidation_0-mae:0.37915\n", "[8]\tvalidation_0-mae:0.35740\n", "[9]\tvalidation_0-mae:0.35317\n", "[10]\tvalidation_0-mae:0.34269\n", "[11]\tvalidation_0-mae:0.33742\n", "[12]\tvalidation_0-mae:0.33955\n", "[13]\tvalidation_0-mae:0.34371\n", "[14]\tvalidation_0-mae:0.33838\n", "[15]\tvalidation_0-mae:0.32279\n", "[16]\tvalidation_0-mae:0.31899\n", "[17]\tvalidation_0-mae:0.31086\n", "[18]\tvalidation_0-mae:0.30516\n", "[19]\tvalidation_0-mae:0.31194\n", "[20]\tvalidation_0-mae:0.30308\n", "[21]\tvalidation_0-mae:0.30014\n", "[22]\tvalidation_0-mae:0.30189\n", "[23]\tvalidation_0-mae:0.30610\n", "[24]\tvalidation_0-mae:0.30530\n", "[25]\tvalidation_0-mae:0.30495\n", "[26]\tvalidation_0-mae:0.30884\n", "[27]\tvalidation_0-mae:0.31387\n", "[28]\tvalidation_0-mae:0.30758\n", "[29]\tvalidation_0-mae:0.30959\n", "[30]\tvalidation_0-mae:0.31079\n", "[31]\tvalidation_0-mae:0.31098\n", "[32]\tvalidation_0-mae:0.30593\n", "[33]\tvalidation_0-mae:0.30342\n", "[34]\tvalidation_0-mae:0.30584\n", "[35]\tvalidation_0-mae:0.30907\n", "[36]\tvalidation_0-mae:0.31031\n", "[37]\tvalidation_0-mae:0.30812\n", "[38]\tvalidation_0-mae:0.30435\n", "[39]\tvalidation_0-mae:0.30662\n", "[40]\tvalidation_0-mae:0.30570\n", "[41]\tvalidation_0-mae:0.30366\n", "[42]\tvalidation_0-mae:0.29916\n", "[43]\tvalidation_0-mae:0.29945\n", "[44]\tvalidation_0-mae:0.29765\n", "[45]\tvalidation_0-mae:0.29659\n", "[46]\tvalidation_0-mae:0.29973\n", "[47]\tvalidation_0-mae:0.29809\n", "[48]\tvalidation_0-mae:0.29695\n", "[49]\tvalidation_0-mae:0.29678\n", "[50]\tvalidation_0-mae:0.29294\n", "[51]\tvalidation_0-mae:0.29349\n", "[52]\tvalidation_0-mae:0.29471\n", "[53]\tvalidation_0-mae:0.29340\n", "[54]\tvalidation_0-mae:0.29251\n", "[55]\tvalidation_0-mae:0.29041\n", "[56]\tvalidation_0-mae:0.29691\n", "[57]\tvalidation_0-mae:0.29623\n", "[58]\tvalidation_0-mae:0.29627\n", "[59]\tvalidation_0-mae:0.29748\n", "[60]\tvalidation_0-mae:0.30118\n", "[61]\tvalidation_0-mae:0.30145\n", "[62]\tvalidation_0-mae:0.30073\n", "[63]\tvalidation_0-mae:0.29940\n", "[64]\tvalidation_0-mae:0.30120\n", "[65]\tvalidation_0-mae:0.30496\n", "[66]\tvalidation_0-mae:0.30551\n", "[67]\tvalidation_0-mae:0.30651\n", "[68]\tvalidation_0-mae:0.30452\n", "[69]\tvalidation_0-mae:0.30286\n", "[70]\tvalidation_0-mae:0.30119\n", "[71]\tvalidation_0-mae:0.30075\n", "[72]\tvalidation_0-mae:0.29866\n", "[73]\tvalidation_0-mae:0.29902\n", "[74]\tvalidation_0-mae:0.30310\n", "[75]\tvalidation_0-mae:0.30220\n", "[76]\tvalidation_0-mae:0.30031\n", "[77]\tvalidation_0-mae:0.30091\n", "[78]\tvalidation_0-mae:0.30192\n", "[79]\tvalidation_0-mae:0.30007\n", "[80]\tvalidation_0-mae:0.30157\n", "[81]\tvalidation_0-mae:0.29966\n", "[82]\tvalidation_0-mae:0.29741\n", "[83]\tvalidation_0-mae:0.29776\n", "[84]\tvalidation_0-mae:0.29667\n", "[85]\tvalidation_0-mae:0.30344\n", "Stopping. Best iteration:\n", "[55]\tvalidation_0-mae:0.29041\n", "\n", "f1 score: 0.8713375796178344\n", "acc score: 0.7750556792873051\n", "precision score: 0.9932236205227493\n", "recall score: 0.7760968229954615\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["test =  totaltraining[ totaltraining['id'].str.contains(r'Chart')]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Chart')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  \n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (9207, 204)\n", "overfitting in train (7267, 204)\n", "correct in train (1940, 204)\n", "====================\n", "Total test:  (1095, 204)\n", "overfitting in test (1032, 204)\n", "correct in test (63, 204)\n", "Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewArray', 'typeOfFaultyStmt_ NewClass',\n", "       'typeOfFaultyStmt_ OperatorAssignment', 'typeOfFaultyStmt_ Parameter',\n", "       'typeOfFaultyStmt_ Return', 'typeOfFaultyStmt_ Switch',\n", "       'typeOfFaultyStmt_ Throw', 'typeOfFaultyStmt_ Try',\n", "       'typeOfFaultyStmt_ TypeReference', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=445)\n", "[0]\tvalidation_0-mae:0.45771\n", "Will train until validation_0-mae hasn't improved in 20 rounds.\n", "[1]\tvalidation_0-mae:0.43977\n", "[2]\tvalidation_0-mae:0.42973\n", "[3]\tvalidation_0-mae:0.42481\n", "[4]\tvalidation_0-mae:0.41161\n", "[5]\tvalidation_0-mae:0.41200\n", "[6]\tvalidation_0-mae:0.41117\n", "[7]\tvalidation_0-mae:0.39179\n", "[8]\tvalidation_0-mae:0.38300\n", "[9]\tvalidation_0-mae:0.38126\n", "[10]\tvalidation_0-mae:0.37785\n", "[11]\tvalidation_0-mae:0.38082\n", "[12]\tvalidation_0-mae:0.36816\n", "[13]\tvalidation_0-mae:0.36879\n", "[14]\tvalidation_0-mae:0.36515\n", "[15]\tvalidation_0-mae:0.36047\n", "[16]\tvalidation_0-mae:0.36083\n", "[17]\tvalidation_0-mae:0.35538\n", "[18]\tvalidation_0-mae:0.35441\n", "[19]\tvalidation_0-mae:0.34823\n", "[20]\tvalidation_0-mae:0.34257\n", "[21]\tvalidation_0-mae:0.34018\n", "[22]\tvalidation_0-mae:0.34584\n", "[23]\tvalidation_0-mae:0.34612\n", "[24]\tvalidation_0-mae:0.34409\n", "[25]\tvalidation_0-mae:0.34444\n", "[26]\tvalidation_0-mae:0.34588\n", "[27]\tvalidation_0-mae:0.34578\n", "[28]\tvalidation_0-mae:0.34690\n", "[29]\tvalidation_0-mae:0.35849\n", "[30]\tvalidation_0-mae:0.35951\n", "[31]\tvalidation_0-mae:0.35889\n", "[32]\tvalidation_0-mae:0.35609\n", "[33]\tvalidation_0-mae:0.35663\n", "[34]\tvalidation_0-mae:0.35088\n", "[35]\tvalidation_0-mae:0.34321\n", "[36]\tvalidation_0-mae:0.34409\n", "[37]\tvalidation_0-mae:0.34362\n", "[38]\tvalidation_0-mae:0.34356\n", "[39]\tvalidation_0-mae:0.34515\n", "[40]\tvalidation_0-mae:0.34772\n", "[41]\tvalidation_0-mae:0.34076\n", "Stopping. Best iteration:\n", "[21]\tvalidation_0-mae:0.34018\n", "\n", "f1 score: 0.841470104223807\n", "acc score: 0.7360730593607306\n", "precision score: 0.9696586599241467\n", "recall score: 0.7432170542635659\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["test =  totaltraining[ totaltraining['id'].str.contains(r'Lang')]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Lang')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=20, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  "]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (9711, 204)\n", "overfitting in train (7950, 204)\n", "correct in train (1761, 204)\n", "====================\n", "Total test:  (591, 204)\n", "overfitting in test (349, 204)\n", "correct in test (242, 204)\n", "Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=444)\n", "[0]\tvalidation_0-mae:0.47533\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.43417\n", "[2]\tvalidation_0-mae:0.41984\n", "[3]\tvalidation_0-mae:0.40945\n", "[4]\tvalidation_0-mae:0.39735\n", "[5]\tvalidation_0-mae:0.38119\n", "[6]\tvalidation_0-mae:0.38697\n", "[7]\tvalidation_0-mae:0.37587\n", "[8]\tvalidation_0-mae:0.35840\n", "[9]\tvalidation_0-mae:0.35557\n", "[10]\tvalidation_0-mae:0.34716\n", "[11]\tvalidation_0-mae:0.33091\n", "[12]\tvalidation_0-mae:0.33323\n", "[13]\tvalidation_0-mae:0.33420\n", "[14]\tvalidation_0-mae:0.33020\n", "[15]\tvalidation_0-mae:0.32220\n", "[16]\tvalidation_0-mae:0.31497\n", "[17]\tvalidation_0-mae:0.31389\n", "[18]\tvalidation_0-mae:0.31143\n", "[19]\tvalidation_0-mae:0.31797\n", "[20]\tvalidation_0-mae:0.32100\n", "[21]\tvalidation_0-mae:0.32569\n", "[22]\tvalidation_0-mae:0.32161\n", "[23]\tvalidation_0-mae:0.33083\n", "[24]\tvalidation_0-mae:0.33239\n", "[25]\tvalidation_0-mae:0.33374\n", "[26]\tvalidation_0-mae:0.33644\n", "[27]\tvalidation_0-mae:0.34279\n", "[28]\tvalidation_0-mae:0.34346\n", "[29]\tvalidation_0-mae:0.34088\n", "[30]\tvalidation_0-mae:0.33274\n", "[31]\tvalidation_0-mae:0.33416\n", "[32]\tvalidation_0-mae:0.33436\n", "[33]\tvalidation_0-mae:0.33451\n", "[34]\tvalidation_0-mae:0.33370\n", "[35]\tvalidation_0-mae:0.33426\n", "[36]\tvalidation_0-mae:0.33398\n", "[37]\tvalidation_0-mae:0.32941\n", "[38]\tvalidation_0-mae:0.32591\n", "[39]\tvalidation_0-mae:0.32434\n", "[40]\tvalidation_0-mae:0.32006\n", "[41]\tvalidation_0-mae:0.31974\n", "[42]\tvalidation_0-mae:0.31936\n", "[43]\tvalidation_0-mae:0.31943\n", "[44]\tvalidation_0-mae:0.31941\n", "[45]\tvalidation_0-mae:0.31835\n", "[46]\tvalidation_0-mae:0.32015\n", "[47]\tvalidation_0-mae:0.31645\n", "[48]\tvalidation_0-mae:0.31907\n", "Stopping. Best iteration:\n", "[18]\tvalidation_0-mae:0.31143\n", "\n", "f1 score: 0.7737909516380655\n", "acc score: 0.754653130287648\n", "precision score: 0.8493150684931506\n", "recall score: 0.7106017191977078\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Jackrabbit\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'ackrabbit')]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'ackrabbit')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  "]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (9944, 204)\n", "overfitting in train (8022, 204)\n", "correct in train (1922, 204)\n", "====================\n", "Total test:  (358, 204)\n", "overfitting in test (277, 204)\n", "correct in test (81, 204)\n", "Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=447)\n", "[0]\tvalidation_0-mae:0.45111\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.40436\n", "[2]\tvalidation_0-mae:0.37999\n", "[3]\tvalidation_0-mae:0.35535\n", "[4]\tvalidation_0-mae:0.35887\n", "[5]\tvalidation_0-mae:0.35024\n", "[6]\tvalidation_0-mae:0.33886\n", "[7]\tvalidation_0-mae:0.33662\n", "[8]\tvalidation_0-mae:0.33489\n", "[9]\tvalidation_0-mae:0.32868\n", "[10]\tvalidation_0-mae:0.32706\n", "[11]\tvalidation_0-mae:0.32841\n", "[12]\tvalidation_0-mae:0.32060\n", "[13]\tvalidation_0-mae:0.31139\n", "[14]\tvalidation_0-mae:0.29727\n", "[15]\tvalidation_0-mae:0.29881\n", "[16]\tvalidation_0-mae:0.31028\n", "[17]\tvalidation_0-mae:0.32218\n", "[18]\tvalidation_0-mae:0.31674\n", "[19]\tvalidation_0-mae:0.31589\n", "[20]\tvalidation_0-mae:0.31680\n", "[21]\tvalidation_0-mae:0.33307\n", "[22]\tvalidation_0-mae:0.33788\n", "[23]\tvalidation_0-mae:0.33076\n", "[24]\tvalidation_0-mae:0.33115\n", "[25]\tvalidation_0-mae:0.34568\n", "[26]\tvalidation_0-mae:0.34109\n", "[27]\tvalidation_0-mae:0.33619\n", "[28]\tvalidation_0-mae:0.33124\n", "[29]\tvalidation_0-mae:0.33233\n", "[30]\tvalidation_0-mae:0.33025\n", "[31]\tvalidation_0-mae:0.33110\n", "[32]\tvalidation_0-mae:0.32881\n", "[33]\tvalidation_0-mae:0.32789\n", "[34]\tvalidation_0-mae:0.33144\n", "[35]\tvalidation_0-mae:0.33140\n", "[36]\tvalidation_0-mae:0.32980\n", "[37]\tvalidation_0-mae:0.33548\n", "[38]\tvalidation_0-mae:0.35043\n", "[39]\tvalidation_0-mae:0.35340\n", "[40]\tvalidation_0-mae:0.35220\n", "[41]\tvalidation_0-mae:0.35719\n", "[42]\tvalidation_0-mae:0.36046\n", "[43]\tvalidation_0-mae:0.36422\n", "[44]\tvalidation_0-mae:0.36029\n", "Stopping. Best iteration:\n", "[14]\tvalidation_0-mae:0.29727\n", "\n", "f1 score: 0.8754863813229572\n", "acc score: 0.8212290502793296\n", "precision score: 0.9493670886075949\n", "recall score: 0.8122743682310469\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Accumulo\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'ccumulo')]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'ccumulo')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  "]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10094, 204)\n", "overfitting in train (8132, 204)\n", "correct in train (1962, 204)\n", "====================\n", "Total test:  (208, 204)\n", "overfitting in test (167, 204)\n", "correct in test (41, 204)\n", "Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=448)\n", "[0]\tvalidation_0-mae:0.45576\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.50589\n", "[2]\tvalidation_0-mae:0.53598\n", "[3]\tvalidation_0-mae:0.55462\n", "[4]\tvalidation_0-mae:0.53331\n", "[5]\tvalidation_0-mae:0.49818\n", "[6]\tvalidation_0-mae:0.53738\n", "[7]\tvalidation_0-mae:0.56470\n", "[8]\tvalidation_0-mae:0.53174\n", "[9]\tvalidation_0-mae:0.53358\n", "[10]\tvalidation_0-mae:0.53259\n", "[11]\tvalidation_0-mae:0.51714\n", "[12]\tvalidation_0-mae:0.51483\n", "[13]\tvalidation_0-mae:0.49568\n", "[14]\tvalidation_0-mae:0.49606\n", "[15]\tvalidation_0-mae:0.46589\n", "[16]\tvalidation_0-mae:0.46474\n", "[17]\tvalidation_0-mae:0.46564\n", "[18]\tvalidation_0-mae:0.45268\n", "[19]\tvalidation_0-mae:0.45596\n", "[20]\tvalidation_0-mae:0.42860\n", "[21]\tvalidation_0-mae:0.40329\n", "[22]\tvalidation_0-mae:0.40459\n", "[23]\tvalidation_0-mae:0.37975\n", "[24]\tvalidation_0-mae:0.38146\n", "[25]\tvalidation_0-mae:0.38056\n", "[26]\tvalidation_0-mae:0.37607\n", "[27]\tvalidation_0-mae:0.36858\n", "[28]\tvalidation_0-mae:0.35731\n", "[29]\tvalidation_0-mae:0.34211\n", "[30]\tvalidation_0-mae:0.34137\n", "[31]\tvalidation_0-mae:0.33702\n", "[32]\tvalidation_0-mae:0.33661\n", "[33]\tvalidation_0-mae:0.33474\n", "[34]\tvalidation_0-mae:0.33861\n", "[35]\tvalidation_0-mae:0.33442\n", "[36]\tvalidation_0-mae:0.33318\n", "[37]\tvalidation_0-mae:0.33350\n", "[38]\tvalidation_0-mae:0.34423\n", "[39]\tvalidation_0-mae:0.34817\n", "[40]\tvalidation_0-mae:0.34726\n", "[41]\tvalidation_0-mae:0.34895\n", "[42]\tvalidation_0-mae:0.33717\n", "[43]\tvalidation_0-mae:0.33719\n", "[44]\tvalidation_0-mae:0.33700\n", "[45]\tvalidation_0-mae:0.32581\n", "[46]\tvalidation_0-mae:0.32352\n", "[47]\tvalidation_0-mae:0.32476\n", "[48]\tvalidation_0-mae:0.32585\n", "[49]\tvalidation_0-mae:0.33355\n", "[50]\tvalidation_0-mae:0.32482\n", "[51]\tvalidation_0-mae:0.32347\n", "[52]\tvalidation_0-mae:0.33003\n", "[53]\tvalidation_0-mae:0.33556\n", "[54]\tvalidation_0-mae:0.33626\n", "[55]\tvalidation_0-mae:0.34398\n", "[56]\tvalidation_0-mae:0.34310\n", "[57]\tvalidation_0-mae:0.33676\n", "[58]\tvalidation_0-mae:0.33581\n", "[59]\tvalidation_0-mae:0.33574\n", "[60]\tvalidation_0-mae:0.32911\n", "[61]\tvalidation_0-mae:0.33458\n", "[62]\tvalidation_0-mae:0.33401\n", "[63]\tvalidation_0-mae:0.33123\n", "[64]\tvalidation_0-mae:0.33136\n", "[65]\tvalidation_0-mae:0.32986\n", "[66]\tvalidation_0-mae:0.33035\n", "[67]\tvalidation_0-mae:0.32937\n", "[68]\tvalidation_0-mae:0.32975\n", "[69]\tvalidation_0-mae:0.33218\n", "[70]\tvalidation_0-mae:0.32457\n", "[71]\tvalidation_0-mae:0.32364\n", "[72]\tvalidation_0-mae:0.32265\n", "[73]\tvalidation_0-mae:0.31559\n", "[74]\tvalidation_0-mae:0.31479\n", "[75]\tvalidation_0-mae:0.31570\n", "[76]\tvalidation_0-mae:0.31321\n", "[77]\tvalidation_0-mae:0.31269\n", "[78]\tvalidation_0-mae:0.31195\n", "[79]\tvalidation_0-mae:0.31211\n", "[80]\tvalidation_0-mae:0.31116\n", "[81]\tvalidation_0-mae:0.31098\n", "[82]\tvalidation_0-mae:0.30706\n", "[83]\tvalidation_0-mae:0.30907\n", "[84]\tvalidation_0-mae:0.29778\n", "[85]\tvalidation_0-mae:0.30038\n", "[86]\tvalidation_0-mae:0.30821\n", "[87]\tvalidation_0-mae:0.30681\n", "[88]\tvalidation_0-mae:0.30625\n", "[89]\tvalidation_0-mae:0.30608\n", "[90]\tvalidation_0-mae:0.30533\n", "[91]\tvalidation_0-mae:0.30584\n", "[92]\tvalidation_0-mae:0.31098\n", "[93]\tvalidation_0-mae:0.31175\n", "[94]\tvalidation_0-mae:0.31107\n", "[95]\tvalidation_0-mae:0.31169\n", "[96]\tvalidation_0-mae:0.30986\n", "[97]\tvalidation_0-mae:0.31202\n", "[98]\tvalidation_0-mae:0.31103\n", "[99]\tvalidation_0-mae:0.31131\n", "f1 score: 0.8304498269896193\n", "acc score: 0.7644230769230769\n", "precision score: 0.9836065573770492\n", "recall score: 0.718562874251497\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Traccar\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'raccar')]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'raccar')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  "]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (5408, 204)\n", "overfitting in train (3625, 204)\n", "correct in train (1783, 204)\n", "====================\n", "Total test:  (4894, 204)\n", "overfitting in test (4674, 204)\n", "correct in test (220, 204)\n", "Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=435)\n", "[0]\tvalidation_0-mae:0.47912\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.47091\n", "[2]\tvalidation_0-mae:0.45206\n", "[3]\tvalidation_0-mae:0.45016\n", "[4]\tvalidation_0-mae:0.43950\n", "[5]\tvalidation_0-mae:0.43382\n", "[6]\tvalidation_0-mae:0.43771\n", "[7]\tvalidation_0-mae:0.43010\n", "[8]\tvalidation_0-mae:0.43220\n", "[9]\tvalidation_0-mae:0.42973\n", "[10]\tvalidation_0-mae:0.42386\n", "[11]\tvalidation_0-mae:0.42824\n", "[12]\tvalidation_0-mae:0.43016\n", "[13]\tvalidation_0-mae:0.43008\n", "[14]\tvalidation_0-mae:0.41775\n", "[15]\tvalidation_0-mae:0.41679\n", "[16]\tvalidation_0-mae:0.40723\n", "[17]\tvalidation_0-mae:0.40700\n", "[18]\tvalidation_0-mae:0.40818\n", "[19]\tvalidation_0-mae:0.40688\n", "[20]\tvalidation_0-mae:0.40632\n", "[21]\tvalidation_0-mae:0.40297\n", "[22]\tvalidation_0-mae:0.40790\n", "[23]\tvalidation_0-mae:0.40488\n", "[24]\tvalidation_0-mae:0.40897\n", "[25]\tvalidation_0-mae:0.41260\n", "[26]\tvalidation_0-mae:0.41598\n", "[27]\tvalidation_0-mae:0.41811\n", "[28]\tvalidation_0-mae:0.42075\n", "[29]\tvalidation_0-mae:0.42179\n", "[30]\tvalidation_0-mae:0.42229\n", "[31]\tvalidation_0-mae:0.42498\n", "[32]\tvalidation_0-mae:0.42580\n", "[33]\tvalidation_0-mae:0.42478\n", "[34]\tvalidation_0-mae:0.41792\n", "[35]\tvalidation_0-mae:0.41735\n", "[36]\tvalidation_0-mae:0.41667\n", "[37]\tvalidation_0-mae:0.41861\n", "[38]\tvalidation_0-mae:0.41553\n", "[39]\tvalidation_0-mae:0.41741\n", "[40]\tvalidation_0-mae:0.41728\n", "[41]\tvalidation_0-mae:0.41968\n", "[42]\tvalidation_0-mae:0.42057\n", "[43]\tvalidation_0-mae:0.42000\n", "[44]\tvalidation_0-mae:0.41855\n", "[45]\tvalidation_0-mae:0.41738\n", "[46]\tvalidation_0-mae:0.41550\n", "[47]\tvalidation_0-mae:0.41652\n", "[48]\tvalidation_0-mae:0.41272\n", "[49]\tvalidation_0-mae:0.41279\n", "[50]\tvalidation_0-mae:0.41653\n", "[51]\tvalidation_0-mae:0.41586\n", "Stopping. Best iteration:\n", "[21]\tvalidation_0-mae:0.40297\n", "\n", "f1 score: 0.7858061198251478\n", "acc score: 0.6595831630568042\n", "precision score: 0.9845360824742269\n", "recall score: 0.6538296961916987\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Math\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Math') |  totaltraining['id'].str.contains(r'math')]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Math') & ~ totaltraining['id'].str.contains(r'math')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  "]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10173, 204)\n", "overfitting in train (8171, 204)\n", "correct in train (2002, 204)\n", "====================\n", "Total test:  (129, 204)\n", "overfitting in test (128, 204)\n", "correct in test (1, 204)\n", "Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=450)\n", "[0]\tvalidation_0-mae:0.43700\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.37927\n", "[2]\tvalidation_0-mae:0.32664\n", "[3]\tvalidation_0-mae:0.27683\n", "[4]\tvalidation_0-mae:0.27118\n", "[5]\tvalidation_0-mae:0.24947\n", "[6]\tvalidation_0-mae:0.22700\n", "[7]\tvalidation_0-mae:0.21227\n", "[8]\tvalidation_0-mae:0.19133\n", "[9]\tvalidation_0-mae:0.18060\n", "[10]\tvalidation_0-mae:0.16845\n", "[11]\tvalidation_0-mae:0.16049\n", "[12]\tvalidation_0-mae:0.16545\n", "[13]\tvalidation_0-mae:0.14917\n", "[14]\tvalidation_0-mae:0.13766\n", "[15]\tvalidation_0-mae:0.14111\n", "[16]\tvalidation_0-mae:0.13113\n", "[17]\tvalidation_0-mae:0.12469\n", "[18]\tvalidation_0-mae:0.11938\n", "[19]\tvalidation_0-mae:0.11805\n", "[20]\tvalidation_0-mae:0.12007\n", "[21]\tvalidation_0-mae:0.11400\n", "[22]\tvalidation_0-mae:0.11178\n", "[23]\tvalidation_0-mae:0.10916\n", "[24]\tvalidation_0-mae:0.10429\n", "[25]\tvalidation_0-mae:0.10486\n", "[26]\tvalidation_0-mae:0.10473\n", "[27]\tvalidation_0-mae:0.10378\n", "[28]\tvalidation_0-mae:0.09563\n", "[29]\tvalidation_0-mae:0.09455\n", "[30]\tvalidation_0-mae:0.08937\n", "[31]\tvalidation_0-mae:0.08934\n", "[32]\tvalidation_0-mae:0.08939\n", "[33]\tvalidation_0-mae:0.09338\n", "[34]\tvalidation_0-mae:0.09136\n", "[35]\tvalidation_0-mae:0.09119\n", "[36]\tvalidation_0-mae:0.09235\n", "[37]\tvalidation_0-mae:0.09522\n", "[38]\tvalidation_0-mae:0.09500\n", "[39]\tvalidation_0-mae:0.09547\n", "[40]\tvalidation_0-mae:0.09563\n", "[41]\tvalidation_0-mae:0.09531\n", "[42]\tvalidation_0-mae:0.09474\n", "[43]\tvalidation_0-mae:0.09238\n", "[44]\tvalidation_0-mae:0.09628\n", "[45]\tvalidation_0-mae:0.10143\n", "[46]\tvalidation_0-mae:0.10003\n", "[47]\tvalidation_0-mae:0.09810\n", "[48]\tvalidation_0-mae:0.09584\n", "[49]\tvalidation_0-mae:0.09589\n", "[50]\tvalidation_0-mae:0.09413\n", "[51]\tvalidation_0-mae:0.09444\n", "[52]\tvalidation_0-mae:0.09390\n", "[53]\tvalidation_0-mae:0.09413\n", "[54]\tvalidation_0-mae:0.09102\n", "[55]\tvalidation_0-mae:0.09322\n", "[56]\tvalidation_0-mae:0.09408\n", "[57]\tvalidation_0-mae:0.09447\n", "[58]\tvalidation_0-mae:0.09659\n", "[59]\tvalidation_0-mae:0.09582\n", "[60]\tvalidation_0-mae:0.09556\n", "[61]\tvalidation_0-mae:0.09540\n", "Stopping. Best iteration:\n", "[31]\tvalidation_0-mae:0.08934\n", "\n", "f1 score: 0.976\n", "acc score: 0.9534883720930233\n", "precision score: 1.0\n", "recall score: 0.953125\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Libra\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'libra') |  totaltraining['id'].str.contains(r'Bears_210')]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'libra') & ~ totaltraining['id'].str.contains(r'Bears_210')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()  "]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10057, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1758, 204)\n", "====================\n", "Total test:  (245, 204)\n", "overfitting in test (0, 204)\n", "correct in test (245, 204)\n", "Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=450)\n", "[0]\tvalidation_0-mae:0.44627\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.40505\n", "[2]\tvalidation_0-mae:0.38036\n", "[3]\tvalidation_0-mae:0.37361\n", "[4]\tvalidation_0-mae:0.37361\n", "[5]\tvalidation_0-mae:0.35942\n", "[6]\tvalidation_0-mae:0.34711\n", "[7]\tvalidation_0-mae:0.33765\n", "[8]\tvalidation_0-mae:0.32796\n", "[9]\tvalidation_0-mae:0.32115\n", "[10]\tvalidation_0-mae:0.32071\n", "[11]\tvalidation_0-mae:0.31760\n", "[12]\tvalidation_0-mae:0.31485\n", "[13]\tvalidation_0-mae:0.31714\n", "[14]\tvalidation_0-mae:0.31419\n", "[15]\tvalidation_0-mae:0.29836\n", "[16]\tvalidation_0-mae:0.29504\n", "[17]\tvalidation_0-mae:0.28937\n", "[18]\tvalidation_0-mae:0.28617\n", "[19]\tvalidation_0-mae:0.28618\n", "[20]\tvalidation_0-mae:0.28558\n", "[21]\tvalidation_0-mae:0.28390\n", "[22]\tvalidation_0-mae:0.28193\n", "[23]\tvalidation_0-mae:0.28383\n", "[24]\tvalidation_0-mae:0.28262\n", "[25]\tvalidation_0-mae:0.27951\n", "[26]\tvalidation_0-mae:0.27894\n", "[27]\tvalidation_0-mae:0.27764\n", "[28]\tvalidation_0-mae:0.27652\n", "[29]\tvalidation_0-mae:0.27322\n", "[30]\tvalidation_0-mae:0.27368\n", "[31]\tvalidation_0-mae:0.26724\n", "[32]\tvalidation_0-mae:0.26485\n", "[33]\tvalidation_0-mae:0.26382\n", "[34]\tvalidation_0-mae:0.26241\n", "[35]\tvalidation_0-mae:0.26202\n", "[36]\tvalidation_0-mae:0.26205\n", "[37]\tvalidation_0-mae:0.26241\n", "[38]\tvalidation_0-mae:0.25873\n", "[39]\tvalidation_0-mae:0.25873\n", "[40]\tvalidation_0-mae:0.25718\n", "[41]\tvalidation_0-mae:0.25625\n", "[42]\tvalidation_0-mae:0.25482\n", "[43]\tvalidation_0-mae:0.25034\n", "[44]\tvalidation_0-mae:0.25003\n", "[45]\tvalidation_0-mae:0.24988\n", "[46]\tvalidation_0-mae:0.24967\n", "[47]\tvalidation_0-mae:0.24856\n", "[48]\tvalidation_0-mae:0.24894\n", "[49]\tvalidation_0-mae:0.24819\n", "[50]\tvalidation_0-mae:0.24608\n", "[51]\tvalidation_0-mae:0.24259\n", "[52]\tvalidation_0-mae:0.24334\n", "[53]\tvalidation_0-mae:0.24377\n", "[54]\tvalidation_0-mae:0.24217\n", "[55]\tvalidation_0-mae:0.24195\n", "[56]\tvalidation_0-mae:0.24082\n", "[57]\tvalidation_0-mae:0.23912\n", "[58]\tvalidation_0-mae:0.23817\n", "[59]\tvalidation_0-mae:0.23770\n", "[60]\tvalidation_0-mae:0.23755\n", "[61]\tvalidation_0-mae:0.23773\n", "[62]\tvalidation_0-mae:0.23745\n", "[63]\tvalidation_0-mae:0.23722\n", "[64]\tvalidation_0-mae:0.23637\n", "[65]\tvalidation_0-mae:0.23569\n", "[66]\tvalidation_0-mae:0.23571\n", "[67]\tvalidation_0-mae:0.23357\n", "[68]\tvalidation_0-mae:0.23181\n", "[69]\tvalidation_0-mae:0.23190\n", "[70]\tvalidation_0-mae:0.23030\n", "[71]\tvalidation_0-mae:0.22983\n", "[72]\tvalidation_0-mae:0.22865\n", "[73]\tvalidation_0-mae:0.22513\n", "[74]\tvalidation_0-mae:0.22490\n", "[75]\tvalidation_0-mae:0.22423\n", "[76]\tvalidation_0-mae:0.22461\n", "[77]\tvalidation_0-mae:0.22370\n", "[78]\tvalidation_0-mae:0.22107\n", "[79]\tvalidation_0-mae:0.22057\n", "[80]\tvalidation_0-mae:0.22029\n", "[81]\tvalidation_0-mae:0.21976\n", "[82]\tvalidation_0-mae:0.21885\n", "[83]\tvalidation_0-mae:0.21960\n", "[84]\tvalidation_0-mae:0.21891\n", "[85]\tvalidation_0-mae:0.21922\n", "[86]\tvalidation_0-mae:0.21935\n", "[87]\tvalidation_0-mae:0.21884\n", "[88]\tvalidation_0-mae:0.21840\n", "[89]\tvalidation_0-mae:0.21785\n", "[90]\tvalidation_0-mae:0.21727\n", "[91]\tvalidation_0-mae:0.21716\n", "[92]\tvalidation_0-mae:0.21676\n", "[93]\tvalidation_0-mae:0.21659\n", "[94]\tvalidation_0-mae:0.21332\n", "[95]\tvalidation_0-mae:0.21122\n", "[96]\tvalidation_0-mae:0.21001\n", "[97]\tvalidation_0-mae:0.20681\n", "[98]\tvalidation_0-mae:0.20715\n", "[99]\tvalidation_0-mae:0.20683\n", "f1 score: 0.0\n", "acc score: 0.8571428571428571\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXoAAAEmCAYAAABs7FscAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjEsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+j8jraAAAgAElEQVR4nO3de7xVdZ3/8dcbEEvwDhqChBfUxElU0tR0KBtEpxHhp4k5auhkpuYlbcaYxttMv/HRRcu0vDJSGYoihWheQk1xvIEgclMsNSBUEFPUQjl85o/1Pbo8ncve++zD2Xud9/PxWI+z1nd993d99tnw2d/zXWt9lyICMzMrrm6dHYCZmXUsJ3ozs4JzojczKzgnejOzgnOiNzMrOCd6M7OCc6K3mifpo5LukPSGpFvb0c5xku6tZmydRdJBkp7t7DisPsjX0Vu1SPoS8A1gN2ANMBf4TkTMbGe7xwNfBw6IiHXtDrTGSQpgcEQ839mxWDG4R29VIekbwA+B/w9sCwwEfgKMqkLzHwee6wpJvhSSenR2DFZfnOit3SRtDlwCnB4Rt0fE2xHxXkTcERHfTHU2lvRDSX9Kyw8lbZz2DZe0TNK5kl6VtELSuLTvYuAC4BhJb0k6WdJFkn6RO/4gSdGYACV9WdIfJK2R9IKk43LlM3OvO0DSk2lI6ElJB+T2PSjpPyU9ktq5V1KfFt5/Y/z/mov/SEmHS3pO0mpJ43P195X0qKQ/p7pXSuqZ9j2Uqj2d3u8xufb/TdLLwP80lqXX7JSOsXfa3k7SSknD2/XBWmE40Vs17A98BJjaSp1/Bz4NDAX2BPYFvp3b/zFgc6A/cDJwlaQtI+JCsr8SbomI3hFxQ2uBSOoFXAEcFhGbAgeQDSE1rbcVcGequzVwGXCnpK1z1b4EjAO2AXoC57Vy6I+R/Q76k30xXQf8M7APcBDwH5J2SHUbgHOAPmS/u0OA0wAi4uBUZ8/0fm/Jtb8V2V83p+QPHBG/B/4N+IWkTYD/ASZGxIOtxGtdiBO9VcPWwKo2hlaOAy6JiFcjYiVwMXB8bv97af97EXEX8Bawa4XxrAf2kPTRiFgREQuaqfOPwJKI+HlErIuIScBi4J9ydf4nIp6LiL8Ak8m+pFryHtn5iPeAm8mS+I8iYk06/kKyLzgiYnZEPJaO+yJwDfD3JbynCyNibYrnQyLiOuB54HGgH9kXqxngRG/V8RrQp42x4+2Al3LbL6Wy99to8kXxDtC73EAi4m3gGOBUYIWkOyXtVkI8jTH1z22/XEY8r0VEQ1pvTMSv5Pb/pfH1knaRNF3Sy5LeJPuLpdlhoZyVEfHXNupcB+wB/Dgi1rZR17oQJ3qrhkeBtcCRrdT5E9mwQ6OBqawSbwOb5LY/lt8ZEfdExD+Q9WwXkyXAtuJpjGl5hTGV46dkcQ2OiM2A8YDaeE2rl8dJ6k12MvwG4KI0NGUGONFbFUTEG2Tj0lelk5CbSNpI0mGSvpuqTQK+LalvOql5AfCLltpsw1zgYEkD04ngbzXukLStpFFprH4t2RDQ+mbauAvYRdKXJPWQdAywOzC9wpjKsSnwJvBW+mvja032vwLsWGabPwJmRcS/kJ17uLrdUVphONFbVUTED8iuof82sBJYCpwB/CpV+S9gFjAPeAZ4KpVVcqz7gFtSW7P5cHLuluL4E7CabOy7aSIlIl4DvgCcSzb09K/AFyJiVSUxlek8shO9a8j+2rilyf6LgInpqpwvttWYpFHASD54n98A9m682sjMN0yZmRWce/RmZgXnRG9mVnBO9GZmBedEb2ZWcJ4cqZ3STINWIEOGDOnsEKzKFixYsCoi+larvTL/398TESOrdexKONGbNTF58uTODsGqbMiQIU3vgt6Q2rrrucM50ZuZVUBq62bmTC1cwu5Eb2ZWASd6M7OCKzXR1wInejOzMkmiW7fSLlpsaGhou1IHc6I3M6uAe/RmZgVXT4neN0yZmVVAUklLCe1sL+kBSQslLZB0VirfStJ9kpakn1umckm6QtLzkuY1Piu4NU70ZmYVqFaiB9YB50bE7mTPVT5d0u7A+cCMiBgMzEjbAIcBg9NyCtmDbFrlRG9mVqZSk3wpiT491/iptL4GWET2SMtRwMRUbSIfPMFtFPCzyDwGbCGpX2vH8Bi9mVkFOmKMXtIgYC+yh7xvGxEr0q6XgW3Ten+yB/s0WpbKVtACJ3ozswqUkej7SJqV2742Iq5tpr3ewBTg7Ih4M99+RER75tVyojczq0AZiX5VRAxro62NyJL8TRFxeyp+RVK/iFiRhmZeTeXLge1zLx9AGw+19xi9mVkFqnjVjYAbgEURcVlu1zTgxLR+IvDrXPkJ6eqbTwNv5IZ4muUevZlZmcq5M7YEBwLHA89ImpvKxgOXApMlnQy8BDQ+KP4u4HDgeeAdYFxbB3CiNzOrQLVOxkbETKClxg5ppn4Ap5dzDCd6M7MK1NOdsU70ZmYVcKI3MyuwMu56rQlO9GZmFXCiNzMrOCd6M7OCc6I3Mys4J3ozswLzyVgzsy7Aid7MrOCqOAVCh3OiNzOrgHv0ZmYF5jF6M7MuwInezKzgnOjNzArOid7MrOCc6M3MCswnY83MugAnejOzgnOiNzMruGrdGStpAvAF4NWI2COV3QLsmqpsAfw5IoZKGgQsAp5N+x6LiFPbOoYTvZlZmao8Rn8jcCXws8aCiDgmd6wfAG/k6v8+IoaWcwAnejOzClQr0UfEQ6mn3twxBHwR+Fx7jlE/s/KYmdWQxl59WwvQR9Ks3HJKGYc5CHglIpbkynaQNEfS7yQdVEoj7tGbmVWgjB79qogYVuFhjgUm5bZXAAMj4jVJ+wC/kjQkIt5srREnejOzCnT0VTeSegBjgH0ayyJiLbA2rc+W9HtgF2BWa2050ZuZlWkD3TD1eWBxRCzLHbcvsDoiGiTtCAwG/tBWQx6jNzOrQBlj9G21Mwl4FNhV0jJJJ6ddY/nwsA3AwcA8SXOB24BTI2J1W8dwj97MrAJVvOrm2BbKv9xM2RRgSrnHcKI3M6uA74w1MyswSX5mrJlZ0blHb2ZWcE70ZmYF50RvZlZwTvRmZgVWb0+Yqp/TxtahBgwYwP3338+CBQuYP38+Z555JgBHHXUU8+fPp6GhgX322edDrzn//PNZsmQJixcvZsSIEZ0RtpVo7dq1HHPMMYwePZojjjiCK6+8EoDx48czYsQIxowZw5gxY1i0aFEnR1o/qnXD1IbgHr0BsG7dOs4991zmzJlD7969mT17Nvfddx/z589nzJgxXHPNNR+q/4lPfIKxY8cyZMgQtttuO37729+yyy67sH79+k56B9aanj17MmHCBHr16sV7773H8ccfz0EHZRMfnnvuuRx66KGdHGH9qZUkXgr36A2Al19+mTlz5gDw1ltvsWjRIvr378/ixYt57rnn/qb+qFGjuPnmm3n33Xd58cUXef7559l33303dNhWIkn06tULyL7U161bV1eJqhbVU4/eid7+xsc//nH22msvHn/88Rbr9O/fn6VLl76/vWzZMvr3778hwrMKNTQ0MGbMGA466CD2339/PvnJTwJwxRVXMHr0aC699FLefffdTo6yfjjR1zlJW0g6rbPj6Ay9evViypQpnH322axZs6azw7Eq6t69O7fffjv3338/zzzzDEuWLOGcc85h+vTp3HLLLbzxxhtcf/31nR1mXSg1yTvRd6A0j3OL2yXYAuhyib5Hjx5MmTKFm266ialTp7Zad/ny5Wy//fbvbw8YMIDly5d3dIhWBZttthn77rsvM2fOpG/fvkiiZ8+ejB49mvnz53d2eHWjW7duJS21oDaiaIWkEyTNk/S0pJ9LGiTp/lQ2Q9LAVO9GSVdLehz4bjPbO0m6W9JsSQ9L2i29bltJU1P7T0s6ALgU2EnSXEnf67x3v2HdcMMNLFq0iMsvv7zNutOmTWPs2LH07NmTQYMGMXjwYJ544okNEKVVYvXq1bz5ZvYQor/+9a88+uij7LDDDqxcuRKAiGDGjBnsvPPOnRlmXamnHn1NX3UjaQjwbeCAiFglaStgIjAxIiZKOgm4AjgyvWRAqtsg6cYm2zPI5m5eImk/4CdkD9y9AvhdRIyW1B3oDZwP7NHSk9aVPfOxnOc+1rwDDzyQE044gXnz5r1/Unb8+PFsvPHG/PjHP6Zv377ceeedzJ07l5EjR7Jw4UImT57MwoULWbduHaeffrqvuKlhK1euZPz48axfv57169dz6KGHMnz4cMaNG8frr79ORLDbbrtxwQUXdHaodaNWkngpFBGdHUOLJH0d+FhE/HuubBXQLyLek7QRsCIi+qTE/kBETEz13t+W1BtYCTyba37jiPiEpJXAgPSIrsZjDAKmR8QeJcRYu79Aq8iCBQs6OwSrsiFDhsxux3Nb/8Zmm20Ww4aV1twDDzxQ1WNXoqZ79BV4u4XtbsCfW+qhm5mVq5569LU+Rn8/cLSkrQHS0M3/kj1iC+A44OG2GklPSH9B0tGpHUnaM+2eAXwtlXeXtDmwBti0mm/EzIqlnsboazrRR8QC4DvA7yQ9DVwGfB0YJ2kecDxwVonNHQecnNpZAIxK5WcBn5X0DDAb2D0iXgMekTS/K52MNbPS1VOir/mhmzTmPrFJ8eeaqfflNrZfAEY287pX+CDp58u/VH60ZtZVVCuJS5oAfAF4tfG8oKSLgK+QnVsEGB8Rd6V93wJOBhqAMyPinraOUdM9ejOzWlTlG6ZupJlOKHB5RAxNS2OS351s6HpIes1P0tWCrXKiNzOrQLUSfUQ8BKwu8bCjgJsjYm0apXgeaHOSKSd6M7MKlHFnbB9Js3JLqffgnJFuDJ0gactU1h9YmquzLJW1qubH6M3MalEZY/SrKriO/qfAfwKRfv4AOKnMNt7nRG9mVqaOvqImXSTSeKzrgOlpczmwfa7qgFTWKg/dmJlVoCMvr5TUL7c5GmicbW4aMFbSxpJ2AAYDbU4y5R69mVkFqnh55SRgONlY/jLgQmC4pKFkQzcvAl+F7N4iSZOBhcA64PSIaGjrGE70ZmYVqFaij4hjmym+oZX63yG7kbRkTvRmZhWolbteS+FEb2ZWplqa3qAUTvRmZhVwojczKzgnejOzApNUM8+DLYUTvZlZBdyjNzMrOCd6M7OCc6I3Mys4J3ozswLzdfRmZl2AE72ZWcE50ZuZFZwTvZlZwTnRm5kVmE/Gmpl1AZ4Cwcys4NyjNzMrOCd6M7MCq7cx+voZZDIzqyGNyb6tpYR2Jkh6VdL8XNn3JC2WNE/SVElbpPJBkv4iaW5ari4lVid6M7MKVCvRAzcCI5uU3QfsERGfBJ4DvpXb9/uIGJqWU0s5gBO9mVkFqpXoI+IhYHWTsnsjYl3afAwY0J5YWxyjl/RjIFoJ7sz2HNjMrJ6VMUbfR9Ks3Pa1EXFtGYc6Cbglt72DpDnAm8C3I+Lhthpo7WTsrFb2mZl1WWWejF0VEcMqPM6/A+uAm1LRCmBgRLwmaR/gV5KGRMSbrbXTYqKPiIlNDrhJRLxTSbBmZkXT0VfdSPoy8AXgkIgIgIhYC6xN67Ml/R7YhTY65m2O0UvaX9JCYHHa3lPST9r1DszM6ly3bt1KWiohaSTwr8AR+Q62pL6Suqf1HYHBwB/ajLWEY/4QOBR4DSAingYOLj90M7PiqOLllZOAR4FdJS2TdDJwJbApcF+TyygPBuZJmgvcBpwaEaubbTinpBumImJpk4AbSnmdmVkRVfOGqYg4tpniG1qoOwWYUu4xSkn0SyUdAISkjYCzgEXlHsjMrEiKdmfsqcDpQH/gT8DQtG1m1mVV8YapDtdmjz4iVgHHbYBYzMzqRq0k8VKUctXNjpLukLQyzcfw63S218ysy6qnHn0pQze/BCYD/YDtgFuBSR0ZlJlZLSs1yddTot8kIn4eEevS8gvgIx0dmJlZLaunRN/aXDdbpdXfSDofuJls7ptjgLs2QGxmZjWrVpJ4KVo7GTubLLE3vpuv5vYFH54208ysSynEM2MjYocNGYiZWb2opWGZUpR0Z6ykPYDdyY3NR8TPOiooM7NaV6hEL+lCYDhZor8LOAyYCTjRm1mXVU+JvpRBpqOAQ4CXI2IcsCeweYdGZWZW4wpx1U3OXyJivaR1kjYDXgW27+C4zMxqWq0k8VKUkuhnKXsC+XVkV+K8RTalpplZl1RLvfVSlDLXzWlp9WpJdwObRcS8jg3LzKy2FSLRS9q7tX0R8VTHhGRmVvsKkeiBH7SyL4DPVTmWurTPPvswa5afo27W1RQi0UfEZzdkIGZm9aSeEn393MNrZlYjJFXt4eCSJqQp4OfnyraSdJ+kJennlqlckq6Q9Lykea0Nsec50ZuZVaCK19HfCIxsUnY+MCMiBgMz0jZkN6wOTsspwE9LOYATvZlZBaqV6CPiIWB1k+JRwMS0PhE4Mlf+s8g8BmwhqV9bxyjlCVOS9M+SLkjbAyXt22b0ZmYF1sF3xm4bESvS+svAtmm9P7A0V29ZKmtVKT36nwD7A8em7TXAVSWFamZWQGU+YaqPpFm55ZRyjhURQXalY8VKuTN2v4jYW9KcdNDXJfVsz0HNzOpdGb31VRExrMzmX5HULyJWpKGZV1P5cj48Bc2AVNaqUnr070nqTvpGkdQXWF9ezGZmxdLBQzfTgBPT+onAr3PlJ6Qh9U8Db+SGeFpUSo/+CmAqsI2k75DNZvntssM2MyuQal1HL2kS2VTwfSQtAy4ELgUmSzoZeAn4Yqp+F3A48DzwDjCulGOUMtfNTZJmk01VLODIiFhU3lsxMyuWaiX6iDi2hV2HNFM3gNPLPUYpDx4ZSPbNcUe+LCL+WO7BzMyKoHCzVwJ38sFDwj8C7AA8CwzpwLjMzGpaIR4O3igi/i6/nW65Pa2F6mZmXULRevQfEhFPSdqvI4IxM6sXhUr0kr6R2+wG7A38qcMiMjOrcUUco980t76ObMx+SseEY2ZWHwqT6NONUptGxHkbKB4zs7pQiEQvqUdErJN04IYMyMysHhQi0QNPkI3Hz5U0DbgVeLtxZ0Tc3sGxmZnVrKIk+kYfAV4je0Zs4/X0ATjRm1mXVKSTsdukK27m80GCb9SuKTPNzOpdURJ9d6A3H07wjZzozaxLK8qdsSsi4pINFomZWR0pSo++ft6FmdkGVKQx+r+ZItPMzDKFSPQR0fSp5GZmlhQi0ZuZWcuc6M3MCs6J3syswIp0MtbMzFpQxYeD7wrckivaEbgA2AL4CrAylY+PiLsqOYYTvZlZBar4cPBngaGpze7AcmAqMA64PCK+395jONGbmVWgg4ZuDgF+HxEvVbP9+rmH18ysRkiiW7duJS1AH0mzcssprTQ9FpiU2z5D0jxJEyRtWWm8TvRmZhVoPCHb1gKsiohhueXaFtrrCRxBNiU8wE+BnciGdVYAP6g0Vg/dmJlVoAOGbg4DnoqIVwAaf6ZjXQdMr7Rh9+jNzCpQRo++VMeSG7aR1C+3bzTZlPEVcY/ezKxM1b6OXlIv4B+Ar+aKvytpKNm08C822VcWJ3ozswpUM9FHxNvA1k3Kjq9W+070ZmYV8J2xZmYF50RvZlZwTvRmZgXmSc3MzLqAojwc3MzMWuAevZlZwTnRm5kVmMfozcy6ACd6M7OCc6I3Mys4J3ozs4Krp0RfPxeCWqe5++672XXXXdl555259NJLOzscqwJ/pu1T6hTFtfJl4ERvrWpoaOD000/nN7/5DQsXLmTSpEksXLiws8OydvBnWh1O9FYYTzzxBDvvvDM77rgjPXv2ZOzYsfz617/u7LCsHfyZVkcZz4ztdLURhdWs5cuXs/3227+/PWDAAJYvX96JEVl7+TOtDvfoO5CkoyUtkvSApGGSrkjlwyUdkKt3pKTdc9uXSPp8Z8RsZsVSb2P0dXPVjbLfmICTga9ExMy0a1b6ORx4C/jftH0k2cN0FwJExAUbLNgC6d+/P0uXLn1/e9myZfTv378TI7L28mdaHbWSxEvRoT16Sd+QND8tZ0u6VNLpuf0XSTovrX9T0pOS5km6OJUNkvSspJ+RPRj3P4DPADdI+l7qxU+XNAg4FThH0lxJfw8cAXwvbe8k6UZJR6V2X5R0saSnJD0jabdU3lfSfZIWSLpe0kuS+nTk76jWfepTn2LJkiW88MILvPvuu9x8880cccQRnR2WtYM/0+pwjx6QtA8wDtiPrCf+OPDPwA+Bq1K1LwKHShoBDAb2TXWnSToY+GMqPzEiHkvtfhY4LyJmSRoOEBEvSroaeCsivp/qTQOmR8RtabtpiKsiYm9JpwHnAf8CXAjcHxH/LWkk2V8Pzb23U4BTAAYOHFj5L6kO9OjRgyuvvJJDDz2UhoYGTjrpJIYMGdLZYVk7+DOtjmomcUkvAmuABmBdRAyTtBVwCzCI7OHgX4yI1ytpvyOHbj4DTE0PvUXS7cBBwDaStgP6Aq9HxFJJZwEjgDnptb3JEvwfgZcak3yV3Z5+zgbG5GIeDRARd0tq9pcaEdcC1wIMGzYsOiC2mnL44Ydz+OGHd3YYVkX+TNuvA3rrn42IVbnt84EZEXGppPPT9r9V0nBnjNHfChwFfIzs2wqyXvx/R8Q1+YppSObtDopjbfrZQB2dqzCzzreBhmVGkZ17BJgIPEiFib4jx+gfBo6UtImkXmQ95YfJkvtYsmR/a6p7D3CSpN4AkvpL2qbM460BNm1luxSPkA0nkYaTtizz9WbWRZQxRt9H0qzcckozzQVwr6TZuf3bRsSKtP4ysG2lsXZYTzYinpJ0I/BEKro+IuYASNoUWN74JiLiXkmfAB5Nv5i3yMbzG8o45B3AbZJGAV8Hbgauk3Qm2ZdKKS4GJkk6HniU7Je7powYzKyLKKNHvyoihrVR5zMRsTx1cO+TtDi/MyJCUsXDxB06ZBERlwGXNVP+d82U/Qj4UTPN7NGk3vDc+oNkf84QEc8Bn2zy2t1z61/OvW5Qbn0WH/x59AZwaESsk7Q/8KmIWIuZWRPVHLqJiOXp56uSppJdmPKKpH4RsUJSP+DVStuvuxumOthA4ElJTwNXAF/p5HjMrAZJqtoUCJJ6pVEO0jD3CLLLyacBJ6ZqJwIVz1Phk5A5EbEE2Kuz4zCz2lfFHv22wNTUXg/gl+mqvyeByZJOBl4inT+shBO9mVkFqpXoI+IPwJ7NlL8GHFKNYzjRm5lVoFbuei2FE72ZWQWc6M3MCqyW5rEphRO9mVkFnOjNzArOid7MrOCc6M3MCs6J3syswBrvjK0XTvRmZhVwj97MrOCc6M3MCs6J3syswHzDlJlZF+BEb2ZWcE70ZmYF50RvZlZwTvRmZgXmk7FmZl2A74w1Myu4eurR189XkplZDWkcvmlrKaGd7SU9IGmhpAWSzkrlF0laLmluWg6vNFb36M3MylTlMfp1wLkR8ZSkTYHZku5L+y6PiO+39wBO9GZmFahWoo+IFcCKtL5G0iKgf1UaTzx0Y2ZWgTKGbvpImpVbTmmlzUHAXsDjqegMSfMkTZC0ZaWxOtGbmVWgjES/KiKG5ZZrW2ivNzAFODsi3gR+CuwEDCXr8f+g0lg9dGNmVoFqXnUjaSOyJH9TRNwOEBGv5PZfB0yvtH336M3MylRqb77Eq24E3AAsiojLcuX9ctVGA/Mrjdc9ejOzClSxR38gcDzwjKS5qWw8cKykoUAALwJfrfQATvRmZhWo4lU3M4HmGrurKgfAid7MrCKeAsHMrMA8qZmZWRfgRG9mVnBO9GZmBedEb2ZWcE70ZmYF5pOxZmZdgBO9mVnBOdGbmRWcE72ZWYFJ8p2xZmZF5x69mVnBOdGbmRWcE72ZWYH5Onozsy7Aid7MrOCc6M3MCs6J3sys4Oop0dfPFf9mZjWi8WRsKUuJ7Y2U9Kyk5yWdX+143aM3M6tAte6MldQduAr4B2AZ8KSkaRGxsCoHwD16M7OKVLFHvy/wfET8ISLeBW4GRlUzVvfo22n27NmrJL3U2XFsIH2AVZ0dhFVNV/o8P17NxmbPnn2PpD4lVv+IpFm57Wsj4trcdn9gaW57GbBfe2PMc6Jvp4jo29kxbCiSZkXEsM6Ow6rDn2flImJkZ8dQDg/dmJl1ruXA9rntAamsapzozcw615PAYEk7SOoJjAWmVfMAHrqxclzbdhWrI/48a0BErJN0BnAP0B2YEBELqnkMRUQ12zMzsxrjoRszs4JzojczKzgneutQkraQdFpnx2EZSUdLWiTpAUnDJF2RyodLOiBX70hJu+e2L5H0+c6I2drPY/TWKkk9ImJdS9slvH4QMD0i9uiA8KxEym7RFHAX8F8RMbPJ/ouAtyLi+2n7RrLP7bYNHKp1ACf6LkTSCcB5QADzgP8AJpDdIbkSGBcRf0z/yf8K7AU8AmzVZPuqtPQF3gG+EhGLJW0LXA3smA75NeBMstu5nwXui4hvdvw7LQZJ3wBOSpvXAx8DlkbEVWn/RaTkLOmbwBeBjYGpEXFh+pK9B3gc2AeYDHyT7BrtacCdZP8ezgAeAxrI/h2cBUwF3kjL/yP7tzI9Im6T9CIwEfgnYCPg6PT59wV+CWwHPEo2d8s+EdFV7r6tXRHhpQsswBDgOaBP2t4KuAM4MW2fBPwqrd8ITAe6t7A9Axic1vcD7k/rtwBnp/XuwObAIGB+Z7//elvIEvMzQC+gN7CA7Iv2d7k6C8lutBlBdqmkyIZjpwMHp9/9euDTudc8CAxL68PJkjfARcB5uXo3Akc1tw28CHw9rZ8GXJ/WrwS+ldZHknUo+nT279JL+Dr6LuRzwK2RelcRsVrS/sCYtP/nwHdz9W+NiIam25J6AwcAt+YmbNo4d4wTUvsNwBuStuyQd1N8nyHrmb8NIOl24CBgG0nbkf019XpELJV0Flmyn5Ne2xsYDPwReCkiHuuA+G5PP2fzwb+hzwCjASLibkmvd8BxrQJO9NaSt1vY7gb8OSKGbuB4LHMrcBTZMM4tqUzAf0fENfmKaeim6edYLWvTzwacR2qer7rpOu4Hjpa0NYCkrYD/JbvdGuA44OG2GomIN4EXJB2d2pGkPdPuGWTj8kjqLmlzYA2waTXfSBfxMHCkpE0k9SLrKT9MltzHkiX7W1Pde0Ng5McAAAPBSURBVICT0l9bSOovaZsyj9f0c6rkc3uE7DwBkkYA/muuRjjRdxGR3VL9HeB3kp4GLgO+DoyTNA84nuwkXCmOA05O7Szgg7mzzwI+K+kZsj/pd4+I14BHJM2X9L3qvaNii4inyMbFnyA7mXp9RMxJn+OmwPKIWJHq3kt2EvTR9Lu/jfKT9B3AaElzJR1ENif6NyXNkbRTiW1cDIyQNB84GniZ7AvDOpmvujGzqpC0MdAQ2dwt+wM/9RBfbfDYmplVy0BgsqRuwLvAVzo5HkvcozczKziP0ZuZFZwTvZlZwTnRm5kVnBO9dTpJDemyvvmSbpW0STvaulHSUWn9+vwMjM3U/dCMjWUc40VJfUotb1LnrTKPdZGk88qN0SzPid5qwV8iYmhkM1y+C5ya3ympoqvDIuJfImJhK1WGk03nYFZoTvRWax4Gdk697YclTQMWpjttvyfpSUnzJH0V3r8z90pJz0r6LfD+HaGSHpQ0LK2PlPSUpKclzUjTA5wKnNN4k5CkvpKmpGM8KenA9NqtJd0raYGk68mmHGiVpF9Jmp1ec0qTfZen8hlpxkck7STp7vSahyXtVo1fphn4OnqrIannfhhwdyraG9gjIl5IyfKNiPhUujHnEUn3ks3ouCuwO7At2YyOE5q02xe4Djg4tbVVmtTtaj48B/svgcsjYqakgWRTC3wCuBCYGRGXSPpH4OQS3s5J6RgfBZ6UNCXdJdwLmBUR50i6ILV9Btnsk6dGxBJJ+wE/IZskzqzdnOitFnxU0ty0/jBwA9mQyhMR8UIqHwF8snH8nWwK5MFk0/FOSrNl/knS/c20/2ngoca2ImJ1C3F8Htg9NyvnZmn+mINJMzRGxJ0lzsp4pqTRaX37FOtrZNMGN05G9gvg9jZmBDVrNyd6qwV/aXqrfEp4+ZkXRTYH+j1N6h1exTi6kc3d/tdmYimZpOFkXxr7R8Q7kh4EPtJC9cAzgloH8xi91Yt7gK9J2ghA0i5pVseHgGPSGH4/4LPNvPYx4GBJO6TXbpXKm87QeC/ZRG+keo2J9yHgS6nsMNqelXFzsrni30lj7Z/O7etGNvMkqc2ZbcwIatZuTvRWL64nG39/Ks2OeA3ZX6RTgSVp38/IHmH3IRGxEjiFbJjkaT4YOmk6Y+OZwLB0snchH1z9czHZF8UCsiGcP7YR691AD0mLgEvJvmgavQ3sm97D54BLUnlLM4KatZvnujEzKzj36M3MCs6J3sys4JzozcwKzonezKzgnOjNzArOid7MrOCc6M3MCu7/AI+nNLlzfCboAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Wicket\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'wicket') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'wicket')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "print(headers)\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10130, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1831, 204)\n", "====================\n", "Total test:  (172, 204)\n", "overfitting in test (0, 204)\n", "correct in test (172, 204)\n", "Index(['wrapsLoop', 'wrapsTryCatch', 'wrapsIfElse', 'wrongMethodRef',\n", "       'constChange', 'unwrapIfElse', 'unwrapTryCatch', 'expArithMod',\n", "       'codeMove', 'expLogicExpand',\n", "       ...\n", "       'typeOfFaultyStmt_ NewClass', 'typeOfFaultyStmt_ OperatorAssignment',\n", "       'typeOfFaultyStmt_ Parameter', 'typeOfFaultyStmt_ Return',\n", "       'typeOfFaultyStmt_ Switch', 'typeOfFaultyStmt_ Throw',\n", "       'typeOfFaultyStmt_ Try', 'typeOfFaultyStmt_ TypeReference',\n", "       'typeOfFaultyStmt_ UnaryOperator', 'typeOfFaultyStmt_ While'],\n", "      dtype='object', length=447)\n", "[0]\tvalidation_0-mae:0.50829\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.52058\n", "[2]\tvalidation_0-mae:0.52482\n", "[3]\tvalidation_0-mae:0.52039\n", "[4]\tvalidation_0-mae:0.52077\n", "[5]\tvalidation_0-mae:0.49947\n", "[6]\tvalidation_0-mae:0.50486\n", "[7]\tvalidation_0-mae:0.49945\n", "[8]\tvalidation_0-mae:0.49933\n", "[9]\tvalidation_0-mae:0.49183\n", "[10]\tvalidation_0-mae:0.48807\n", "[11]\tvalidation_0-mae:0.48369\n", "[12]\tvalidation_0-mae:0.48170\n", "[13]\tvalidation_0-mae:0.48590\n", "[14]\tvalidation_0-mae:0.48011\n", "[15]\tvalidation_0-mae:0.47216\n", "[16]\tvalidation_0-mae:0.46798\n", "[17]\tvalidation_0-mae:0.46964\n", "[18]\tvalidation_0-mae:0.46695\n", "[19]\tvalidation_0-mae:0.46564\n", "[20]\tvalidation_0-mae:0.46211\n", "[21]\tvalidation_0-mae:0.46193\n", "[22]\tvalidation_0-mae:0.46194\n", "[23]\tvalidation_0-mae:0.46012\n", "[24]\tvalidation_0-mae:0.45743\n", "[25]\tvalidation_0-mae:0.45715\n", "[26]\tvalidation_0-mae:0.45361\n", "[27]\tvalidation_0-mae:0.45417\n", "[28]\tvalidation_0-mae:0.45310\n", "[29]\tvalidation_0-mae:0.45275\n", "[30]\tvalidation_0-mae:0.44747\n", "[31]\tvalidation_0-mae:0.44933\n", "[32]\tvalidation_0-mae:0.45182\n", "[33]\tvalidation_0-mae:0.45336\n", "[34]\tvalidation_0-mae:0.45488\n", "[35]\tvalidation_0-mae:0.45383\n", "[36]\tvalidation_0-mae:0.45254\n", "[37]\tvalidation_0-mae:0.45156\n", "[38]\tvalidation_0-mae:0.45102\n", "[39]\tvalidation_0-mae:0.44547\n", "[40]\tvalidation_0-mae:0.44359\n", "[41]\tvalidation_0-mae:0.43984\n", "[42]\tvalidation_0-mae:0.44050\n", "[43]\tvalidation_0-mae:0.44047\n", "[44]\tvalidation_0-mae:0.43340\n", "[45]\tvalidation_0-mae:0.42943\n", "[46]\tvalidation_0-mae:0.42809\n", "[47]\tvalidation_0-mae:0.42983\n", "[48]\tvalidation_0-mae:0.42498\n", "[49]\tvalidation_0-mae:0.42260\n", "[50]\tvalidation_0-mae:0.41911\n", "[51]\tvalidation_0-mae:0.42004\n", "[52]\tvalidation_0-mae:0.41941\n", "[53]\tvalidation_0-mae:0.41906\n", "[54]\tvalidation_0-mae:0.41936\n", "[55]\tvalidation_0-mae:0.41674\n", "[56]\tvalidation_0-mae:0.41575\n", "[57]\tvalidation_0-mae:0.41627\n", "[58]\tvalidation_0-mae:0.41681\n", "[59]\tvalidation_0-mae:0.41549\n", "[60]\tvalidation_0-mae:0.41287\n", "[61]\tvalidation_0-mae:0.40980\n", "[62]\tvalidation_0-mae:0.40986\n", "[63]\tvalidation_0-mae:0.40916\n", "[64]\tvalidation_0-mae:0.41047\n", "[65]\tvalidation_0-mae:0.40743\n", "[66]\tvalidation_0-mae:0.40518\n", "[67]\tvalidation_0-mae:0.40434\n", "[68]\tvalidation_0-mae:0.40325\n", "[69]\tvalidation_0-mae:0.40434\n", "[70]\tvalidation_0-mae:0.40387\n", "[71]\tvalidation_0-mae:0.40312\n", "[72]\tvalidation_0-mae:0.40300\n", "[73]\tvalidation_0-mae:0.40216\n", "[74]\tvalidation_0-mae:0.40016\n", "[75]\tvalidation_0-mae:0.39645\n", "[76]\tvalidation_0-mae:0.39652\n", "[77]\tvalidation_0-mae:0.39531\n", "[78]\tvalidation_0-mae:0.39481\n", "[79]\tvalidation_0-mae:0.39572\n", "[80]\tvalidation_0-mae:0.39692\n", "[81]\tvalidation_0-mae:0.39299\n", "[82]\tvalidation_0-mae:0.39336\n", "[83]\tvalidation_0-mae:0.39163\n", "[84]\tvalidation_0-mae:0.39126\n", "[85]\tvalidation_0-mae:0.38950\n", "[86]\tvalidation_0-mae:0.38777\n", "[87]\tvalidation_0-mae:0.38698\n", "[88]\tvalidation_0-mae:0.38695\n", "[89]\tvalidation_0-mae:0.38703\n", "[90]\tvalidation_0-mae:0.38513\n", "[91]\tvalidation_0-mae:0.38524\n", "[92]\tvalidation_0-mae:0.38563\n", "[93]\tvalidation_0-mae:0.38400\n", "[94]\tvalidation_0-mae:0.38612\n", "[95]\tvalidation_0-mae:0.38404\n", "[96]\tvalidation_0-mae:0.38462\n", "[97]\tvalidation_0-mae:0.38483\n", "[98]\tvalidation_0-mae:0.38433\n", "[99]\tvalidation_0-mae:0.38343\n", "f1 score: 0.0\n", "acc score: 0.6686046511627907\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXoAAAEmCAYAAABs7FscAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjEsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+j8jraAAAgAElEQVR4nO3debxVdb3/8df7iEiKaHpwQglUNKdrKTmlXsquonlT+zldrRwox8wyLetWqL/bL291c0hNTQu0nDBnDe2HUmhOgKgM4oyAOCBqoCmCn98f63twc35n2HuxN3vvdd7Px2M/zl7D/q7PPhvee53vWuu7FBGYmVlxtdS7ADMzqy0HvZlZwTnozcwKzkFvZlZwDnozs4Jz0JuZFZyD3hqepI9Jul3S25LGrEA7R0q6p5q11YukPSTNrHcd1hzk8+itWiQdAZwGfBJYCEwBfhoR969gu18FTgF2i4glK1xog5MUwJCIeLbetVgxeI/eqkLSacD5wP8B1gcGApcAB1Sh+U8AT/eEkC+HpF71rsGai4PeVpiktYBzgJMj4qaIeCciPoiI2yPijLTOapLOl/RyepwvabW0bJikOZK+K+k1SfMkHZOWnQ38BDhM0iJJIySdJekPJdsfJCnaAlDS0ZKel7RQ0guSjiyZf3/J63aT9GjqEnpU0m4ly8ZL+t+SHkjt3COptZP331b/90rqP1DSfpKelrRA0g9L1t9J0oOS3krrXiSpd1r2t7Ta4+n9HlbS/vclvQL8vm1ees1maRs7pOmNJL0uadgKfbBWGA56q4ZdgT7AzV2s85/ALsCngO2BnYAflSzfAFgLGACMAC6W9PGIGEn2V8L1EdE3Iq7sqhBJawAXAvtGxJrAbmRdSO3XWwe4M627LvAr4E5J65asdgRwDLAe0Bs4vYtNb0D2OxhA9sX0W+ArwI7AHsCPJQ1O6y4FvgO0kv3u9gJOAoiIPdM626f3e31J++uQ/XVzXOmGI+I54PvAHyStDvweGB0R47uo13oQB71Vw7rA/G66Vo4EzomI1yLideBs4Kslyz9Iyz+IiLuARcCWOev5ENhW0sciYl5ETOtgnS8Cz0TE1RGxJCKuBZ4C/r1knd9HxNMR8U/gBrIvqc58QHY84gPgOrIQvyAiFqbtTyf7giMiJkXEQ2m7LwKXAf9axnsaGRHvp3qWExG/BZ4FHgY2JPtiNQMc9FYdbwCt3fQdbwTMKpmeleYta6PdF8W7QN9KC4mId4DDgBOAeZLulPTJMuppq2lAyfQrFdTzRkQsTc/bgvjVkuX/bHu9pC0k3SHpFUn/IPuLpcNuoRKvR8R73azzW2Bb4NcR8X4361oP4qC3angQeB84sIt1XibrdmgzMM3L4x1g9ZLpDUoXRsTdEfFvZHu2T5EFYHf1tNU0N2dNlfgNWV1DIqIf8ENA3bymy9PjJPUlOxh+JXBW6poyAxz0VgUR8TZZv/TF6SDk6pJWlbSvpJ+n1a4FfiSpfzqo+RPgD5212Y0pwJ6SBqYDwT9oWyBpfUkHpL7698m6gD7soI27gC0kHSGpl6TDgK2BO3LWVIk1gX8Ai9JfGye2W/4qsGmFbV4ATIyIr5Mde7h0hau0wnDQW1VExP+QnUP/I+B1YDbwTeCWtMp/AROBJ4AngclpXp5t/QW4PrU1ieXDuSXV8TKwgKzvu32QEhFvAPsD3yXrevoesH9EzM9TU4VOJzvQu5Dsr43r2y0/Cxidzso5tLvGJB0ADOej93kasEPb2UZmvmDKzKzgvEdvZlZwDnozs4Jz0JuZFZyD3sys4Dw40gpKIw1agWy00Ubdr2RN5eWXX54fEf2r1V6F/+/vjojh1dp2Hg56s3aOP/74epdgVTZy5Mj2V0GvTN1d9VxzDnozsxyk7i5mzjTCKewOejOzHBz0ZmYFV27QNwIHvZlZhSTR0lLeSYtLly7tfqUac9CbmeXgPXozs4Jz0JuZFZyD3sys4Bz0ZmYFJslBb2ZWdA56M7OCc9CbmRWcg97MrOAc9GZmBVbJlbGNwEFvZpaD9+jNzArOQW9mVnAOejOzAvMFU2ZmPYCD3sys4Bz0ZmYF56A3Mys4B72ZWYH5YKyZWQ/goDczKzgPgWBmVnDNtEffPF9JZmYNoq2PvpxHGW39TtJrkqaWzFtH0l8kPZN+fjzNl6QLJT0r6QlJO5RTr4PezCyHagU9MAoY3m7emcC4iBgCjEvTAPsCQ9LjOOA35WzAQW9mlkO1gj4i/gYsaDf7AGB0ej4aOLBk/lWReQhYW9KG3W3DffRmZjlU0EffKmliyfTlEXF5N69ZPyLmpeevAOun5wOA2SXrzUnz5tEFB72ZWQ4VBP38iBiadzsREZIi7+vBXTdmZhWr5sHYTrza1iWTfr6W5s8FNilZb+M0r0sOejOzHGoc9LcBR6XnRwG3lsz/Wjr7Zhfg7ZIunk6568bMLIdqnUcv6VpgGFlf/hxgJHAucIOkEcAs4NC0+l3AfsCzwLvAMeVsw0FvZpZDta6MjYj/6GTRXh2sG8DJlW7DQW9mViEPamZm1gM46M3MCs5Bb2ZWcA56M7OCc9CbmRWYD8aamfUADnozs4Jz0JuZFZyD3syswCT5nrFmZkXnPXozs4Jz0JuZFZyD3sys4Bz0ZmYF5gumrCldeeWV7L///rz22mtst912ABx88MGcddZZbLXVVuy0005MmjQJgE984hPMmDGDmTNnAvDQQw9x4okn1q12K895551H7969aWlpoaWlheOPP54bbriBN954A4D33nuPPn36+LMsk4Pems6oUaO46KKLuOqqq5bNmzp1Kl/+8pe57LLL/r/1n3vuOT796U+vzBKtCo4++mjWWGONZdOHHnrosudjx46lT58+9SirKTVT0DfPiaBWUxMmTGDBggXLzXvqqad4+umn61SRrUwRwbRp05b9NWfdq/E9Y6vKQW+5DB48mMmTJzN+/Hh23333epdjZbr66qu59NJLmThx4nLzZ82aRd++fVl33XXrVFnzaaagd9dNByStDRwREZfUu5ZGNG/ePAYOHMiCBQvYYYcduOWWW9hmm21YuHBhvUuzLowYMYJ+/fqxaNEirrrqKlpbWxk0aBAATz75JNtuu219C2wijRTi5SjkHr2kXl1Nl2Ft4KTqVVQsixcvXtbNM3nyZJ577jm22GKLOldl3enXrx8Affv2ZauttmLu3LkALF26lBkzZjjoK9R2ULu7RyNojCq6IOlrkp6Q9LikqyUNknRvmjdO0sC03ihJl0p6GPh5B9ObSRoraZKkCZI+mV63vqSbU/uPS9oNOBfYTNIUSb+o37tvTK2trcv+AQ8ePJghQ4bw/PPP17kq68rixYt5//33lz1/7rnnWG+99QB4/vnnaW1tZa211qpniU3HXTdVImkb4EfAbhExX9I6wGhgdESMlnQscCFwYHrJxmndpZJGtZseB5wQEc9I2hm4BPh8ev1fI+IgSasAfYEzgW0j4lOd1HUccFyt3nc9XHPNNQwbNozW1lZmz57NyJEjWbBgAb/+9a/p378/d955J1OmTGH48OHsueeenHPOOXzwwQd8+OGHnHDCCbz55pv1fgvWhUWLFnHdddcB8OGHH7LddtsxZMgQIDu7ygdhK9coIV4ORUS9a+iUpFOADSLiP0vmzQc2jIgPJK0KzIuI1hTs90XE6LTesmlJfYHXgZklza8WEVtJeh3YOCLeL9nGIOCOiOj2b1lJjfsLtFzOPvvsepdgVTZy5MhJETG0Wu3169cvhg4tr7n77ruvqtvOo6H36HN4p5PpFuCtzvbQzcwq1Ux79I3eR38vcIikdQFS183fgcPT8iOBCd01EhH/AF6QdEhqR5K2T4vHASem+atIWgtYCKxZzTdiZsXSTH30DR30ETEN+CnwV0mPA78CTgGOkfQE8FXg1DKbOxIYkdqZBhyQ5p8KfE7Sk8AkYOuIeAN4QNJUH4w1s440U9A3fNdN6nMf3W725ztY7+hupl8Ahnfwulf5KPRL5x9RebVm1lM0SoiXo+GD3sys0TTS3no5HPRmZjk46M3MCq5RrnotR/NUambWQKp5MFbSdyRNSyeAXCupj6TBkh6W9Kyk6yX1zlurg97MrELlhnw5QS9pAPAtYGi6SHMVslPI/xs4LyI2B94ERuSt10FvZpZDlU+v7AV8TNkAjKsD88jOLrwxLR/NR0O9VMxBb2aWQwVB3yppYsljuXGyImIu8EvgJbKAf5vsmp63ImJJWm0OMCBvrT4Ya2aWQwV76/O7GutG0sfJruUZDLwFjKGDa35WhIPezCyHKp5e+QXghYh4PbV7E/BZYG1JvdJe/cbA3LwbcNeNmVmFqnkwlqzLZhdJqyt7wV7AdOA+4OC0zlHArXnrddCbmeVQraCPiIfJDrpOBp4ky+XLge8Dp0l6FlgXuDJvre66MTPLoZpXxkbESGBku9nPAztVo30HvZlZhSQ11ZWxDnozsxw81o2ZWcE56M3MCs5Bb2ZWcA56M7MC841HzMx6AAe9mVnBOejNzArOQW9mVnAOejOzAvPBWDOzHsBDIJiZFZz36M3MCs5Bb2ZWYO6jNzPrARz0ZmYF56A3Myu4QgS9pF8D0dnyiPhWTSoyM2sChQh6YOJKq8LMrIkU5mBsRIwunZa0ekS8W/uSzMwaXzMFfbeXdknaVdJ04Kk0vb2kS2pemZlZA2tpaSnr0QjKqeJ8YB/gDYCIeBzYs5ZFmZk1urbum+4ejaCss24iYna7gpfWphwzs8bXSCFejnKCfrak3YCQtCpwKjCjtmWZmTW2Zgr6crpuTgBOBgYALwOfStNmZj1WobpuImI+cORKqMXMrGk0SoiXo5yzbjaVdLuk1yW9JulWSZuujOLMzBpVM+3Rl9N1cw1wA7AhsBEwBri2lkWZmTWyckO+mYJ+9Yi4OiKWpMcfgD61LszMrJE1U9B3NdbNOunpnyWdCVxHNvbNYcBdK6E2M7OG1SghXo6uDsZOIgv2tndzfMmyAH5Qq6LMzBpdo1z1Wo6uxroZvDILMTNrFtXulpG0NnAFsC3ZjvSxwEzgemAQ8CJwaES8maf9sq6MlbQtsDUlffMRcVWeDZqZFUGVu24uAMZGxMGSegOrAz8ExkXEuan7/Ezg+3ka7zboJY0EhpEF/V3AvsD9gIPezHqsagW9pLXIxg87GiAiFgOLJR1Alr0Ao4Hx5Az6cjqZDgb2Al6JiGOA7YG18mzMzKwoKjjrplXSxJLHce2aGgy8Dvxe0mOSrpC0BrB+RMxL67wCrJ+31nK6bv4ZER9KWiKpH/AasEneDZqZFUEFe/TzI2JoF8t7ATsAp0TEw5IuIOumWSYiQlKnd/zrTjl79BPTgYLfkp2JMxl4MO8GzcyaXZUvmJoDzImIh9P0jWTB/6qkDdP2NiTbyc6lnLFuTkpPL5U0FugXEU/k3aCZWRFUq48+Il6RNFvSlhExk6yrfHp6HAWcm37emncbXV0wtUNXyyJict6Nmpk1uyqfdXMK8Md0xs3zwDFkPS43SBoBzAIOzdt4V3v0/9PFsgA+n3ejRbLjjjsycaLvo27WyEaOHFn1NqsZ9BExBeioH3+varTf1QVTn6vGBszMiqgoQyCYmVkHJBVjCAQzM+uc9+jNzAqumYK+nDtMSdJXJP0kTQ+UtFPtSzMza1zNNB59OZ1MlwC7Av+RphcCF9esIjOzBtdsd5gqp+tm54jYQdJjABHxZjrX08ysx2qUEC9HOUH/gaRVyM6dR1J/4MOaVmVm1uCKFvQXAjcD60n6Kdlolj+qaVVmZg2uUEEfEX+UNInsCi0BB0bEjJpXZmbWwAoV9JIGAu8Ct5fOi4iXalmYmVmjaqQDreUop+vmTj66SXgfskHyZwLb1LAuM7OGVqgrYyNiu9LpNKrlSZ2sbmbWIxRtj345ETFZ0s61KMbMrFkUKuglnVYy2UJ255OXa1aRmVmDK2If/Zolz5eQ9dn/qTblmJk1h8IEfbpQas2IOH0l1WNm1hQKEfSSekXEEkmfXZkFmZk1g0IEPfAIWX/8FEm3AWOAd9oWRsRNNa7NzKxhFSXo2/QB3iC7R2zb+fQBOOjNrEcq0sHY9dIZN1P5KODbRE2rMjNrcEUJ+lWAviwf8G0c9GbWoxXlyth5EXHOSqvEzKyJFGWPvnnehZnZSlSkPvq9VloVZmZNphBBHxELVmYhZmbNpBBBb2ZmnXPQm5kVnIPezKzAinQw1szMOuGgNzMruGYK+ua5tMvMrIG0dd9096igvVUkPSbpjjQ9WNLDkp6VdL2k3nlrddCbmVVIEi0tLWU9KnAqMKNk+r+B8yJic+BNYETeeh30ZmY5VHOPXtLGwBeBK9K0yEYMvjGtMho4MG+t7qM3M8uhyn305wPf46Nbt64LvBURS9L0HGBA3sa9R29mlkMFe/StkiaWPI5r187+wGsRMalWtXqP3sysQhUeaJ0fEUO7WP5Z4EuS9iO70VM/4AJg7bZbugIbA3Pz1us9ejOzHKrVRx8RP4iIjSNiEHA4cG9EHAncBxycVjsKuDVvrQ56M7Mcqn16ZQe+D5wm6VmyPvsr8zbkrhszsxxqccFURIwHxqfnzwM7VaNdB72ZWQ7NdGWsg97MrEIe1MzMrAcoys3BzcysE96jNzMrOAe9mVmBuY/ezKwHcNCbmRWcg97MrOAc9GZmBddMQd88J4Ja3YwdO5Ytt9ySzTffnHPPPbfe5VgV+DNdMeWOc9MoXwYOeuvS0qVLOfnkk/nzn//M9OnTufbaa5k+fXq9y7IV4M+0Ohz0VhiPPPIIm2++OZtuuim9e/fm8MMP59Zbc4+Wag3An2l11OCesTXTGFVYw5o7dy6bbLLJsumNN96YuXNz3//AGoA/0+rwHn0NSTpE0gxJ90kaKunCNH+YpN1K1jtQ0tYl0+dI+kI9ajazYmm2PvqmOesm3RVdwAjgGxFxf1o0Mf0cBiwC/p6mDwTuAKYDRMRPVlqxBTJgwABmz569bHrOnDkMGJD7HsXWAPyZVkejhHg5arpHL+k0SVPT49uSzpV0csnysySdnp6fIelRSU9IOjvNGyRppqSrgKnAj4HdgSsl/SLtxd8haRBwAvAdSVMk/SvwJeAXaXozSaMkHZzafVHS2ZImS3pS0ifT/P6S/iJpmqQrJM2S1FrL31Gj+8xnPsMzzzzDCy+8wOLFi7nuuuv40pe+VO+ybAX4M60O79EDknYEjgF2JtsTfxj4CnA+cHFa7VBgH0l7A0PI7qYi4DZJewIvpflHRcRDqd3PAadHxERJwwAi4kVJlwKLIuKXab3bgDsi4sY03b7E+RGxg6STgNOBrwMjye7X+DNJw8n+eujovR0HHAcwcODA/L+kJtCrVy8uuugi9tlnH5YuXcqxxx7LNttsU++ybAX4M62ORgnxctSy62Z34OaIeAdA0k3AHsB6kjYC+gNvRsRsSacCewOPpdf2JQv4l4BZbSFfZTeln5OAL5fUfBBARIyV9GZHL4yIy4HLAYYOHRo1qK2h7Lfffuy33371LsOqyJ/pinPQd20M2Z3NNwCuT/ME/CwiLitdMXXJvFOjOt5PP5fSRMcqzKz+Gqlbphy17KOfABwoaXVJa5DtKU8gC/fDycJ+TFr3buBYSX0BJA2QtF6F21sIrNnFdDkeIOtOInUnfbzC15tZD9FMffQ1C/qImAyMAh4h65+/IiIei4hpZAE8NyLmpXXvAa4BHpT0JHAjlYf07cBB6eDrHsB1wBmSHpO0WZltnA3sLWkqcAjwCtkXhpnZcpop6GvaZRERvwJ+1cH87TqYdwFwQQfNbNtuvWElz8cD49Pzp4F/affarUueH13yukElzyeSnZoJ8DawT0QskbQr8JmIeB8zs3YaJcTL4b7p5Q0EbpDUAiwGvlHnesysAUlqmOENyuGgLxERzwCfrncdZtb4vEdvZlZwDnozs4Jz0JuZFZyD3syswBrp1MlyOOjNzHJw0JuZFZyD3sys4Jop6JvnjH8zswZSrSEQJG2i7I5509O9ME5N89dJ98d4Jv3MPfaWg97MrEJtV8ZW6ebgS4DvRsTWwC7Aycpug3omMC4ihgDj0nQuDnozsxyqtUcfEfPSIJBExEJgBjAAOAAYnVYbTXZ71FzcR29mlkMFffStkiaWTF+ebl7UUZuDyIZheRhYv22EX7KRdNfPV6mD3swslwqCfn5EDC2jvb7An4BvR8Q/StuPiJCU+2527roxM6tQud025X4ZSFqVLOT/GBFttzl9VdKGafmGwGt563XQm5nlUMWzbgRcCcxI9/BocxtwVHp+FHBr3lrddWNmlkMVz6P/LPBV4ElJU9K8HwLnkt0fYwQwi3Sb0zwc9GZmOVQr6CPifqCzxvaqxjYc9GZmOTTTlbEOejOzCnn0SjOzHsD3jDUzKzjv0ZuZFZyD3syswNxHb2bWAzjozcwKzkFvZlZwDnozs4Jz0JuZFZgPxpqZ9QAOejOzgnPQm5kVnIdAMDMrMPfRm5n1AA56M7OCc9CbmRWcg97MrOAc9GZmBeaDsWZmPYCD3sys4Bz0ZmYF56A3MyswSb4y1sys6LxHb2ZWcA56M7OCc9CbmRWYz6M3M+sBHPRmZgXnoDczKzgHvZlZwTVT0DfPGf9mZg2i7WBsOY8y2xsuaaakZyWdWe16vUdvZpZDta6MlbQKcDHwb8Ac4FFJt0XE9KpsAO/Rm5nlUsU9+p2AZyPi+YhYDFwHHFDNWr1Hv4ImTZo0X9KsetexkrQC8+tdhFVNT/o8P1HNxiZNmnS3pNYyV+8jaWLJ9OURcXnJ9ABgdsn0HGDnFa2xlIN+BUVE/3rXsLJImhgRQ+tdh1WHP8/8ImJ4vWuohLtuzMzqay6wScn0xmle1Tjozczq61FgiKTBknoDhwO3VXMD7rqxSlze/SrWRPx5NoCIWCLpm8DdwCrA7yJiWjW3oYioZntmZtZg3HVjZlZwDnozs4Jz0FtNSVpb0kn1rsMykg6RNEPSfZKGSrowzR8mabeS9Q6UtHXJ9DmSvlCPmm3FuY/euiSpV0Qs6Wy6jNcPAu6IiG1rUJ6VSdklmgLuAv4rIu5vt/wsYFFE/DJNjyL73G5cyaVaDTjoexBJXwNOBwJ4Avgx8DuyKyRfB46JiJfSf/L3gE8DDwDrtJu+OD36A+8C34iIpyStD1wKbJo2eSLwLbLLuWcCf4mIM2r/TotB0mnAsWnyCmADYHZEXJyWn0UKZ0lnAIcCqwE3R8TI9CV7N/AwsCNwA3AG2TnatwF3kv17+CbwELCU7N/BqcDNwNvp8b/I/q3cERE3SnoRGA38O7AqcEj6/PsD1wAbAQ+Sjd2yY0T0lKtvG1dE+NEDHsA2wNNAa5peB7gdOCpNHwvckp6PAu4AVulkehwwJD3fGbg3Pb8e+HZ6vgqwFjAImFrv999sD7JgfhJYA+gLTCP7ov1ryTrTyS602ZvsVEmRdcfeAeyZfvcfAruUvGY8MDQ9H0YW3gBnAaeXrDcKOLijaeBF4JT0/CTgivT8IuAH6flwsh2K1nr/Lv0In0ffg3weGBNp7yoiFkjaFfhyWn418POS9cdExNL205L6ArsBY0oGbFqtZBtfS+0vBd6W9PGavJvi251sz/wdAEk3AXsA60naiOyvqTcjYrakU8nC/rH02r7AEOAlYFZEPFSD+m5KPyfx0b+h3YGDACJirKQ3a7Bdy8FBb515p5PpFuCtiPjUSq7HMmOAg8m6ca5P8wT8LCIuK10xdd20/xyr5f30cynOkYbns256jnuBQyStCyBpHeDvZJdbAxwJTOiukYj4B/CCpENSO5K0fVo8jqxfHkmrSFoLWAisWc030kNMAA6UtLqkNcj2lCeQhfvhZGE/Jq17N3Bs+msLSQMkrVfh9tp/Tnk+twfIjhMgaW/Af801CAd9DxHZJdU/Bf4q6XHgV8ApwDGSngC+SnYQrhxHAiNSO9P4aOzsU4HPSXqS7E/6rSPiDeABSVMl/aJ676jYImIyWb/4I2QHU6+IiMfS57gmMDci5qV17yE7CPpg+t3fSOUhfTtwkKQpkvYgGxP9DEmPSdqszDbOBvaWNBU4BHiF7AvD6sxn3ZhZVUhaDVga2dgtuwK/cRdfY3DfmplVy0DgBkktwGLgG3WuxxLv0ZuZFZz76M3MCs5Bb2ZWcA56M7OCc9Bb3Ulamk7rmyppjKTVV6CtUZIOTs+vKB2BsYN1lxuxsYJtvCiptdz57dZZVOG2zpJ0eqU1mpVy0Fsj+GdEfCqyES4XAyeULpSU6+ywiPh6REzvYpVhZMM5mBWag94azQRg87S3PUHSbcD0dKXtLyQ9KukJScfDsitzL5I0U9L/BZZdESppvKSh6flwSZMlPS5pXBoe4ATgO20XCUnqL+lPaRuPSvpseu26ku6RNE3SFWRDDnRJ0i2SJqXXHNdu2Xlp/rg04iOSNpM0Nr1mgqRPVuOXaQY+j94aSNpz3xcYm2btAGwbES+ksHw7Ij6TLsx5QNI9ZCM6bglsDaxPNqLj79q12x/4LbBnamudNKjbpSw/Bvs1wHkRcb+kgWRDC2wFjATuj4hzJH0RGFHG2zk2beNjwKOS/pSuEl4DmBgR35H0k9T2N8lGnzwhIp6RtDNwCdkgcWYrzEFvjeBjkqak5xOAK8m6VB6JiBfS/L2Bf2nrfycbAnkI2XC816bRMl+WdG8H7e8C/K2trYhY0EkdXwC2LhmVs18aP2ZP0giNEXFnmaMyfkvSQen5JqnWN8iGDW4bjOwPwE3djAhqtsIc9NYI/tn+UvkUeKUjL4psDPS72623XxXraCEbu/29Dmopm6RhZF8au0bEu5LGA306WT3wiKBWY+6jt2ZxN3CipFUBJG2RRnX8G3BY6sPfEPhcB699CNhT0uD02nXS/PYjNN5DNtAbab224P0bcESaty/dj8q4FtlY8e+mvvZdSpa1kI08SWrz/m5GBDVbYQ56axZXkPW/T06jI15G9hfpzcAzadlVZLewW05EvA4cR9ZN8jgfdZ20H7HxW8DQdLB3Oh+d/XM22RfFNLIunJe6qXUs0EvSDOBcsi+aNu8AO6X38HngnDS/sxFBzVaYx7oxMys479GbmRWcgx9qKykAAAAlSURBVN7MrOAc9GZmBeegNzMrOAe9mVnBOejNzArOQW9mVnD/DyDHk+2H3rZMAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Closure\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Closure') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Closure')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10178, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1879, 204)\n", "====================\n", "Total test:  (124, 204)\n", "overfitting in test (0, 204)\n", "correct in test (124, 204)\n", "[0]\tvalidation_0-mae:0.44765\n", "Will train until validation_0-mae hasn't improved in 20 rounds.\n", "[1]\tvalidation_0-mae:0.40897\n", "[2]\tvalidation_0-mae:0.38299\n", "[3]\tvalidation_0-mae:0.36491\n", "[4]\tvalidation_0-mae:0.35307\n", "[5]\tvalidation_0-mae:0.33267\n", "[6]\tvalidation_0-mae:0.32101\n", "[7]\tvalidation_0-mae:0.31014\n", "[8]\tvalidation_0-mae:0.30680\n", "[9]\tvalidation_0-mae:0.30457\n", "[10]\tvalidation_0-mae:0.30251\n", "[11]\tvalidation_0-mae:0.30033\n", "[12]\tvalidation_0-mae:0.29890\n", "[13]\tvalidation_0-mae:0.29841\n", "[14]\tvalidation_0-mae:0.29803\n", "[15]\tvalidation_0-mae:0.29457\n", "[16]\tvalidation_0-mae:0.28960\n", "[17]\tvalidation_0-mae:0.28823\n", "[18]\tvalidation_0-mae:0.28637\n", "[19]\tvalidation_0-mae:0.28433\n", "[20]\tvalidation_0-mae:0.27881\n", "[21]\tvalidation_0-mae:0.27627\n", "[22]\tvalidation_0-mae:0.27779\n", "[23]\tvalidation_0-mae:0.27706\n", "[24]\tvalidation_0-mae:0.27133\n", "[25]\tvalidation_0-mae:0.26877\n", "[26]\tvalidation_0-mae:0.26537\n", "[27]\tvalidation_0-mae:0.26372\n", "[28]\tvalidation_0-mae:0.26303\n", "[29]\tvalidation_0-mae:0.25980\n", "[30]\tvalidation_0-mae:0.25632\n", "[31]\tvalidation_0-mae:0.25886\n", "[32]\tvalidation_0-mae:0.25861\n", "[33]\tvalidation_0-mae:0.25699\n", "[34]\tvalidation_0-mae:0.25663\n", "[35]\tvalidation_0-mae:0.25570\n", "[36]\tvalidation_0-mae:0.25401\n", "[37]\tvalidation_0-mae:0.25244\n", "[38]\tvalidation_0-mae:0.25227\n", "[39]\tvalidation_0-mae:0.24897\n", "[40]\tvalidation_0-mae:0.24961\n", "[41]\tvalidation_0-mae:0.24849\n", "[42]\tvalidation_0-mae:0.24894\n", "[43]\tvalidation_0-mae:0.24514\n", "[44]\tvalidation_0-mae:0.24370\n", "[45]\tvalidation_0-mae:0.24152\n", "[46]\tvalidation_0-mae:0.24125\n", "[47]\tvalidation_0-mae:0.23971\n", "[48]\tvalidation_0-mae:0.23617\n", "[49]\tvalidation_0-mae:0.23520\n", "[50]\tvalidation_0-mae:0.23669\n", "[51]\tvalidation_0-mae:0.23656\n", "[52]\tvalidation_0-mae:0.23789\n", "[53]\tvalidation_0-mae:0.23424\n", "[54]\tvalidation_0-mae:0.23552\n", "[55]\tvalidation_0-mae:0.23484\n", "[56]\tvalidation_0-mae:0.23574\n", "[57]\tvalidation_0-mae:0.23572\n", "[58]\tvalidation_0-mae:0.23386\n", "[59]\tvalidation_0-mae:0.23373\n", "[60]\tvalidation_0-mae:0.23322\n", "[61]\tvalidation_0-mae:0.23251\n", "[62]\tvalidation_0-mae:0.23245\n", "[63]\tvalidation_0-mae:0.23064\n", "[64]\tvalidation_0-mae:0.23098\n", "[65]\tvalidation_0-mae:0.23150\n", "[66]\tvalidation_0-mae:0.22993\n", "[67]\tvalidation_0-mae:0.23007\n", "[68]\tvalidation_0-mae:0.22966\n", "[69]\tvalidation_0-mae:0.23012\n", "[70]\tvalidation_0-mae:0.22849\n", "[71]\tvalidation_0-mae:0.22659\n", "[72]\tvalidation_0-mae:0.22734\n", "[73]\tvalidation_0-mae:0.22723\n", "[74]\tvalidation_0-mae:0.22591\n", "[75]\tvalidation_0-mae:0.22529\n", "[76]\tvalidation_0-mae:0.22306\n", "[77]\tvalidation_0-mae:0.22217\n", "[78]\tvalidation_0-mae:0.22421\n", "[79]\tvalidation_0-mae:0.22419\n", "[80]\tvalidation_0-mae:0.22343\n", "[81]\tvalidation_0-mae:0.22354\n", "[82]\tvalidation_0-mae:0.22304\n", "[83]\tvalidation_0-mae:0.22200\n", "[84]\tvalidation_0-mae:0.22130\n", "[85]\tvalidation_0-mae:0.22011\n", "[86]\tvalidation_0-mae:0.22028\n", "[87]\tvalidation_0-mae:0.21938\n", "[88]\tvalidation_0-mae:0.22003\n", "[89]\tvalidation_0-mae:0.21988\n", "[90]\tvalidation_0-mae:0.21958\n", "[91]\tvalidation_0-mae:0.21987\n", "[92]\tvalidation_0-mae:0.22028\n", "[93]\tvalidation_0-mae:0.21989\n", "[94]\tvalidation_0-mae:0.21746\n", "[95]\tvalidation_0-mae:0.21644\n", "[96]\tvalidation_0-mae:0.21751\n", "[97]\tvalidation_0-mae:0.21816\n", "[98]\tvalidation_0-mae:0.21884\n", "[99]\tvalidation_0-mae:0.21854\n", "f1 score: 0.0\n", "acc score: 0.8467741935483871\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Camel\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'camel') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'camel')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10214, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1915, 204)\n", "====================\n", "Total test:  (88, 204)\n", "overfitting in test (0, 204)\n", "correct in test (88, 204)\n", "[0]\tvalidation_0-mae:0.48785\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.47509\n", "[2]\tvalidation_0-mae:0.48995\n", "[3]\tvalidation_0-mae:0.45401\n", "[4]\tvalidation_0-mae:0.46021\n", "[5]\tvalidation_0-mae:0.44464\n", "[6]\tvalidation_0-mae:0.43120\n", "[7]\tvalidation_0-mae:0.43321\n", "[8]\tvalidation_0-mae:0.43885\n", "[9]\tvalidation_0-mae:0.42654\n", "[10]\tvalidation_0-mae:0.42424\n", "[11]\tvalidation_0-mae:0.42285\n", "[12]\tvalidation_0-mae:0.41853\n", "[13]\tvalidation_0-mae:0.41132\n", "[14]\tvalidation_0-mae:0.41744\n", "[15]\tvalidation_0-mae:0.42106\n", "[16]\tvalidation_0-mae:0.41442\n", "[17]\tvalidation_0-mae:0.41382\n", "[18]\tvalidation_0-mae:0.41149\n", "[19]\tvalidation_0-mae:0.41363\n", "[20]\tvalidation_0-mae:0.40660\n", "[21]\tvalidation_0-mae:0.40266\n", "[22]\tvalidation_0-mae:0.39265\n", "[23]\tvalidation_0-mae:0.38996\n", "[24]\tvalidation_0-mae:0.38816\n", "[25]\tvalidation_0-mae:0.37705\n", "[26]\tvalidation_0-mae:0.37624\n", "[27]\tvalidation_0-mae:0.37903\n", "[28]\tvalidation_0-mae:0.37831\n", "[29]\tvalidation_0-mae:0.37653\n", "[30]\tvalidation_0-mae:0.37359\n", "[31]\tvalidation_0-mae:0.37287\n", "[32]\tvalidation_0-mae:0.37503\n", "[33]\tvalidation_0-mae:0.37324\n", "[34]\tvalidation_0-mae:0.37089\n", "[35]\tvalidation_0-mae:0.37322\n", "[36]\tvalidation_0-mae:0.37002\n", "[37]\tvalidation_0-mae:0.36673\n", "[38]\tvalidation_0-mae:0.36501\n", "[39]\tvalidation_0-mae:0.36317\n", "[40]\tvalidation_0-mae:0.36294\n", "[41]\tvalidation_0-mae:0.35975\n", "[42]\tvalidation_0-mae:0.35373\n", "[43]\tvalidation_0-mae:0.35014\n", "[44]\tvalidation_0-mae:0.34948\n", "[45]\tvalidation_0-mae:0.35016\n", "[46]\tvalidation_0-mae:0.34967\n", "[47]\tvalidation_0-mae:0.34977\n", "[48]\tvalidation_0-mae:0.34959\n", "[49]\tvalidation_0-mae:0.34220\n", "[50]\tvalidation_0-mae:0.33986\n", "[51]\tvalidation_0-mae:0.33766\n", "[52]\tvalidation_0-mae:0.33843\n", "[53]\tvalidation_0-mae:0.33756\n", "[54]\tvalidation_0-mae:0.33622\n", "[55]\tvalidation_0-mae:0.33630\n", "[56]\tvalidation_0-mae:0.33787\n", "[57]\tvalidation_0-mae:0.33447\n", "[58]\tvalidation_0-mae:0.33489\n", "[59]\tvalidation_0-mae:0.33191\n", "[60]\tvalidation_0-mae:0.33215\n", "[61]\tvalidation_0-mae:0.33102\n", "[62]\tvalidation_0-mae:0.33108\n", "[63]\tvalidation_0-mae:0.33113\n", "[64]\tvalidation_0-mae:0.33096\n", "[65]\tvalidation_0-mae:0.33022\n", "[66]\tvalidation_0-mae:0.32896\n", "[67]\tvalidation_0-mae:0.32599\n", "[68]\tvalidation_0-mae:0.32559\n", "[69]\tvalidation_0-mae:0.32215\n", "[70]\tvalidation_0-mae:0.32053\n", "[71]\tvalidation_0-mae:0.32128\n", "[72]\tvalidation_0-mae:0.32365\n", "[73]\tvalidation_0-mae:0.32283\n", "[74]\tvalidation_0-mae:0.32188\n", "[75]\tvalidation_0-mae:0.32111\n", "[76]\tvalidation_0-mae:0.31913\n", "[77]\tvalidation_0-mae:0.31993\n", "[78]\tvalidation_0-mae:0.31919\n", "[79]\tvalidation_0-mae:0.31956\n", "[80]\tvalidation_0-mae:0.31996\n", "[81]\tvalidation_0-mae:0.31914\n", "[82]\tvalidation_0-mae:0.31785\n", "[83]\tvalidation_0-mae:0.31769\n", "[84]\tvalidation_0-mae:0.31690\n", "[85]\tvalidation_0-mae:0.31502\n", "[86]\tvalidation_0-mae:0.31482\n", "[87]\tvalidation_0-mae:0.31457\n", "[88]\tvalidation_0-mae:0.31479\n", "[89]\tvalidation_0-mae:0.31350\n", "[90]\tvalidation_0-mae:0.31406\n", "[91]\tvalidation_0-mae:0.31401\n", "[92]\tvalidation_0-mae:0.30998\n", "[93]\tvalidation_0-mae:0.31125\n", "[94]\tvalidation_0-mae:0.31066\n", "[95]\tvalidation_0-mae:0.30959\n", "[96]\tvalidation_0-mae:0.30870\n", "[97]\tvalidation_0-mae:0.30707\n", "[98]\tvalidation_0-mae:0.30781\n", "[99]\tvalidation_0-mae:0.30729\n", "f1 score: 0.0\n", "acc score: 0.7727272727272727\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Jsoup\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Jsoup') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Jsoup')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10230, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1931, 204)\n", "====================\n", "Total test:  (72, 204)\n", "overfitting in test (0, 204)\n", "correct in test (72, 204)\n", "[0]\tvalidation_0-mae:0.42705\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.38372\n", "[2]\tvalidation_0-mae:0.34979\n", "[3]\tvalidation_0-mae:0.32594\n", "[4]\tvalidation_0-mae:0.30900\n", "[5]\tvalidation_0-mae:0.29686\n", "[6]\tvalidation_0-mae:0.28886\n", "[7]\tvalidation_0-mae:0.28248\n", "[8]\tvalidation_0-mae:0.28493\n", "[9]\tvalidation_0-mae:0.28375\n", "[10]\tvalidation_0-mae:0.28229\n", "[11]\tvalidation_0-mae:0.28105\n", "[12]\tvalidation_0-mae:0.27806\n", "[13]\tvalidation_0-mae:0.27094\n", "[14]\tvalidation_0-mae:0.26639\n", "[15]\tvalidation_0-mae:0.26649\n", "[16]\tvalidation_0-mae:0.26857\n", "[17]\tvalidation_0-mae:0.26193\n", "[18]\tvalidation_0-mae:0.25737\n", "[19]\tvalidation_0-mae:0.24963\n", "[20]\tvalidation_0-mae:0.24439\n", "[21]\tvalidation_0-mae:0.24358\n", "[22]\tvalidation_0-mae:0.24320\n", "[23]\tvalidation_0-mae:0.23861\n", "[24]\tvalidation_0-mae:0.23664\n", "[25]\tvalidation_0-mae:0.23098\n", "[26]\tvalidation_0-mae:0.22939\n", "[27]\tvalidation_0-mae:0.22725\n", "[28]\tvalidation_0-mae:0.22716\n", "[29]\tvalidation_0-mae:0.22833\n", "[30]\tvalidation_0-mae:0.22280\n", "[31]\tvalidation_0-mae:0.22152\n", "[32]\tvalidation_0-mae:0.22096\n", "[33]\tvalidation_0-mae:0.22072\n", "[34]\tvalidation_0-mae:0.21877\n", "[35]\tvalidation_0-mae:0.21040\n", "[36]\tvalidation_0-mae:0.20982\n", "[37]\tvalidation_0-mae:0.20713\n", "[38]\tvalidation_0-mae:0.20725\n", "[39]\tvalidation_0-mae:0.20567\n", "[40]\tvalidation_0-mae:0.20217\n", "[41]\tvalidation_0-mae:0.20067\n", "[42]\tvalidation_0-mae:0.20045\n", "[43]\tvalidation_0-mae:0.20101\n", "[44]\tvalidation_0-mae:0.20133\n", "[45]\tvalidation_0-mae:0.19930\n", "[46]\tvalidation_0-mae:0.19906\n", "[47]\tvalidation_0-mae:0.19880\n", "[48]\tvalidation_0-mae:0.19755\n", "[49]\tvalidation_0-mae:0.19625\n", "[50]\tvalidation_0-mae:0.19534\n", "[51]\tvalidation_0-mae:0.19437\n", "[52]\tvalidation_0-mae:0.19455\n", "[53]\tvalidation_0-mae:0.19506\n", "[54]\tvalidation_0-mae:0.19345\n", "[55]\tvalidation_0-mae:0.19136\n", "[56]\tvalidation_0-mae:0.19338\n", "[57]\tvalidation_0-mae:0.19077\n", "[58]\tvalidation_0-mae:0.19051\n", "[59]\tvalidation_0-mae:0.18992\n", "[60]\tvalidation_0-mae:0.18998\n", "[61]\tvalidation_0-mae:0.18934\n", "[62]\tvalidation_0-mae:0.18790\n", "[63]\tvalidation_0-mae:0.18457\n", "[64]\tvalidation_0-mae:0.18386\n", "[65]\tvalidation_0-mae:0.18154\n", "[66]\tvalidation_0-mae:0.18097\n", "[67]\tvalidation_0-mae:0.17913\n", "[68]\tvalidation_0-mae:0.17772\n", "[69]\tvalidation_0-mae:0.17738\n", "[70]\tvalidation_0-mae:0.17849\n", "[71]\tvalidation_0-mae:0.17873\n", "[72]\tvalidation_0-mae:0.17825\n", "[73]\tvalidation_0-mae:0.17614\n", "[74]\tvalidation_0-mae:0.17601\n", "[75]\tvalidation_0-mae:0.17542\n", "[76]\tvalidation_0-mae:0.17427\n", "[77]\tvalidation_0-mae:0.17157\n", "[78]\tvalidation_0-mae:0.17124\n", "[79]\tvalidation_0-mae:0.17033\n", "[80]\tvalidation_0-mae:0.17014\n", "[81]\tvalidation_0-mae:0.16963\n", "[82]\tvalidation_0-mae:0.16972\n", "[83]\tvalidation_0-mae:0.17022\n", "[84]\tvalidation_0-mae:0.17034\n", "[85]\tvalidation_0-mae:0.16968\n", "[86]\tvalidation_0-mae:0.16818\n", "[87]\tvalidation_0-mae:0.16644\n", "[88]\tvalidation_0-mae:0.16412\n", "[89]\tvalidation_0-mae:0.16427\n", "[90]\tvalidation_0-mae:0.16449\n", "[91]\tvalidation_0-mae:0.16188\n", "[92]\tvalidation_0-mae:0.16048\n", "[93]\tvalidation_0-mae:0.15990\n", "[94]\tvalidation_0-mae:0.15849\n", "[95]\tvalidation_0-mae:0.15776\n", "[96]\tvalidation_0-mae:0.15735\n", "[97]\tvalidation_0-mae:0.15629\n", "[98]\tvalidation_0-mae:0.15586\n", "[99]\tvalidation_0-mae:0.15435\n", "f1 score: 0.0\n", "acc score: 0.9166666666666666\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Log4J\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'logging') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'logging')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10260, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1961, 204)\n", "====================\n", "Total test:  (42, 204)\n", "overfitting in test (0, 204)\n", "correct in test (42, 204)\n", "[0]\tvalidation_0-mae:0.43168\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.39348\n", "[2]\tvalidation_0-mae:0.35027\n", "[3]\tvalidation_0-mae:0.31253\n", "[4]\tvalidation_0-mae:0.29874\n", "[5]\tvalidation_0-mae:0.29437\n", "[6]\tvalidation_0-mae:0.28468\n", "[7]\tvalidation_0-mae:0.28059\n", "[8]\tvalidation_0-mae:0.27727\n", "[9]\tvalidation_0-mae:0.27089\n", "[10]\tvalidation_0-mae:0.26719\n", "[11]\tvalidation_0-mae:0.27259\n", "[12]\tvalidation_0-mae:0.26837\n", "[13]\tvalidation_0-mae:0.25578\n", "[14]\tvalidation_0-mae:0.25913\n", "[15]\tvalidation_0-mae:0.25594\n", "[16]\tvalidation_0-mae:0.25758\n", "[17]\tvalidation_0-mae:0.25989\n", "[18]\tvalidation_0-mae:0.25974\n", "[19]\tvalidation_0-mae:0.25911\n", "[20]\tvalidation_0-mae:0.25819\n", "[21]\tvalidation_0-mae:0.25643\n", "[22]\tvalidation_0-mae:0.25405\n", "[23]\tvalidation_0-mae:0.25232\n", "[24]\tvalidation_0-mae:0.25159\n", "[25]\tvalidation_0-mae:0.25098\n", "[26]\tvalidation_0-mae:0.25401\n", "[27]\tvalidation_0-mae:0.25428\n", "[28]\tvalidation_0-mae:0.25480\n", "[29]\tvalidation_0-mae:0.25132\n", "[30]\tvalidation_0-mae:0.24789\n", "[31]\tvalidation_0-mae:0.24726\n", "[32]\tvalidation_0-mae:0.24606\n", "[33]\tvalidation_0-mae:0.24483\n", "[34]\tvalidation_0-mae:0.24590\n", "[35]\tvalidation_0-mae:0.24449\n", "[36]\tvalidation_0-mae:0.24588\n", "[37]\tvalidation_0-mae:0.24275\n", "[38]\tvalidation_0-mae:0.24117\n", "[39]\tvalidation_0-mae:0.23552\n", "[40]\tvalidation_0-mae:0.23329\n", "[41]\tvalidation_0-mae:0.23284\n", "[42]\tvalidation_0-mae:0.23138\n", "[43]\tvalidation_0-mae:0.22560\n", "[44]\tvalidation_0-mae:0.22350\n", "[45]\tvalidation_0-mae:0.22124\n", "[46]\tvalidation_0-mae:0.21876\n", "[47]\tvalidation_0-mae:0.21574\n", "[48]\tvalidation_0-mae:0.21579\n", "[49]\tvalidation_0-mae:0.21435\n", "[50]\tvalidation_0-mae:0.21403\n", "[51]\tvalidation_0-mae:0.20922\n", "[52]\tvalidation_0-mae:0.20922\n", "[53]\tvalidation_0-mae:0.20853\n", "[54]\tvalidation_0-mae:0.20680\n", "[55]\tvalidation_0-mae:0.20165\n", "[56]\tvalidation_0-mae:0.20192\n", "[57]\tvalidation_0-mae:0.20217\n", "[58]\tvalidation_0-mae:0.20246\n", "[59]\tvalidation_0-mae:0.20278\n", "[60]\tvalidation_0-mae:0.20195\n", "[61]\tvalidation_0-mae:0.19505\n", "[62]\tvalidation_0-mae:0.19240\n", "[63]\tvalidation_0-mae:0.19184\n", "[64]\tvalidation_0-mae:0.19206\n", "[65]\tvalidation_0-mae:0.19156\n", "[66]\tvalidation_0-mae:0.19207\n", "[67]\tvalidation_0-mae:0.19273\n", "[68]\tvalidation_0-mae:0.19296\n", "[69]\tvalidation_0-mae:0.18964\n", "[70]\tvalidation_0-mae:0.18688\n", "[71]\tvalidation_0-mae:0.18562\n", "[72]\tvalidation_0-mae:0.18308\n", "[73]\tvalidation_0-mae:0.18373\n", "[74]\tvalidation_0-mae:0.18217\n", "[75]\tvalidation_0-mae:0.18196\n", "[76]\tvalidation_0-mae:0.18066\n", "[77]\tvalidation_0-mae:0.17983\n", "[78]\tvalidation_0-mae:0.18132\n", "[79]\tvalidation_0-mae:0.18019\n", "[80]\tvalidation_0-mae:0.18014\n", "[81]\tvalidation_0-mae:0.18093\n", "[82]\tvalidation_0-mae:0.17932\n", "[83]\tvalidation_0-mae:0.18067\n", "[84]\tvalidation_0-mae:0.18063\n", "[85]\tvalidation_0-mae:0.17946\n", "[86]\tvalidation_0-mae:0.17958\n", "[87]\tvalidation_0-mae:0.17938\n", "[88]\tvalidation_0-mae:0.17733\n", "[89]\tvalidation_0-mae:0.17726\n", "[90]\tvalidation_0-mae:0.17242\n", "[91]\tvalidation_0-mae:0.17278\n", "[92]\tvalidation_0-mae:0.17288\n", "[93]\tvalidation_0-mae:0.17244\n", "[94]\tvalidation_0-mae:0.17237\n", "[95]\tvalidation_0-mae:0.17158\n", "[96]\tvalidation_0-mae:0.17466\n", "[97]\tvalidation_0-mae:0.17455\n", "[98]\tvalidation_0-mae:0.17475\n", "[99]\tvalidation_0-mae:0.17041\n", "f1 score: 0.0\n", "acc score: 0.9285714285714286\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Maven\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'maven') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'maven')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10267, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1968, 204)\n", "====================\n", "Total test:  (35, 204)\n", "overfitting in test (0, 204)\n", "correct in test (35, 204)\n", "[0]\tvalidation_0-mae:0.46384\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.45806\n", "[2]\tvalidation_0-mae:0.43431\n", "[3]\tvalidation_0-mae:0.42070\n", "[4]\tvalidation_0-mae:0.38533\n", "[5]\tvalidation_0-mae:0.38488\n", "[6]\tvalidation_0-mae:0.37535\n", "[7]\tvalidation_0-mae:0.37011\n", "[8]\tvalidation_0-mae:0.36108\n", "[9]\tvalidation_0-mae:0.36074\n", "[10]\tvalidation_0-mae:0.36706\n", "[11]\tvalidation_0-mae:0.35982\n", "[12]\tvalidation_0-mae:0.36276\n", "[13]\tvalidation_0-mae:0.35728\n", "[14]\tvalidation_0-mae:0.34835\n", "[15]\tvalidation_0-mae:0.35500\n", "[16]\tvalidation_0-mae:0.34710\n", "[17]\tvalidation_0-mae:0.34603\n", "[18]\tvalidation_0-mae:0.35092\n", "[19]\tvalidation_0-mae:0.35070\n", "[20]\tvalidation_0-mae:0.35180\n", "[21]\tvalidation_0-mae:0.35045\n", "[22]\tvalidation_0-mae:0.34750\n", "[23]\tvalidation_0-mae:0.34258\n", "[24]\tvalidation_0-mae:0.34235\n", "[25]\tvalidation_0-mae:0.34177\n", "[26]\tvalidation_0-mae:0.34025\n", "[27]\tvalidation_0-mae:0.33771\n", "[28]\tvalidation_0-mae:0.34294\n", "[29]\tvalidation_0-mae:0.34400\n", "[30]\tvalidation_0-mae:0.33863\n", "[31]\tvalidation_0-mae:0.33825\n", "[32]\tvalidation_0-mae:0.32930\n", "[33]\tvalidation_0-mae:0.32458\n", "[34]\tvalidation_0-mae:0.32321\n", "[35]\tvalidation_0-mae:0.32108\n", "[36]\tvalidation_0-mae:0.32192\n", "[37]\tvalidation_0-mae:0.32046\n", "[38]\tvalidation_0-mae:0.32375\n", "[39]\tvalidation_0-mae:0.32046\n", "[40]\tvalidation_0-mae:0.32129\n", "[41]\tvalidation_0-mae:0.32138\n", "[42]\tvalidation_0-mae:0.32287\n", "[43]\tvalidation_0-mae:0.32251\n", "[44]\tvalidation_0-mae:0.32187\n", "[45]\tvalidation_0-mae:0.32523\n", "[46]\tvalidation_0-mae:0.32266\n", "[47]\tvalidation_0-mae:0.32347\n", "[48]\tvalidation_0-mae:0.32444\n", "[49]\tvalidation_0-mae:0.32779\n", "[50]\tvalidation_0-mae:0.32703\n", "[51]\tvalidation_0-mae:0.32354\n", "[52]\tvalidation_0-mae:0.32276\n", "[53]\tvalidation_0-mae:0.32350\n", "[54]\tvalidation_0-mae:0.32268\n", "[55]\tvalidation_0-mae:0.32328\n", "[56]\tvalidation_0-mae:0.31872\n", "[57]\tvalidation_0-mae:0.31773\n", "[58]\tvalidation_0-mae:0.31950\n", "[59]\tvalidation_0-mae:0.32299\n", "[60]\tvalidation_0-mae:0.32465\n", "[61]\tvalidation_0-mae:0.32322\n", "[62]\tvalidation_0-mae:0.32219\n", "[63]\tvalidation_0-mae:0.32027\n", "[64]\tvalidation_0-mae:0.31931\n", "[65]\tvalidation_0-mae:0.31724\n", "[66]\tvalidation_0-mae:0.31453\n", "[67]\tvalidation_0-mae:0.31455\n", "[68]\tvalidation_0-mae:0.31315\n", "[69]\tvalidation_0-mae:0.31422\n", "[70]\tvalidation_0-mae:0.31463\n", "[71]\tvalidation_0-mae:0.31412\n", "[72]\tvalidation_0-mae:0.31458\n", "[73]\tvalidation_0-mae:0.31496\n", "[74]\tvalidation_0-mae:0.31435\n", "[75]\tvalidation_0-mae:0.31051\n", "[76]\tvalidation_0-mae:0.31209\n", "[77]\tvalidation_0-mae:0.30915\n", "[78]\tvalidation_0-mae:0.30805\n", "[79]\tvalidation_0-mae:0.30710\n", "[80]\tvalidation_0-mae:0.30680\n", "[81]\tvalidation_0-mae:0.30662\n", "[82]\tvalidation_0-mae:0.30607\n", "[83]\tvalidation_0-mae:0.30652\n", "[84]\tvalidation_0-mae:0.30606\n", "[85]\tvalidation_0-mae:0.30773\n", "[86]\tvalidation_0-mae:0.30768\n", "[87]\tvalidation_0-mae:0.30687\n", "[88]\tvalidation_0-mae:0.30735\n", "[89]\tvalidation_0-mae:0.30709\n", "[90]\tvalidation_0-mae:0.30522\n", "[91]\tvalidation_0-mae:0.30395\n", "[92]\tvalidation_0-mae:0.30387\n", "[93]\tvalidation_0-mae:0.30584\n", "[94]\tvalidation_0-mae:0.30524\n", "[95]\tvalidation_0-mae:0.30208\n", "[96]\tvalidation_0-mae:0.30108\n", "[97]\tvalidation_0-mae:0.29962\n", "[98]\tvalidation_0-mae:0.29970\n", "[99]\tvalidation_0-mae:0.30048\n", "f1 score: 0.0\n", "acc score: 0.7714285714285715\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# <PERSON><PERSON><PERSON>\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Mockito') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Mockito')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10287, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1988, 204)\n", "====================\n", "Total test:  (15, 204)\n", "overfitting in test (0, 204)\n", "correct in test (15, 204)\n", "[0]\tvalidation_0-mae:0.49617\n", "Will train until validation_0-mae hasn't improved in 20 rounds.\n", "[1]\tvalidation_0-mae:0.48690\n", "[2]\tvalidation_0-mae:0.48625\n", "[3]\tvalidation_0-mae:0.45686\n", "[4]\tvalidation_0-mae:0.45289\n", "[5]\tvalidation_0-mae:0.46372\n", "[6]\tvalidation_0-mae:0.46235\n", "[7]\tvalidation_0-mae:0.47552\n", "[8]\tvalidation_0-mae:0.45440\n", "[9]\tvalidation_0-mae:0.44816\n", "[10]\tvalidation_0-mae:0.45398\n", "[11]\tvalidation_0-mae:0.45423\n", "[12]\tvalidation_0-mae:0.44052\n", "[13]\tvalidation_0-mae:0.45472\n", "[14]\tvalidation_0-mae:0.46177\n", "[15]\tvalidation_0-mae:0.46421\n", "[16]\tvalidation_0-mae:0.46782\n", "[17]\tvalidation_0-mae:0.46433\n", "[18]\tvalidation_0-mae:0.46256\n", "[19]\tvalidation_0-mae:0.45650\n", "[20]\tvalidation_0-mae:0.46493\n", "[21]\tvalidation_0-mae:0.46199\n", "[22]\tvalidation_0-mae:0.46533\n", "[23]\tvalidation_0-mae:0.46382\n", "[24]\tvalidation_0-mae:0.44200\n", "[25]\tvalidation_0-mae:0.43643\n", "[26]\tvalidation_0-mae:0.43015\n", "[27]\tvalidation_0-mae:0.41735\n", "[28]\tvalidation_0-mae:0.41041\n", "[29]\tvalidation_0-mae:0.40743\n", "[30]\tvalidation_0-mae:0.40721\n", "[31]\tvalidation_0-mae:0.40500\n", "[32]\tvalidation_0-mae:0.40618\n", "[33]\tvalidation_0-mae:0.40716\n", "[34]\tvalidation_0-mae:0.40790\n", "[35]\tvalidation_0-mae:0.40865\n", "[36]\tvalidation_0-mae:0.40044\n", "[37]\tvalidation_0-mae:0.40023\n", "[38]\tvalidation_0-mae:0.39866\n", "[39]\tvalidation_0-mae:0.39544\n", "[40]\tvalidation_0-mae:0.39736\n", "[41]\tvalidation_0-mae:0.39827\n", "[42]\tvalidation_0-mae:0.39499\n", "[43]\tvalidation_0-mae:0.39185\n", "[44]\tvalidation_0-mae:0.39120\n", "[45]\tvalidation_0-mae:0.38542\n", "[46]\tvalidation_0-mae:0.38969\n", "[47]\tvalidation_0-mae:0.38648\n", "[48]\tvalidation_0-mae:0.38539\n", "[49]\tvalidation_0-mae:0.38849\n", "[50]\tvalidation_0-mae:0.38958\n", "[51]\tvalidation_0-mae:0.39093\n", "[52]\tvalidation_0-mae:0.38960\n", "[53]\tvalidation_0-mae:0.38836\n", "[54]\tvalidation_0-mae:0.38966\n", "[55]\tvalidation_0-mae:0.38542\n", "[56]\tvalidation_0-mae:0.38201\n", "[57]\tvalidation_0-mae:0.38555\n", "[58]\tvalidation_0-mae:0.38886\n", "[59]\tvalidation_0-mae:0.38493\n", "[60]\tvalidation_0-mae:0.38590\n", "[61]\tvalidation_0-mae:0.38180\n", "[62]\tvalidation_0-mae:0.38079\n", "[63]\tvalidation_0-mae:0.38317\n", "[64]\tvalidation_0-mae:0.38271\n", "[65]\tvalidation_0-mae:0.38165\n", "[66]\tvalidation_0-mae:0.38118\n", "[67]\tvalidation_0-mae:0.37935\n", "[68]\tvalidation_0-mae:0.37463\n", "[69]\tvalidation_0-mae:0.37458\n", "[70]\tvalidation_0-mae:0.37117\n", "[71]\tvalidation_0-mae:0.36961\n", "[72]\tvalidation_0-mae:0.37193\n", "[73]\tvalidation_0-mae:0.36462\n", "[74]\tvalidation_0-mae:0.36456\n", "[75]\tvalidation_0-mae:0.36357\n", "[76]\tvalidation_0-mae:0.36333\n", "[77]\tvalidation_0-mae:0.36211\n", "[78]\tvalidation_0-mae:0.36271\n", "[79]\tvalidation_0-mae:0.36425\n", "[80]\tvalidation_0-mae:0.36437\n", "[81]\tvalidation_0-mae:0.36493\n", "[82]\tvalidation_0-mae:0.36651\n", "[83]\tvalidation_0-mae:0.36325\n", "[84]\tvalidation_0-mae:0.35860\n", "[85]\tvalidation_0-mae:0.35838\n", "[86]\tvalidation_0-mae:0.36050\n", "[87]\tvalidation_0-mae:0.36324\n", "[88]\tvalidation_0-mae:0.36268\n", "[89]\tvalidation_0-mae:0.36092\n", "[90]\tvalidation_0-mae:0.35986\n", "[91]\tvalidation_0-mae:0.36008\n", "[92]\tvalidation_0-mae:0.36107\n", "[93]\tvalidation_0-mae:0.36199\n", "[94]\tvalidation_0-mae:0.36138\n", "[95]\tvalidation_0-mae:0.36146\n", "[96]\tvalidation_0-mae:0.36130\n", "[97]\tvalidation_0-mae:0.36169\n", "[98]\tvalidation_0-mae:0.36348\n", "[99]\tvalidation_0-mae:0.35779\n", "f1 score: 0.0\n", "acc score: 0.8\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXQAAAEmCAYAAAByJWuvAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjEsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+j8jraAAAej0lEQVR4nO3de5xVdb3/8dcb8EZ4ScEb6PFuBylNKcNSQTtqZonnp2VRGVimllEe7UQ31N/pl4/seLKjHSUwKO+YlmKpHNNEUxGQFFCxQkXUEEzyFsjw+f2xvqObaWbYe82e2WuveT8fj/1gr8v+7s+ere/5znet9V2KCMzMrPn1aXQBZmZWHw50M7OScKCbmZWEA93MrCQc6GZmJeFANzMrCQe6FZ6kzSTdLGmVpOldaGeMpNvrWVujSDpY0uONrsOKRT4P3epF0ieBM4F3AC8D84HvRsQ9XWz308AZwEERsbbLhRacpAD2jIg/NroWay7uoVtdSDoT+CHw/4DtgJ2BHwPH1qH5fwIW94Ywr4akfo2uwQoqIvzwo0sPYEvgFeCETvbZhCzwn02PHwKbpG0jgWeAfwOWA88BY9O2c4E1wBvpPU4GzgGuqGh7FyCAfmn5s8Cfyf5KWAKMqVh/T8XrDgIeBFalfw+q2HYX8H+Be1M7twMDO/hsrfV/raL+0cDRwGLgReAbFfu/F7gPeCntezGwcdp2d/osr6bP+/GK9v8deB74eeu69Jrd03vsn5Z3BF4ARjb6vw0/evbhHrrVwwhgU+DGTvb5JvA+YD9gX7JQ+1bF9u3JfjEMJgvtSyS9PSImkvX6r42IARExpbNCJL0N+BHwoYjYnCy057ez39bALWnfbYALgVskbVOx2yeBscC2wMbAWZ289fZkP4PBwHeAnwCfAg4ADga+LWnXtG8L8FVgINnP7nDgdICIOCTts2/6vNdWtL812V8rp1S+cUT8iSzsr5DUH/gpMC0i7uqkXishB7rVwzbAiuh8SGQMcF5ELI+IF8h63p+u2P5G2v5GRPyarHe6d8561gHDJG0WEc9FxMJ29vkw8ERE/Dwi1kbE1cBjwEcq9vlpRCyOiNeB68h+GXXkDbLjBW8A15CF9UUR8XJ6/0Vkv8iIiLkRcX963yeBy4BDq/hMEyNidapnPRHxE+CPwAPADmS/QK2XcaBbPawEBm5gbHdH4KmK5afSujfbaPML4TVgQK2FRMSrZMMUpwLPSbpF0juqqKe1psEVy8/XUM/KiGhJz1sD9y8V219vfb2kvSTNkPS8pL+R/QUysJO2AV6IiL9vYJ+fAMOA/46I1RvY10rIgW71cB+wmmzcuCPPkg0XtNo5rcvjVaB/xfL2lRsj4raI+BeynupjZEG3oXpaa1qWs6Za/A9ZXXtGxBbANwBt4DWdno4maQDZcYkpwDlpSMl6GQe6dVlErCIbN75E0mhJ/SVtJOlDkr6fdrsa+JakQZIGpv2vyPmW84FDJO0saUtgQusGSdtJOjaNpa8mG7pZ104bvwb2kvRJSf0kfRwYCszIWVMtNgf+BryS/no4rc32vwC71djmRcCciPgc2bGBS7tcpTUdB7rVRUT8J9k56N8iO8NiKfAl4Jdpl/8A5gAPA48A89K6PO81E7g2tTWX9UO4T6rjWbIzPw7lHwOTiFgJHEN2Zs1KsjNUjomIFXlqqtFZZAdcXyb76+HaNtvPAaZJeknSxzbUmKRjgaN463OeCewvaUzdKram4AuLzMxKwj10M7OScKCbmTWQpMslLZe0oGLdBZIek/SwpBslbVVNWw50M7PGmkp2DKTSTGBYRLyL7GrjCW1f1B4HuplZA0XE3WQH8CvX3V5xXcb9wJBq2vIkP12UZsazEtlrr70aXYLV2eLFi1dExKB6tVfj//cLgcqLwiZFxKQaXj+OfzwTql0OdLM2LrvsskaXYHU2atSotlcF96S/R8TwPC+U9E1gLXBlNfs70M3McpA2dHFvJu+p4ZI+S3atxOFRZSMOdDOzHLoz0CUdRXax26ER8Vq1r/NBUTOzHCRV9aiinavJ5kPaW9Izkk4mmyN/c2CmpPmSqprKwT10M7MaSaJPn+r6wy0tLZ1uj4hPtLO603n/O+JANzPLodohl57kQDczy8GBbmZWEg50M7OScKCbmZVAtWew9DQHuplZDg50M7OScKCbmZWEA93MrCQc6GZmJVDLlaI9yYFuZpaDe+hmZiXhQDczKwkHuplZCfjCIjOzEnGgm5mVhAPdzKwkHOhmZiXhQDczKwEfFDUzKxEHuplZSfjSfzOzknAP3cysBDyGbmZWIg50M7OScKCbmZWEA93MrCQc6GZmJeCDomZmJeJANzMriSIGevEudTIzawJ9+vSp6rEhki6XtFzSgop1W0uaKemJ9O/bq6qpC5/HzKxXah1Dr+ZRhanAUW3WfR24IyL2BO5IyxvkQDczy6FegR4RdwMvtll9LDAtPZ8GjK6mJo+hm5nlUMMY+kBJcyqWJ0XEpA28ZruIeC49fx7Yrpo3cqCbmeVQQ6CviIjhed8nIkJSVLOvh1zMzHKo4xh6e/4iaYf0PjsAy6t5kQPdzKxGdT4o2p6bgJPS85OAX1XzIg+5mJnlUK/z0CVdDYwkG2t/BpgInA9cJ+lk4CngY9W05UA3M8uhXoEeEZ/oYNPhtbblQDczy6GIV4o60M3MaiTJ9xQ1MysL99DNzErCgW5mVhIOdDOzknCgm5mVgO9YZE1jypQpHHPMMSxfvpx3vvOdAHz/+9/nIx/5CGvWrOFPf/oTY8eOZdWqVQ2u1PJYs2YN48ePZ82aNbS0tHDooYcyduzYRpfVdIoY6MU778YaburUqRx11PrTM8+cOZNhw4ax7777snjxYiZMmNCg6qyrNtpoIy688EKmTJnC5MmTmT17NosWLWp0WU2nmy/9z8WBbv9g1qxZvPji+tMzz5w5k5aWFgDuv/9+hgwZ0ojSrA4ksdlmmwGwdu3aN79Xq00RA91DLlazcePGce211za6DOuClpYWvvCFL7Bs2TJGjx7N0KFDG11S0/GQS5OQtJWk0xtdRxF94xvfYO3atVx55ZWNLsW6oG/fvkyePJnp06fz2GOPsWTJkkaX1FR6YLbFXEoZ6JL6dbZcha0AB3obJ510EscccwxjxoxpdClWJwMGDGC//fZj9uzZjS6l6dTrJtH1VPghF0mfAc4CAngY+DZwOTAQeAEYGxFPS5oK/B14N3CvpK3bLF8CXAIMAl4DPh8Rj0naDrgU2C295WnAl4HdJc0HZkbE2T3yYQvsyCOP5Gtf+xqHHnoor7/+eqPLsS546aWX6NevHwMGDGD16tXMnTuXT3yiown/rCNFHHIpdKBL2gf4FnBQRKxIIT0NmBYR0ySNA37EWzdQHZL2bUkBX7l8B3BqRDwh6UDgx8Bh6fW/i4jjJPUFBpDdYXtYROzXQV2nAKd01+dutKuuuoqRI0cycOBAli5dysSJE5kwYQKbbLIJM2fOBLIDo6eddlqDK7U8Vq5cyfnnn8+6detYt24dI0eOZMSIEY0uq+kUMdAVUdWt6hpC0hnA9hHxzYp1K4AdIuINSRsBz0XEwBTgd0bEtLTfm8uSBpD15h+vaH6TiPhnSS8AQyJidcV77ALMiIhhVdRY3B+g5XLnnXc2ugSrs1GjRs3tyn0929piiy1i+PDqmrvzzjvr+t6dKXQPPYdXO1juA7zUUY/bzKxWReyhF/2g6G+BEyRtA5CGXH4PnJi2jwFmbaiRiPgbsETSCakdSdo3bb6DbNwcSX0lbQm8DGxezw9iZuXis1xqFBELge8Cv5P0B+BC4AxgrKSHgU8D46tsbgxwcmpnIXBsWj8eGCXpEWAuMDQiVpIdSF0g6YL6fSIzK4siBnrhh1zSmPi0NqsPa2e/z25geQmw/vXs2fq/8Fa4V67/ZO3VmllvUcQhl8IHuplZ0Xi2RTOzEnGgm5mVhG8SbWZWEu6hm5mVgMfQzcxKxIFuZlYSDnQzs5JwoJuZlYQD3cysBIp6ULR4J1KamTWBes7lIumrkham+aOulrRpnpoc6GZmOdQr0CUNJrtL2vB0D4a+vDWjbE085GJmViNJ9b5StB+wmaQ3gP7As3kacQ/dzCyHGnroAyXNqXisd/vKiFgG/AB4GngOWBURt+epyT10M7McajgouqKzW9BJejvZFN67Ai8B0yV9KiKuqLUm99DNzHKo40HRDwJLIuKFiHgDuAE4KE9N7qGbmeVQx9MWnwbeJ6k/8DpwODAnT0MOdDOzGtXzPPSIeEDS9cA8YC3wEDApT1sOdDOzHOp5YVFETAQmdrUdB7qZWQ5FvFLUgW5mloMD3cysJBzoZmYlUNTJuRzoZmY5+CbRZmYl4R66mVlJONDNzErAY+hmZiXiQDczKwkHuplZSTRVoEv6byA62h4RX+6WiszMmkBTBTo5p280Myu7pjsoGhHTKpcl9Y+I17q/JDOz4itioG/wUidJIyQtAh5Ly/tK+nG3V2ZmVmB9+vSp6tGjNVWxzw+BI4GVABHxB+CQ7izKzKzo6ngLurqp6iyXiFjaprCW7inHzKz4mm4MvcJSSQcBIWkjYDzwaPeWZWZWbEUM9GqGXE4FvggMBp4F9kvLZma9VlMOuUTECmBMD9RiZtY0mrKHLmk3STdLekHSckm/krRbTxRnZlZUReyhVzPkchVwHbADsCMwHbi6O4syMyuyasO8iIHePyJ+HhFr0+MKYNPuLszMrMiKGOidzeWydXr6G0lfB64hm9vl48Cve6A2M7PCKuIYemcHReeSBXhr1V+o2BbAhO4qysys6JrqnqIRsWtPFmJm1iya+cIiJA0DhlIxdh4RP+uuoszMiq4pA13SRGAkWaD/GvgQcA/gQDezXquIgV7NINDxwOHA8xExFtgX2LJbqzIzK7imOsulwusRsU7SWklbAMuBnbq5LjOzQmvWHvocSVsBPyE782UecF+3VmVmVmD1vrBI0laSrpf0mKRHJY3IU1c1c7mcnp5eKulWYIuIeDjPm5mZlUWde+gXAbdGxPGSNgb652mkswuL9u9sW0TMy/OGZmZlUK9Al7Ql2U2DPgsQEWuANXna6qyH/p+dbAvgsDxvWDYHHHAAc+b4ftpmvU0NgT5QUmVITIqISRXLuwIvAD+VtC/Z0Pb4iHi11po6u7BoVK2NmZn1FjUE+oqIGN7J9n7A/sAZEfGApIuArwPfrrWm4l27amZWcJLqeZPoZ4BnIuKBtHw9WcDXzIFuZpZDvc5yiYjnyW71uXdadTiwKE9NVV36b2Zm66vzWS5nAFemM1z+DIzN00g1l/6L7BZ0u0XEeZJ2BraPiNl53tDMrAzqGegRMR/obJy9KtUMufwYGAF8Ii2/DFzS1Tc2M2tWRb1jUTVDLgdGxP6SHgKIiL+mPwvMzHqtIl76X02gvyGpL9m550gaBKzr1qrMzAquWQP9R8CNwLaSvks2++K3urUqM7OCa8pAj4grJc0lO5VGwOiIeLTbKzMzK7CmDPR0VstrwM2V6yLi6e4szMysqJr5FnS38NbNojclm3fgcWCfbqzLzKzQmuom0a0i4p2Vy2kWxtM72N3MrFdo1h76eiJinqQDu6MYM7Nm0ZSBLunMisU+ZJPGPNttFZmZFVwzj6FvXvF8LdmY+i+6pxwzs+bQdIGeLijaPCLO6qF6zMyaQlMFuqR+EbFW0vt7siAzs2bQVIEOzCYbL58v6SZgOvDmLZEi4oZurs3MrLCaLdBbbQqsJLuHaOv56AE40M2sV2rGg6LbpjNcFvBWkLeKbq3KzKzgmi3Q+wIDWD/IWznQzaxXa7YrRZ+LiPN6rBIzsybSbD304lVrZlYAzTiGfniPVWFm1mSaKtAj4sWeLMTMrJk0VaCbmVnHHOhmZiXhQDczK4FmPChqZmYdcKCbmZWEA93MrCQc6GZmJSCp6S79NzOzDriHbmZWEkUM9OL9zWBm1gRaT13c0KOG9vpKekjSjLw1uYduZlajbjoPfTzwKLBF3gbcQzczy6GePXRJQ4APA5O7UpN76GZmOdTQQx8oaU7F8qSImNRmnx8CXwM270pNDnQzsxxqCPQVETG8k3aOAZZHxFxJI7tSkwPdzCyHOo6hvx/4qKSjgU2BLSRdERGfqrUhj6GbmdWo2vHzakI/IiZExJCI2AU4EfhtnjAH99DNzHLxlaJmZiXRHRcWRcRdwF15X+9ANzPLoYhXijrQzcxq5BtcmJmViAPdzKwkHOhmZiXhQDczK4kiBnrxTqS0wrn11lvZe++92WOPPTj//PMbXY7Vgb/TrqnnhUX15EC3TrW0tPDFL36R3/zmNyxatIirr76aRYsWNbos6wJ/p/XhQLemM3v2bPbYYw922203Nt54Y0488UR+9atfNbos6wJ/p/XRp0+fqh49WlOPvps1nWXLlrHTTju9uTxkyBCWLVvWwIqsq/yd1od76HUg6QRJj0q6U9JwST9K60dKOqhiv9GShlYsnyfpg42o2czKpahj6E1zlouyn4yAk4HPR8Q9aVPrxPEjgVeA36fl0cAMYBFARHynx4otkcGDB7N06dI3l5955hkGDx7cwIqsq/yd1kevO8tF0pmSFqTHVySdL+mLFdvPkXRWen62pAclPSzp3LRuF0mPS/oZsAD4NvABYIqkC1KvfIakXYBTga9Kmi/pUOCjwAVpeXdJUyUdn9p9UtK5kuZJekTSO9L6QZJmSlooabKkpyQN7M6fUdG95z3v4YknnmDJkiWsWbOGa665ho9+9KONLsu6wN9pffSqHrqkA4CxwIFkPesHgE+R3WrpkrTbx4AjJR0B7Am8N+17k6RDgKfT+pMi4v7U7ijgrIiYo3R3j4h4UtKlwCsR8YO0303AjIi4Pi23LXFFROwv6XTgLOBzwESyuYi/J+kosr8G2vtspwCnAOy88875f0hNoF+/flx88cUceeSRtLS0MG7cOPbZZ59Gl2Vd4O+0PorYQ+/OIZcPADdGxKsAkm4ADga2lbQjMAj4a0QslTQeOAJ4KL12AFmQPw081RrmdXZD+ncu8K8VNR8HEBG3Svprey9M9wOcBDB8+PDohtoK5eijj+boo49udBlWR/5Ou663BXpHpgPHA9sD16Z1Ar4XEZdV7piGUl7tpjpWp39baKJjCWbWeEWdbbE7x9BnAaMl9Zf0NrKe7yyyED+RLNSnp31vA8ZJGgAgabCkbWt8v5dZ/47ZbZercS/ZMBBpGOjtNb7ezHqJIo6hd1ugR8Q8YCowm2z8fHJEPBQRC8mCdllEPJf2vR24CrhP0iPA9dQexjcDx6WDoAcD1wBnS3pI0u5VtnEucISkBcAJwPNkvxjMzNZTxEDv1qGGiLgQuLCd9e9sZ91FwEXtNDOszX4jK57fRbpdU0QsBt7V5rVDK55/tuJ1u1Q8n0N2yiPAKuDIiFgraQTwnohYjZlZG0UccvHY8fp2Bq6T1AdYA3y+wfWYWQFJ8k2iiy4ingDe3eg6zKz43EM3MysJB7qZWUk40M3MSsKBbmZWAkW9sMiBbmaWgwPdzKwkHOhmZiXhQDczKwkHuplZCRT1StHiVWRm1gTqNTmXpJ2U3SN5Ubpb2vi8NbmHbmaWQx2HXNYC/xYR8yRtDsyVNDMiFtXakAPdzCyHegV6mka8dSrxlyU9Cgwm3eC+Fg50M7Ma1Xhh0UBJcyqWJ6XbWLbX7i5kEwQ+kKcuB7qZWQ41BPqKiBheRXsDgF8AX4mIv+WpyYFuZpZDPU9blLQRWZhfGRE3bGj/jjjQzcxyqFegK2toCvBoustbbj5t0cwshzreU/T9wKeBw9I9kedLOjpPTe6hm5nVqJ6zLUbEPUBdGnOgm5nlUMQrRR3oZmY5eC4XM7OScKCbmZWA71hkZlYiDnQzs5JwoJuZlYQD3cysJBzoZmYl4IOiZmYl4kA3MysJB7qZWUn40n8zsxLwGLqZWYk40M3MSsKBbmZWEg50M7OScKCbmZWAD4qamZWIA93MrCQc6GZmJeFANzMrAUm+UtTMrCzcQzczKwkHuplZSTjQzcxKwOehm5mViAPdzKwkHOhmZiXhQDczK4kiBnrxzow3Myu41oOi1TyqbO8oSY9L+qOkr+etyz10M7Mc6nWlqKS+wCXAvwDPAA9KuikiFtVcU10qMjPrZerYQ38v8MeI+HNErAGuAY7NU5N76F00d+7cFZKeanQdPWQgsKLRRVjd9Kbv85/q2djcuXNvkzSwyt03lTSnYnlSREyqWB4MLK1YfgY4ME9dDvQuiohBja6hp0iaExHDG12H1Ye/z/wi4qhG19AeD7mYmTXWMmCniuUhaV3NHOhmZo31ILCnpF0lbQycCNyUpyEPuVgtJm14F2si/j4LICLWSvoScBvQF7g8IhbmaUsRUdfizMysMTzkYmZWEg50M7OScKBbt5K0laTTG12HZSSdIOlRSXdKGi7pR2n9SEkHVew3WtLQiuXzJH2wETVb9TyGbp2S1C8i1na0XMXrdwFmRMSwbijPqqTskkUBvwb+IyLuabP9HOCViPhBWp5K9r1d38OlWhc40HsRSZ8BzgICeBj4NnA52RWDLwBjI+Lp9D/z34F3A/cCW7dZviQ9BgGvAZ+PiMckbQdcCuyW3vI04MtklzE/DsyMiLO7/5OWg6QzgXFpcTKwPbA0Ii5J288hhbCks4GPAZsAN0bExPTL9DbgAeAA4DrgbLJznG8CbiH77+FLwP1AC9l/B+OBG4FV6fF/yP5bmRER10t6EpgGfATYCDghff+DgKuAHYH7yOYmOSAiesvVqI0XEX70ggewD7AYGJiWtwZuBk5Ky+OAX6bnU4EZQN8Olu8A9kzPDwR+m55fC3wlPe8LbAnsAixo9OdvtgdZAD8CvA0YACwk+4X6u4p9FpFdkHIE2SmIIhtGnQEckn7264D3VbzmLmB4ej6SLKQBzgHOqthvKnB8e8vAk8AZ6fnpwOT0/GJgQnp+FFnHYWCjf5a96eHz0HuPw4DpkXpLEfGipBHAv6btPwe+X7H/9IhoabssaQBwEDC9YuKhTSre4zOp/RZglaS3d8unKb8PkPW0XwWQdANwMLCtpB3J/jr6a0QslTSeLNQfSq8dAOwJPA08FRH3d0N9N6R/5/LWf0MfAI4DiIhbJf21G97XOuFAt4682sFyH+CliNivh+uxzHTgeLLhl2vTOgHfi4jLKndMQy5tv8d6WZ3+bcE5Uhg+y6X3+C1wgqRtACRtDfye7DJjgDHArA01EhF/A5ZIOiG1I0n7ps13kI2bI6mvpC2Bl4HN6/lBeolZwGhJ/SW9jaznO4ssxE8kC/Xpad/bgHHpryckDZa0bY3v1/Z7yvO93Us2jo+kIwD/ddbDHOi9RGSXEn8X+J2kPwAXAmcAYyU9DHya7GBYNcYAJ6d2FvLW3M3jgVGSHiH7U3xoRKwE7pW0QNIF9ftE5RYR88jGrWeTHdScHBEPpe9xc2BZRDyX9r2d7GDkfelnfz21h/HNwHGS5ks6mGxO7rMlPSRp9yrbOBc4QtIC4ATgebJfDNZDfJaLmdWFpE2AlsjmJhkB/I+H5nqWx77MrF52Bq6T1AdYA3y+wfX0Ou6hm5mVhMfQzcxKwoFuZlYSDnQzs5JwoFvDSWpJp8stkDRdUv8utDVV0vHp+eTKGQPb2Xe9GQZreI8n27vje0fr2+zzSo3vdY6ks2qt0XonB7oVwesRsV9kMzKuAU6t3Cgp19lYEfG5iFjUyS4jyaYxMCsFB7oVzSxgj9R7niXpJmBRuvL0AkkPSnpY0hfgzStVL5b0uKT/Bd68QlLSXZKGp+dHSZon6Q+S7kiXxZ8KfLX1YhpJgyT9Ir3Hg5Len167jaTbJS2UNJnsUvtOSfqlpLnpNae02fZfaf0daYZCJO0u6db0mlmS3lGPH6b1Lj4P3Qoj9cQ/BNyaVu0PDIuIJSkUV0XEe9IFLPdKup1sBsK9gaHAdmQzEF7ept1BwE+AQ1JbW6fJyS5l/TnArwL+KyLukbQz2SX1/wxMBO6JiPMkfRg4uYqPMy69x2bAg5J+ka6afRswJyK+Kuk7qe0vkc2WeGpEPCHpQODHZJOdmVXNgW5FsJmk+en5LGAK2VDI7IhYktYfAbyrdXycbGrePcmmib06ze74rKTfttP++4C7W9uKiBc7qOODwNCKWSS3SPOjHEKaUTAibqlyFsEvSzouPd8p1bqSbDrb1km1rgBu2MAMlmZVc6BbEbze9hLxFGyVMwWKbA7u29rsd3Qd6+hDNnf439uppWqSRpL9chgREa9JugvYtIPdA89gaXXiMXRrFrcBp0naCEDSXmkWwruBj6cx9h2AUe289n7gEEm7ptdunda3nVHwdrIJy0j7tQbs3cAn07oPseFZBLckm6v8tTQW/r6KbX3IZkoktXnPBmawNKuaA92axWSy8fF5aTa/y8j+wrwReCJt+xnZrc/WExEvAKeQDW/8gbeGPNrOMPhlYHg66LqIt862OZfsF8JCsqGXpzdQ661AP0mPAueT/UJp9Srw3vQZDgPOS+s7msHSrGqey8XMrCTcQzczKwkHuplZSTjQzcxKwoFuZlYSDnQzs5JwoJuZlYQD3cysJP4/sXV/6t99w68AAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# G<PERSON>\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Gson') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Gson')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=20, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10285, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1986, 204)\n", "====================\n", "Total test:  (17, 204)\n", "overfitting in test (0, 204)\n", "correct in test (17, 204)\n", "[0]\tvalidation_0-mae:0.48503\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.44640\n", "[2]\tvalidation_0-mae:0.44180\n", "[3]\tvalidation_0-mae:0.44234\n", "[4]\tvalidation_0-mae:0.42004\n", "[5]\tvalidation_0-mae:0.42380\n", "[6]\tvalidation_0-mae:0.41708\n", "[7]\tvalidation_0-mae:0.40906\n", "[8]\tvalidation_0-mae:0.40020\n", "[9]\tvalidation_0-mae:0.39620\n", "[10]\tvalidation_0-mae:0.38870\n", "[11]\tvalidation_0-mae:0.38082\n", "[12]\tvalidation_0-mae:0.38156\n", "[13]\tvalidation_0-mae:0.38444\n", "[14]\tvalidation_0-mae:0.37755\n", "[15]\tvalidation_0-mae:0.36835\n", "[16]\tvalidation_0-mae:0.35621\n", "[17]\tvalidation_0-mae:0.35894\n", "[18]\tvalidation_0-mae:0.35128\n", "[19]\tvalidation_0-mae:0.35227\n", "[20]\tvalidation_0-mae:0.34362\n", "[21]\tvalidation_0-mae:0.33607\n", "[22]\tvalidation_0-mae:0.33701\n", "[23]\tvalidation_0-mae:0.33239\n", "[24]\tvalidation_0-mae:0.33016\n", "[25]\tvalidation_0-mae:0.32458\n", "[26]\tvalidation_0-mae:0.31786\n", "[27]\tvalidation_0-mae:0.31218\n", "[28]\tvalidation_0-mae:0.31180\n", "[29]\tvalidation_0-mae:0.31344\n", "[30]\tvalidation_0-mae:0.31711\n", "[31]\tvalidation_0-mae:0.31590\n", "[32]\tvalidation_0-mae:0.30606\n", "[33]\tvalidation_0-mae:0.30369\n", "[34]\tvalidation_0-mae:0.29450\n", "[35]\tvalidation_0-mae:0.29068\n", "[36]\tvalidation_0-mae:0.29186\n", "[37]\tvalidation_0-mae:0.28301\n", "[38]\tvalidation_0-mae:0.28066\n", "[39]\tvalidation_0-mae:0.27795\n", "[40]\tvalidation_0-mae:0.28273\n", "[41]\tvalidation_0-mae:0.27714\n", "[42]\tvalidation_0-mae:0.27762\n", "[43]\tvalidation_0-mae:0.26927\n", "[44]\tvalidation_0-mae:0.26859\n", "[45]\tvalidation_0-mae:0.26665\n", "[46]\tvalidation_0-mae:0.26584\n", "[47]\tvalidation_0-mae:0.26065\n", "[48]\tvalidation_0-mae:0.26169\n", "[49]\tvalidation_0-mae:0.25653\n", "[50]\tvalidation_0-mae:0.25761\n", "[51]\tvalidation_0-mae:0.25622\n", "[52]\tvalidation_0-mae:0.25432\n", "[53]\tvalidation_0-mae:0.25344\n", "[54]\tvalidation_0-mae:0.25240\n", "[55]\tvalidation_0-mae:0.25037\n", "[56]\tvalidation_0-mae:0.24627\n", "[57]\tvalidation_0-mae:0.24323\n", "[58]\tvalidation_0-mae:0.24091\n", "[59]\tvalidation_0-mae:0.23953\n", "[60]\tvalidation_0-mae:0.24007\n", "[61]\tvalidation_0-mae:0.23879\n", "[62]\tvalidation_0-mae:0.23628\n", "[63]\tvalidation_0-mae:0.23970\n", "[64]\tvalidation_0-mae:0.23989\n", "[65]\tvalidation_0-mae:0.23897\n", "[66]\tvalidation_0-mae:0.24061\n", "[67]\tvalidation_0-mae:0.23799\n", "[68]\tvalidation_0-mae:0.23689\n", "[69]\tvalidation_0-mae:0.23830\n", "[70]\tvalidation_0-mae:0.23543\n", "[71]\tvalidation_0-mae:0.23521\n", "[72]\tvalidation_0-mae:0.23337\n", "[73]\tvalidation_0-mae:0.23339\n", "[74]\tvalidation_0-mae:0.23543\n", "[75]\tvalidation_0-mae:0.23536\n", "[76]\tvalidation_0-mae:0.23098\n", "[77]\tvalidation_0-mae:0.23051\n", "[78]\tvalidation_0-mae:0.22848\n", "[79]\tvalidation_0-mae:0.22852\n", "[80]\tvalidation_0-mae:0.22862\n", "[81]\tvalidation_0-mae:0.22812\n", "[82]\tvalidation_0-mae:0.23101\n", "[83]\tvalidation_0-mae:0.22805\n", "[84]\tvalidation_0-mae:0.22570\n", "[85]\tvalidation_0-mae:0.22583\n", "[86]\tvalidation_0-mae:0.23042\n", "[87]\tvalidation_0-mae:0.23032\n", "[88]\tvalidation_0-mae:0.22962\n", "[89]\tvalidation_0-mae:0.22831\n", "[90]\tvalidation_0-mae:0.22772\n", "[91]\tvalidation_0-mae:0.22823\n", "[92]\tvalidation_0-mae:0.22929\n", "[93]\tvalidation_0-mae:0.22929\n", "[94]\tvalidation_0-mae:0.22821\n", "[95]\tvalidation_0-mae:0.22706\n", "[96]\tvalidation_0-mae:0.22067\n", "[97]\tvalidation_0-mae:0.21972\n", "[98]\tvalidation_0-mae:0.21894\n", "[99]\tvalidation_0-mae:0.21824\n", "f1 score: 0.0\n", "acc score: 0.8235294117647058\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Csv\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Csv') ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Csv')]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10285, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1986, 204)\n", "====================\n", "Total test:  (17, 204)\n", "overfitting in test (0, 204)\n", "correct in test (17, 204)\n", "[0]\tvalidation_0-mae:0.39491\n", "Will train until validation_0-mae hasn't improved in 20 rounds.\n", "[1]\tvalidation_0-mae:0.33645\n", "[2]\tvalidation_0-mae:0.30014\n", "[3]\tvalidation_0-mae:0.25047\n", "[4]\tvalidation_0-mae:0.23385\n", "[5]\tvalidation_0-mae:0.22071\n", "[6]\tvalidation_0-mae:0.19577\n", "[7]\tvalidation_0-mae:0.17088\n", "[8]\tvalidation_0-mae:0.14643\n", "[9]\tvalidation_0-mae:0.14133\n", "[10]\tvalidation_0-mae:0.14305\n", "[11]\tvalidation_0-mae:0.14313\n", "[12]\tvalidation_0-mae:0.14267\n", "[13]\tvalidation_0-mae:0.13797\n", "[14]\tvalidation_0-mae:0.14045\n", "[15]\tvalidation_0-mae:0.13627\n", "[16]\tvalidation_0-mae:0.13198\n", "[17]\tvalidation_0-mae:0.12820\n", "[18]\tvalidation_0-mae:0.12638\n", "[19]\tvalidation_0-mae:0.12556\n", "[20]\tvalidation_0-mae:0.12245\n", "[21]\tvalidation_0-mae:0.11211\n", "[22]\tvalidation_0-mae:0.11315\n", "[23]\tvalidation_0-mae:0.11284\n", "[24]\tvalidation_0-mae:0.11258\n", "[25]\tvalidation_0-mae:0.11352\n", "[26]\tvalidation_0-mae:0.11381\n", "[27]\tvalidation_0-mae:0.11398\n", "[28]\tvalidation_0-mae:0.10859\n", "[29]\tvalidation_0-mae:0.10828\n", "[30]\tvalidation_0-mae:0.09742\n", "[31]\tvalidation_0-mae:0.09669\n", "[32]\tvalidation_0-mae:0.09305\n", "[33]\tvalidation_0-mae:0.09209\n", "[34]\tvalidation_0-mae:0.08923\n", "[35]\tvalidation_0-mae:0.08940\n", "[36]\tvalidation_0-mae:0.09059\n", "[37]\tvalidation_0-mae:0.09063\n", "[38]\tvalidation_0-mae:0.09140\n", "[39]\tvalidation_0-mae:0.08895\n", "[40]\tvalidation_0-mae:0.08959\n", "[41]\tvalidation_0-mae:0.08990\n", "[42]\tvalidation_0-mae:0.08943\n", "[43]\tvalidation_0-mae:0.08870\n", "[44]\tvalidation_0-mae:0.08877\n", "[45]\tvalidation_0-mae:0.08099\n", "[46]\tvalidation_0-mae:0.07970\n", "[47]\tvalidation_0-mae:0.08147\n", "[48]\tvalidation_0-mae:0.08084\n", "[49]\tvalidation_0-mae:0.08064\n", "[50]\tvalidation_0-mae:0.07730\n", "[51]\tvalidation_0-mae:0.07667\n", "[52]\tvalidation_0-mae:0.07651\n", "[53]\tvalidation_0-mae:0.07638\n", "[54]\tvalidation_0-mae:0.07643\n", "[55]\tvalidation_0-mae:0.07643\n", "[56]\tvalidation_0-mae:0.07514\n", "[57]\tvalidation_0-mae:0.07475\n", "[58]\tvalidation_0-mae:0.07231\n", "[59]\tvalidation_0-mae:0.07129\n", "[60]\tvalidation_0-mae:0.07136\n", "[61]\tvalidation_0-mae:0.07192\n", "[62]\tvalidation_0-mae:0.06977\n", "[63]\tvalidation_0-mae:0.06955\n", "[64]\tvalidation_0-mae:0.07029\n", "[65]\tvalidation_0-mae:0.07022\n", "[66]\tvalidation_0-mae:0.06831\n", "[67]\tvalidation_0-mae:0.06200\n", "[68]\tvalidation_0-mae:0.06159\n", "[69]\tvalidation_0-mae:0.06142\n", "[70]\tvalidation_0-mae:0.06023\n", "[71]\tvalidation_0-mae:0.05958\n", "[72]\tvalidation_0-mae:0.05525\n", "[73]\tvalidation_0-mae:0.05559\n", "[74]\tvalidation_0-mae:0.05570\n", "[75]\tvalidation_0-mae:0.05455\n", "[76]\tvalidation_0-mae:0.05418\n", "[77]\tvalidation_0-mae:0.05394\n", "[78]\tvalidation_0-mae:0.05391\n", "[79]\tvalidation_0-mae:0.05373\n", "[80]\tvalidation_0-mae:0.05393\n", "[81]\tvalidation_0-mae:0.05420\n", "[82]\tvalidation_0-mae:0.05377\n", "[83]\tvalidation_0-mae:0.05387\n", "[84]\tvalidation_0-mae:0.05355\n", "[85]\tvalidation_0-mae:0.05335\n", "[86]\tvalidation_0-mae:0.05019\n", "[87]\tvalidation_0-mae:0.05013\n", "[88]\tvalidation_0-mae:0.05045\n", "[89]\tvalidation_0-mae:0.05024\n", "[90]\tvalidation_0-mae:0.04974\n", "[91]\tvalidation_0-mae:0.04988\n", "[92]\tvalidation_0-mae:0.04980\n", "[93]\tvalidation_0-mae:0.05022\n", "[94]\tvalidation_0-mae:0.05018\n", "[95]\tvalidation_0-mae:0.04986\n", "[96]\tvalidation_0-mae:0.04924\n", "[97]\tvalidation_0-mae:0.05062\n", "[98]\tvalidation_0-mae:0.04874\n", "[99]\tvalidation_0-mae:0.04911\n", "f1 score: 1.0\n", "acc score: 1.0\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 due to no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Spring\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Bears_180|Bears_181|Bears_182|Bears_183|Bears_184|Bears_185|Bears_84|Bears_85|Bears_86|Bears_87|Bears_88|Bears_89|Bears_90|Bears_91|Bears_92|Bears_93|Bears_94|Bears_95|Bears_96|Bears_97')   ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Bears_180|Bears_181|Bears_182|Bears_183|Bears_184|Bears_185|Bears_84|Bears_85|Bears_86|Bears_87|Bears_88|Bears_89|Bears_90|Bears_91|Bears_92|Bears_93|Bears_94|Bears_95|Bears_96|Bears_97')  ]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=20, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10284, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1985, 204)\n", "====================\n", "Total test:  (18, 204)\n", "overfitting in test (0, 204)\n", "correct in test (18, 204)\n", "[0]\tvalidation_0-mae:0.46051\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.42211\n", "[2]\tvalidation_0-mae:0.41838\n", "[3]\tvalidation_0-mae:0.40995\n", "[4]\tvalidation_0-mae:0.39238\n", "[5]\tvalidation_0-mae:0.35801\n", "[6]\tvalidation_0-mae:0.35203\n", "[7]\tvalidation_0-mae:0.34876\n", "[8]\tvalidation_0-mae:0.35275\n", "[9]\tvalidation_0-mae:0.35339\n", "[10]\tvalidation_0-mae:0.33484\n", "[11]\tvalidation_0-mae:0.33754\n", "[12]\tvalidation_0-mae:0.34044\n", "[13]\tvalidation_0-mae:0.34176\n", "[14]\tvalidation_0-mae:0.32179\n", "[15]\tvalidation_0-mae:0.32094\n", "[16]\tvalidation_0-mae:0.31803\n", "[17]\tvalidation_0-mae:0.31865\n", "[18]\tvalidation_0-mae:0.31672\n", "[19]\tvalidation_0-mae:0.29712\n", "[20]\tvalidation_0-mae:0.30019\n", "[21]\tvalidation_0-mae:0.29489\n", "[22]\tvalidation_0-mae:0.29204\n", "[23]\tvalidation_0-mae:0.29074\n", "[24]\tvalidation_0-mae:0.28905\n", "[25]\tvalidation_0-mae:0.29187\n", "[26]\tvalidation_0-mae:0.28962\n", "[27]\tvalidation_0-mae:0.28994\n", "[28]\tvalidation_0-mae:0.28597\n", "[29]\tvalidation_0-mae:0.28198\n", "[30]\tvalidation_0-mae:0.27649\n", "[31]\tvalidation_0-mae:0.26791\n", "[32]\tvalidation_0-mae:0.26280\n", "[33]\tvalidation_0-mae:0.25848\n", "[34]\tvalidation_0-mae:0.25607\n", "[35]\tvalidation_0-mae:0.26176\n", "[36]\tvalidation_0-mae:0.25281\n", "[37]\tvalidation_0-mae:0.25184\n", "[38]\tvalidation_0-mae:0.24852\n", "[39]\tvalidation_0-mae:0.23978\n", "[40]\tvalidation_0-mae:0.24191\n", "[41]\tvalidation_0-mae:0.24101\n", "[42]\tvalidation_0-mae:0.24085\n", "[43]\tvalidation_0-mae:0.23966\n", "[44]\tvalidation_0-mae:0.23845\n", "[45]\tvalidation_0-mae:0.23743\n", "[46]\tvalidation_0-mae:0.23649\n", "[47]\tvalidation_0-mae:0.23664\n", "[48]\tvalidation_0-mae:0.24048\n", "[49]\tvalidation_0-mae:0.23857\n", "[50]\tvalidation_0-mae:0.23487\n", "[51]\tvalidation_0-mae:0.23328\n", "[52]\tvalidation_0-mae:0.23440\n", "[53]\tvalidation_0-mae:0.23664\n", "[54]\tvalidation_0-mae:0.23378\n", "[55]\tvalidation_0-mae:0.23289\n", "[56]\tvalidation_0-mae:0.23179\n", "[57]\tvalidation_0-mae:0.23116\n", "[58]\tvalidation_0-mae:0.23190\n", "[59]\tvalidation_0-mae:0.23222\n", "[60]\tvalidation_0-mae:0.23786\n", "[61]\tvalidation_0-mae:0.24216\n", "[62]\tvalidation_0-mae:0.24241\n", "[63]\tvalidation_0-mae:0.24333\n", "[64]\tvalidation_0-mae:0.23951\n", "[65]\tvalidation_0-mae:0.23792\n", "[66]\tvalidation_0-mae:0.23415\n", "[67]\tvalidation_0-mae:0.23378\n", "[68]\tvalidation_0-mae:0.23468\n", "[69]\tvalidation_0-mae:0.23429\n", "[70]\tvalidation_0-mae:0.23171\n", "[71]\tvalidation_0-mae:0.23191\n", "[72]\tvalidation_0-mae:0.22764\n", "[73]\tvalidation_0-mae:0.22762\n", "[74]\tvalidation_0-mae:0.22987\n", "[75]\tvalidation_0-mae:0.22571\n", "[76]\tvalidation_0-mae:0.22598\n", "[77]\tvalidation_0-mae:0.23023\n", "[78]\tvalidation_0-mae:0.22986\n", "[79]\tvalidation_0-mae:0.23019\n", "[80]\tvalidation_0-mae:0.23278\n", "[81]\tvalidation_0-mae:0.23249\n", "[82]\tvalidation_0-mae:0.23216\n", "[83]\tvalidation_0-mae:0.23151\n", "[84]\tvalidation_0-mae:0.23042\n", "[85]\tvalidation_0-mae:0.22870\n", "[86]\tvalidation_0-mae:0.22993\n", "[87]\tvalidation_0-mae:0.22993\n", "[88]\tvalidation_0-mae:0.22927\n", "[89]\tvalidation_0-mae:0.22469\n", "[90]\tvalidation_0-mae:0.22307\n", "[91]\tvalidation_0-mae:0.22481\n", "[92]\tvalidation_0-mae:0.22308\n", "[93]\tvalidation_0-mae:0.22199\n", "[94]\tvalidation_0-mae:0.22214\n", "[95]\tvalidation_0-mae:0.22017\n", "[96]\tvalidation_0-mae:0.21975\n", "[97]\tvalidation_0-mae:0.21808\n", "[98]\tvalidation_0-mae:0.21820\n", "[99]\tvalidation_0-mae:0.21778\n", "f1 score: 0.0\n", "acc score: 0.8888888888888888\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# jacksonxml\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Bears_0_|Bears_1_|Bears_2_|Bears_3_|Bears_4_|Bears_5_|Bears_6_|Bears_7_|Bears_8_|Bears_9_|Bears_10_|Bears_11_|Bears_12_|Bears_13_|Bears_14_|Bears_15_|Bears_16_|Bears_17_|Bears_18_|JacksonXml')   ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Bears_0_|Bears_1_|Bears_2_|Bears_3_|Bears_4_|Bears_5_|Bears_6_|Bears_7_|Bears_8_|Bears_9_|Bears_10_|Bears_11_|Bears_12_|Bears_13_|Bears_14_|Bears_15_|Bears_16_|Bears_17_|Bears_18_|JacksonXml') ]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10252, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1953, 204)\n", "====================\n", "Total test:  (50, 204)\n", "overfitting in test (0, 204)\n", "correct in test (50, 204)\n", "[0]\tvalidation_0-mae:0.42549\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.37195\n", "[2]\tvalidation_0-mae:0.33263\n", "[3]\tvalidation_0-mae:0.30580\n", "[4]\tvalidation_0-mae:0.28065\n", "[5]\tvalidation_0-mae:0.25677\n", "[6]\tvalidation_0-mae:0.23993\n", "[7]\tvalidation_0-mae:0.23300\n", "[8]\tvalidation_0-mae:0.22645\n", "[9]\tvalidation_0-mae:0.21557\n", "[10]\tvalidation_0-mae:0.20788\n", "[11]\tvalidation_0-mae:0.20428\n", "[12]\tvalidation_0-mae:0.20955\n", "[13]\tvalidation_0-mae:0.20178\n", "[14]\tvalidation_0-mae:0.20185\n", "[15]\tvalidation_0-mae:0.19475\n", "[16]\tvalidation_0-mae:0.19453\n", "[17]\tvalidation_0-mae:0.19082\n", "[18]\tvalidation_0-mae:0.18810\n", "[19]\tvalidation_0-mae:0.18001\n", "[20]\tvalidation_0-mae:0.18007\n", "[21]\tvalidation_0-mae:0.17665\n", "[22]\tvalidation_0-mae:0.17598\n", "[23]\tvalidation_0-mae:0.16639\n", "[24]\tvalidation_0-mae:0.16780\n", "[25]\tvalidation_0-mae:0.16700\n", "[26]\tvalidation_0-mae:0.16768\n", "[27]\tvalidation_0-mae:0.16683\n", "[28]\tvalidation_0-mae:0.16496\n", "[29]\tvalidation_0-mae:0.16614\n", "[30]\tvalidation_0-mae:0.16782\n", "[31]\tvalidation_0-mae:0.16715\n", "[32]\tvalidation_0-mae:0.16529\n", "[33]\tvalidation_0-mae:0.16325\n", "[34]\tvalidation_0-mae:0.16184\n", "[35]\tvalidation_0-mae:0.15999\n", "[36]\tvalidation_0-mae:0.16104\n", "[37]\tvalidation_0-mae:0.15779\n", "[38]\tvalidation_0-mae:0.15459\n", "[39]\tvalidation_0-mae:0.15009\n", "[40]\tvalidation_0-mae:0.15103\n", "[41]\tvalidation_0-mae:0.15227\n", "[42]\tvalidation_0-mae:0.15336\n", "[43]\tvalidation_0-mae:0.15266\n", "[44]\tvalidation_0-mae:0.15316\n", "[45]\tvalidation_0-mae:0.15164\n", "[46]\tvalidation_0-mae:0.15327\n", "[47]\tvalidation_0-mae:0.14973\n", "[48]\tvalidation_0-mae:0.14977\n", "[49]\tvalidation_0-mae:0.14931\n", "[50]\tvalidation_0-mae:0.14586\n", "[51]\tvalidation_0-mae:0.14249\n", "[52]\tvalidation_0-mae:0.14319\n", "[53]\tvalidation_0-mae:0.14222\n", "[54]\tvalidation_0-mae:0.14218\n", "[55]\tvalidation_0-mae:0.14325\n", "[56]\tvalidation_0-mae:0.14252\n", "[57]\tvalidation_0-mae:0.14351\n", "[58]\tvalidation_0-mae:0.14223\n", "[59]\tvalidation_0-mae:0.14138\n", "[60]\tvalidation_0-mae:0.14115\n", "[61]\tvalidation_0-mae:0.13953\n", "[62]\tvalidation_0-mae:0.13899\n", "[63]\tvalidation_0-mae:0.13860\n", "[64]\tvalidation_0-mae:0.13632\n", "[65]\tvalidation_0-mae:0.13570\n", "[66]\tvalidation_0-mae:0.13561\n", "[67]\tvalidation_0-mae:0.13511\n", "[68]\tvalidation_0-mae:0.13433\n", "[69]\tvalidation_0-mae:0.13406\n", "[70]\tvalidation_0-mae:0.13150\n", "[71]\tvalidation_0-mae:0.13149\n", "[72]\tvalidation_0-mae:0.13173\n", "[73]\tvalidation_0-mae:0.12573\n", "[74]\tvalidation_0-mae:0.12572\n", "[75]\tvalidation_0-mae:0.12507\n", "[76]\tvalidation_0-mae:0.12449\n", "[77]\tvalidation_0-mae:0.12451\n", "[78]\tvalidation_0-mae:0.12307\n", "[79]\tvalidation_0-mae:0.12279\n", "[80]\tvalidation_0-mae:0.12301\n", "[81]\tvalidation_0-mae:0.12250\n", "[82]\tvalidation_0-mae:0.12155\n", "[83]\tvalidation_0-mae:0.12064\n", "[84]\tvalidation_0-mae:0.12133\n", "[85]\tvalidation_0-mae:0.12129\n", "[86]\tvalidation_0-mae:0.11866\n", "[87]\tvalidation_0-mae:0.11950\n", "[88]\tvalidation_0-mae:0.11798\n", "[89]\tvalidation_0-mae:0.11779\n", "[90]\tvalidation_0-mae:0.11691\n", "[91]\tvalidation_0-mae:0.11669\n", "[92]\tvalidation_0-mae:0.11881\n", "[93]\tvalidation_0-mae:0.11965\n", "[94]\tvalidation_0-mae:0.12024\n", "[95]\tvalidation_0-mae:0.12000\n", "[96]\tvalidation_0-mae:0.11969\n", "[97]\tvalidation_0-mae:0.11961\n", "[98]\tvalidation_0-mae:0.11904\n", "[99]\tvalidation_0-mae:0.11983\n", "f1 score: 0.0\n", "acc score: 0.98\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Spoon\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Bears_27|Bears_28|Bears_29|Bears_30|Bears_31|Bears_32|Bears_33|Bears_34|Bears_35|Bears_36|Bears_37|Bears_38|Bears_39|Bears_40|Bears_41|Bears_42|Bears_43|Bears_44|Bears_45|Bears_46|Bears_47|Bears_48|Bears_49|Bears_50|Bears_51|Bears_52|Bears_53|Bears_54|Bears_55|Bears_56|Bears_57|Bears_58|Bears_59|Bears_60|Bears_61|Bears_62|Bears_63|Bears_64|Bears_65|Bears_66|Bears_67|Bears_68|Bears_69|Bears_70|Bears_71|Bears_72|Bears_73|Bears_74|Bears_75|Bears_76|Bears_77|Bears_78|Bears_79|Bears_80|Bears_81|Bears_82|Bears_83|Bears_215|Bears_216|Bears_217|Bears_218|Bears_219')  ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Bears_27|Bears_28|Bears_29|Bears_30|Bears_31|Bears_32|Bears_33|Bears_34|Bears_35|Bears_36|Bears_37|Bears_38|Bears_39|Bears_40|Bears_41|Bears_42|Bears_43|Bears_44|Bears_45|Bears_46|Bears_47|Bears_48|Bears_49|Bears_50|Bears_51|Bears_52|Bears_53|Bears_54|Bears_55|Bears_56|Bears_57|Bears_58|Bears_59|Bears_60|Bears_61|Bears_62|Bears_63|Bears_64|Bears_65|Bears_66|Bears_67|Bears_68|Bears_69|Bears_70|Bears_71|Bears_72|Bears_73|Bears_74|Bears_75|Bears_76|Bears_77|Bears_78|Bears_79|Bears_80|Bears_81|Bears_82|Bears_83|Bears_215|Bears_216|Bears_217|Bears_218|Bears_219') ]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10295, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1996, 204)\n", "====================\n", "Total test:  (7, 204)\n", "overfitting in test (0, 204)\n", "correct in test (7, 204)\n", "[0]\tvalidation_0-mae:0.49662\n", "Will train until validation_0-mae hasn't improved in 10 rounds.\n", "[1]\tvalidation_0-mae:0.49177\n", "[2]\tvalidation_0-mae:0.44640\n", "[3]\tvalidation_0-mae:0.40748\n", "[4]\tvalidation_0-mae:0.37393\n", "[5]\tvalidation_0-mae:0.34591\n", "[6]\tvalidation_0-mae:0.31696\n", "[7]\tvalidation_0-mae:0.34298\n", "[8]\tvalidation_0-mae:0.32214\n", "[9]\tvalidation_0-mae:0.31080\n", "[10]\tvalidation_0-mae:0.31569\n", "[11]\tvalidation_0-mae:0.31279\n", "[12]\tvalidation_0-mae:0.30907\n", "[13]\tvalidation_0-mae:0.31315\n", "[14]\tvalidation_0-mae:0.33032\n", "[15]\tvalidation_0-mae:0.31721\n", "[16]\tvalidation_0-mae:0.32542\n", "[17]\tvalidation_0-mae:0.33441\n", "[18]\tvalidation_0-mae:0.33066\n", "[19]\tvalidation_0-mae:0.32639\n", "[20]\tvalidation_0-mae:0.32243\n", "[21]\tvalidation_0-mae:0.33095\n", "[22]\tvalidation_0-mae:0.30275\n", "[23]\tvalidation_0-mae:0.29984\n", "[24]\tvalidation_0-mae:0.33304\n", "[25]\tvalidation_0-mae:0.33221\n", "[26]\tvalidation_0-mae:0.32004\n", "[27]\tvalidation_0-mae:0.29984\n", "[28]\tvalidation_0-mae:0.29209\n", "[29]\tvalidation_0-mae:0.29097\n", "[30]\tvalidation_0-mae:0.29255\n", "[31]\tvalidation_0-mae:0.28887\n", "[32]\tvalidation_0-mae:0.29037\n", "[33]\tvalidation_0-mae:0.30257\n", "[34]\tvalidation_0-mae:0.32414\n", "[35]\tvalidation_0-mae:0.32006\n", "[36]\tvalidation_0-mae:0.31798\n", "[37]\tvalidation_0-mae:0.32216\n", "[38]\tvalidation_0-mae:0.31335\n", "[39]\tvalidation_0-mae:0.31084\n", "[40]\tvalidation_0-mae:0.31511\n", "[41]\tvalidation_0-mae:0.31698\n", "Stopping. Best iteration:\n", "[31]\tvalidation_0-mae:0.28887\n", "\n", "f1 score: 0.0\n", "acc score: 0.7142857142857143\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# incubator\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Bears_149|Bears_150|Bears_151|Bears_152|Bears_153|Bears_154|Bears_195|Bears_196')  ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Bears_149|Bears_150|Bears_151|Bears_152|Bears_153|Bears_154|Bears_195|Bears_196') ]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=10, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10297, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1998, 204)\n", "====================\n", "Total test:  (5, 204)\n", "overfitting in test (0, 204)\n", "correct in test (5, 204)\n", "[0]\tvalidation_0-mae:0.42839\n", "Will train until validation_0-mae hasn't improved in 10 rounds.\n", "[1]\tvalidation_0-mae:0.37410\n", "[2]\tvalidation_0-mae:0.34547\n", "[3]\tvalidation_0-mae:0.32879\n", "[4]\tvalidation_0-mae:0.32538\n", "[5]\tvalidation_0-mae:0.33290\n", "[6]\tvalidation_0-mae:0.30290\n", "[7]\tvalidation_0-mae:0.27685\n", "[8]\tvalidation_0-mae:0.28681\n", "[9]\tvalidation_0-mae:0.29575\n", "[10]\tvalidation_0-mae:0.29169\n", "[11]\tvalidation_0-mae:0.28806\n", "[12]\tvalidation_0-mae:0.29637\n", "[13]\tvalidation_0-mae:0.31051\n", "[14]\tvalidation_0-mae:0.31554\n", "[15]\tvalidation_0-mae:0.29896\n", "[16]\tvalidation_0-mae:0.28593\n", "[17]\tvalidation_0-mae:0.28095\n", "Stopping. Best iteration:\n", "[7]\tvalidation_0-mae:0.27685\n", "\n", "f1 score: 1.0\n", "acc score: 1.0\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 due to no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n", "/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# fresco\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Bears_144|Bears_145|Bears_146|Bears_147|Bears_148')  ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Bears_144|Bears_145|Bears_146|Bears_147|Bears_148')  ]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=10, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total train:  (10297, 204)\n", "overfitting in train (8299, 204)\n", "correct in train (1998, 204)\n", "====================\n", "Total test:  (5, 204)\n", "overfitting in test (0, 204)\n", "correct in test (5, 204)\n", "[0]\tvalidation_0-mae:0.43331\n", "Will train until validation_0-mae hasn't improved in 30 rounds.\n", "[1]\tvalidation_0-mae:0.46985\n", "[2]\tvalidation_0-mae:0.40982\n", "[3]\tvalidation_0-mae:0.37225\n", "[4]\tvalidation_0-mae:0.35693\n", "[5]\tvalidation_0-mae:0.36239\n", "[6]\tvalidation_0-mae:0.36775\n", "[7]\tvalidation_0-mae:0.36969\n", "[8]\tvalidation_0-mae:0.37014\n", "[9]\tvalidation_0-mae:0.36029\n", "[10]\tvalidation_0-mae:0.35638\n", "[11]\tvalidation_0-mae:0.35536\n", "[12]\tvalidation_0-mae:0.33918\n", "[13]\tvalidation_0-mae:0.31952\n", "[14]\tvalidation_0-mae:0.32459\n", "[15]\tvalidation_0-mae:0.30053\n", "[16]\tvalidation_0-mae:0.28925\n", "[17]\tvalidation_0-mae:0.27429\n", "[18]\tvalidation_0-mae:0.27020\n", "[19]\tvalidation_0-mae:0.27218\n", "[20]\tvalidation_0-mae:0.27820\n", "[21]\tvalidation_0-mae:0.29231\n", "[22]\tvalidation_0-mae:0.28812\n", "[23]\tvalidation_0-mae:0.29539\n", "[24]\tvalidation_0-mae:0.29625\n", "[25]\tvalidation_0-mae:0.29479\n", "[26]\tvalidation_0-mae:0.29513\n", "[27]\tvalidation_0-mae:0.29601\n", "[28]\tvalidation_0-mae:0.30210\n", "[29]\tvalidation_0-mae:0.29740\n", "[30]\tvalidation_0-mae:0.28769\n", "[31]\tvalidation_0-mae:0.28415\n", "[32]\tvalidation_0-mae:0.28530\n", "[33]\tvalidation_0-mae:0.28423\n", "[34]\tvalidation_0-mae:0.28713\n", "[35]\tvalidation_0-mae:0.28690\n", "[36]\tvalidation_0-mae:0.28715\n", "[37]\tvalidation_0-mae:0.27857\n", "[38]\tvalidation_0-mae:0.27912\n", "[39]\tvalidation_0-mae:0.27868\n", "[40]\tvalidation_0-mae:0.27997\n", "[41]\tvalidation_0-mae:0.27682\n", "[42]\tvalidation_0-mae:0.28024\n", "[43]\tvalidation_0-mae:0.27925\n", "[44]\tvalidation_0-mae:0.28113\n", "[45]\tvalidation_0-mae:0.28095\n", "[46]\tvalidation_0-mae:0.28840\n", "[47]\tvalidation_0-mae:0.27983\n", "[48]\tvalidation_0-mae:0.28628\n", "Stopping. Best iteration:\n", "[18]\tvalidation_0-mae:0.27020\n", "\n", "f1 score: 0.0\n", "acc score: 0.8\n", "precision score: 0.0\n", "recall score: 0.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/sklearn/metrics/_classification.py:1221: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# molgenis\n", "test =  totaltraining[ totaltraining['id'].str.contains(r'Bears_170|Bears_171|Bears_172|Bears_173|Bears_174|Bears_175')   ]\n", "training= totaltraining[ ~ totaltraining['id'].str.contains(r'Bears_170|Bears_171|Bears_172|Bears_173|Bears_174|Bears_175')   ]\n", "#statistics about training samples\n", "overfittingintrain = training[training['label']==1]\n", "correctintrain=training[training['label']==0]\n", "print(\"Total train: \", training.shape)\n", "print(\"overfitting in train\",overfittingintrain.shape)\n", "print(\"correct in train\",correctintrain.shape)\n", "print(\"====================\")\n", "#statistics about test samples\n", "overfittingintest = test[test['label']==1]\n", "correctintest=test[test['label']==0]\n", "print(\"Total test: \",test.shape)\n", "print(\"overfitting in test\",overfittingintest.shape)\n", "print(\"correct in test\",correctintest.shape)\n", "\n", "\n", "numerical_features = training.select_dtypes(include=np.number).columns.tolist()\n", "Outliers_to_drop = detect_outliers(training,15,numerical_features)\n", "training.loc[Outliers_to_drop] # Show the outliers rows\n", "training = training.drop(Outliers_to_drop, axis = 0).reset_index(drop=True)\n", "\n", "\n", "# 202 features\n", "test_file_name = test.iloc[:,0]\n", "X_train = training.iloc[:,2:]\n", "Y_train = training.iloc[:,1]\n", "X_test = test.iloc[:,2:]\n", "Y_test = test.iloc[:,1]\n", "\n", "X_train = pd.get_dummies(X_train)\n", "X_test = pd.get_dummies(X_test)\n", "X_train,X_test=X_train.align(X_test,join='left',axis=1)\n", "X_train.head(2)\n", "X_test.head(2)\n", "headers = X_train.columns\n", "training.isnull().sum()\n", "from sklearn.impute import SimpleImputer\n", "imputer = SimpleImputer(missing_values = np.nan , strategy = 'most_frequent')\n", "imputer = imputer.fit(X_train)\n", "X_train = imputer.transform(X_train)\n", "X_test = imputer.transform(X_test)\n", "\n", "X_train = pd.DataFrame(X_train,columns=headers)\n", "X_test = pd.DataFrame(X_test,columns=headers)\n", "\n", "\n", "smote = SMOTE(random_state=42,sampling_strategy='minority')\n", "X_train, Y_train = smote.fit_sample(X_train, Y_train)\n", "\n", "\n", "X_train, Y_train = shuffle(X_train, Y_train, random_state=0)\n", "\n", "\n", "model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)\n", "eval_set=[(X_test,Y_test)]\n", "model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric=\"mae\", eval_set=eval_set)\n", "\n", "Y_pred = model.predict(X_test)\n", "cnf_matrix = confusion_matrix(Y_test,Y_pred)\n", "\n", "f1s = f1_score(Y_test, Y_pred, zero_division=1)\n", "print('f1 score:', f1s)\n", "acc = accuracy_score(Y_test, Y_pred)\n", "print('acc score:', acc)\n", "precisionsc = precision_score(Y_test, Y_pred)\n", "print('precision score:', precisionsc)\n", "recall = recall_score(Y_test, Y_pred)\n", "print('recall score:', recall)\n", "class_names = ['correct','overfitting']\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix\n", "                      , classes=class_names\n", "                      , title='Confusion matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.10"}}, "nbformat": 4, "nbformat_minor": 2}