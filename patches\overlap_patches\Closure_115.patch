--- /src/com/google/javascript/jscomp/FunctionInjector.java
+++ /src/com/google/javascript/jscomp/FunctionInjector.java
@@ -694,14 +694,6 @@
 
     Node block = fnNode.getLastChild();
 
-    boolean hasSideEffects = false;
-    if (block.hasChildren()) {
-      Preconditions.checkState(block.hasOneChild());
-      Node stmt = block.getFirstChild();
-      if (stmt.isReturn()) {
-        hasSideEffects = NodeUtil.mayHaveSideEffects(stmt.getFirstChild(), compiler);
-      }
-    }
     // CALL NODE: [ NAME, ARG1, ARG2, ... ]
     Node cArg = callNode.getFirstChild().getNext();
 
@@ -727,9 +719,6 @@
       // For each named parameter check if a mutable argument use more than one.
       if (fnParam != null) {
         if (cArg != null) {
-          if (hasSideEffects && NodeUtil.canBeSideEffected(cArg)) {
-            return CanInlineResult.NO;
-          }
           // Check for arguments that are evaluated more than once.
           // Note: Unlike block inlining, there it is not possible that a
           // parameter reference will be in a loop.

