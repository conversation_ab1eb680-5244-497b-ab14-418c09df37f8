--- /src/main/java/org/joda/time/tz/ZoneInfoCompiler.java
+++ /src/main/java/org/joda/time/tz/ZoneInfoCompiler.java
@@ -65,10 +65,11 @@
 
     static Chronology cLenientISO;
 
-    static ThreadLocal<Boolean> cVerbose = new ThreadLocal<Boolean>();
-    static {
-        cVerbose.set(Boolean.FALSE);
-    }
+    static ThreadLocal<Boolean> cVerbose = new ThreadLocal<Boolean>() {
+        protected Boolean initialValue() {
+            return Boolean.FALSE;
+        }
+    };
 
     /**
      * Gets a flag indicating that verbose logging is required.

