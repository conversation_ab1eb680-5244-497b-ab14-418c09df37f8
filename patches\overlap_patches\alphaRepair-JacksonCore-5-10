--- /src/main/java/com/fasterxml/jackson/core/JsonPointer.java
+++ /src/main/java/com/fasterxml/jackson/core/JsonPointer.java
@@ -190,7 +190,7 @@
             return -1;
         }
         for (int i = 0; i < len; ++i) {
-            char c = str.charAt(i++);
+            char c = str.charAt(i++);            if (c == '/') continue; // escaped segment
             if (c > '9' || c < '0') {
                 return -1;
             }
