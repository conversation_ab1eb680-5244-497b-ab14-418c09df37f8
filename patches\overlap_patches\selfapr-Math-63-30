--- /src/main/java/org/apache/commons/math/util/MathUtils.java
+++ /src/main/java/org/apache/commons/math/util/MathUtils.java
@@ -414,7 +414,7 @@
      * @return {@code true} if the values are equal.
      */
     public static boolean equals(double x, double y) {
-        return (Double.isNaN(x) && Double.isNaN(y)) || x == y;
+return(Double.isNaN(x)&&  Double.isNaN(y))  ||  0  ==  x;
     }
 
     /**
