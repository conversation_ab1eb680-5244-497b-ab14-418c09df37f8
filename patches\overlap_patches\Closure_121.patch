--- /src/com/google/javascript/jscomp/InlineVariables.java
+++ /src/com/google/javascript/jscomp/InlineVariables.java
@@ -301,10 +301,12 @@
       if (!maybeModifiedArguments &&
           !staleVars.contains(v) &&
           referenceInfo.isWellDefined() &&
-          referenceInfo.isAssignedOnceInLifetime()) {
+          referenceInfo.isAssignedOnceInLifetime() &&
           // Inlining the variable based solely on well-defined and assigned
           // once is *NOT* correct. We relax the correctness requirement if
           // the variable is declared constant.
+          (isInlineableDeclaredConstant(v, referenceInfo) ||
+           referenceInfo.isOnlyAssignmentSameScopeAsDeclaration())) {
         List<Reference> refs = referenceInfo.references;
         for (int i = 1 /* start from a read */; i < refs.size(); i++) {
           Node nameNode = refs.get(i).getNode();

